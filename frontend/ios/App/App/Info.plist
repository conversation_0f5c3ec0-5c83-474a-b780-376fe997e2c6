<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
        <string>KingBill</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>

	<!-- Bluetooth permissions for iOS -->
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>This app uses Bluetooth to connect to thermal printers for printing invoices and receipts.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>This app uses Bluetooth to connect to thermal printers for printing invoices and receipts.</string>
	
	<!-- Photo Library permissions for social sharing -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to share invoices and receipts.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app needs access to photo library to save invoices and receipts.</string>
	
	<!-- AirPrint support -->
	<key>UIPrintingPolicy</key>
	<dict>
		<key>UIPrintingPolicyAllowsAirPrint</key>
		<true/>
	</dict>
	
	<!-- Camera permissions (if needed for barcode scanning) -->
	<key>NSCameraUsageDescription</key>
	<string>This app uses camera to scan barcodes and QR codes for quick product entry.</string>
	
	<!-- Location permissions (if needed for delivery tracking) -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app uses location to track delivery routes and optimize logistics.</string>
	
	<!-- Microphone permissions (if needed for voice notes) -->
	<key>NSMicrophoneUsageDescription</key>
	<string>This app uses microphone to record voice notes for orders and deliveries.</string>
</dict>
</plist>
