import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.abi.hubapp',
  appName: 'KingBill',
  webDir: 'www',
  bundledWebRuntime: false,
  android: {
    appendUserAgent: 'KingBill',
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true
  },
  ios: {
    appendUserAgent: 'KingBill',
    allowsLinkPreview: false,
    handleApplicationNotifications: false
  },
  cordova: {
    preferences: {
      ScrollEnabled: 'false',
      'android-minSdkVersion': '22',
      'android-targetSdkVersion': '34',
      BackupWebStorage: 'none'
    }
  }
};

export default config;
