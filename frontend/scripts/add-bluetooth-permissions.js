#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const manifestPath = path.join(__dirname, '../android/app/src/main/AndroidManifest.xml');

// Bluetooth permissions to add
const bluetoothPermissions = [
    '    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />',
    '    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />',
    '    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />',
    '    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />'
];

function addBluetoothPermissions() {
    try {
        if (!fs.existsSync(manifestPath)) {
            console.log('AndroidManifest.xml not found');
            return;
        }

        let manifestContent = fs.readFileSync(manifestPath, 'utf8');
        
        // Check if Bluetooth permissions already exist
        if (manifestContent.includes('android.permission.BLUETOOTH_CONNECT')) {
            console.log('Bluetooth permissions already exist in AndroidManifest.xml');
            return;
        }

        // Find the closing </manifest> tag and add permissions before it
        const manifestEndTag = '</manifest>';
        const manifestEndIndex = manifestContent.lastIndexOf(manifestEndTag);
        
        if (manifestEndIndex === -1) {
            console.log('Could not find </manifest> tag');
            return;
        }

        // Add Bluetooth permissions before the closing manifest tag
        const permissionsBlock = '\n    <!-- Bluetooth permissions for Cordova Bluetooth Serial plugin -->\n' +
            bluetoothPermissions.join('\n') + '\n';
        
        const newManifestContent = manifestContent.slice(0, manifestEndIndex) + 
            permissionsBlock + 
            manifestContent.slice(manifestEndIndex);

        fs.writeFileSync(manifestPath, newManifestContent, 'utf8');
        console.log('✅ Bluetooth permissions added to AndroidManifest.xml');
        
    } catch (error) {
        console.error('❌ Error adding Bluetooth permissions:', error.message);
    }
}

// Run the function
addBluetoothPermissions();
