// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/theming/

/** Ionic CSS Variables **/
:root {
  /** primary **/
  --ion-color-primary: #3880ff;
  --ion-color-primary-rgb: 56, 128, 255;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #3171e0;
  --ion-color-primary-tint: #4c8dff;

  /** secondary **/
  --ion-color-secondary: #3dc2ff;
  --ion-color-secondary-rgb: 61, 194, 255;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #36abe0;
  --ion-color-secondary-tint: #50c8ff;

  /** tertiary **/
  --ion-color-tertiary: #5260ff;
  --ion-color-tertiary-rgb: 82, 96, 255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #4854e0;
  --ion-color-tertiary-tint: #6370ff;

  /** success **/
  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45, 211, 111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  /** warning **/
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  /** danger **/
  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb: 235, 68, 90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;

  /** dark **/
  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  /** medium **/
  --ion-color-medium: #92949c;
  --ion-color-medium-rgb: 146, 148, 156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;

  /** light **/
  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 245, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  /** Map custom background variables to Ionic built-ins for consistency **/
  --app-background: var(--ion-background-color);
  --card-background: var(--ion-card-background);
  --section-background: var(--ion-card-background);
  --item-background: var(--ion-item-background);
  --header-background: var(--ion-toolbar-background);
  --content-background: var(--ion-background-color);
  --modal-background: var(--ion-card-background);
  --form-background: var(--ion-card-background);

  /* Custom App Colors */
  --app-color-blue-dark: #2c3e50;
  --app-color-grey-light: #f8f9fa;
  --app-color-grey-medium: #6c757d;
  --app-color-purple: #6f42c1;
  --app-color-primary-blue: #007bff;
  --app-color-green: #28a745;
  --app-color-yellow: #ffc107;
  --app-color-red: #dc3545;
  --app-color-grey-border: #dee2e6;
  --app-color-grey-bg: #e9ecef;
  --app-color-grey-dark: #495057;
  --app-color-grey-lighter: #f5f5f5;
  --app-color-grey-lightest: #ffffff;
  --app-color-grey-very-light: #e0e0e0;
  --app-color-grey-xlight: #f0f0f0;
  --app-color-grey-xxlight: #f3e5f5;
  --app-color-grey-xxxlight: #faf2fb;
  --app-color-blue-gradient1: #e3f2fd;
  --app-color-blue-gradient2: #f1f8ff;
  --app-color-green-gradient1: #e8f5e8;
  --app-color-green-gradient2: #f0f9f0;
  --app-color-orange: #ff9800;
  --app-color-orange-bg: #fff3e0;
  --app-color-red-bg: #ffebee;
  --app-color-red-light: #f44336;
  --app-color-blue-light: #2196f3;
  --app-color-purple-gradient1: #667eea;
  --app-color-purple-gradient2: #764ba2;
  --app-color-green-bg: #f0fff4;
  --app-color-green-bg2: #e6fffa;
  --app-color-grey-placeholder: #999;
  --app-color-grey-verydark: #333;
  --app-color-grey-dark2: #666;
  --app-color-grey-border2: #ddd;
  --app-color-grey-border3: #dddddd;
  --app-color-grey-border4: #ccc;
  --app-color-grey-border5: #adb5bd;
  --app-color-grey-bg3: #fef9e7;
  --app-color-grey-bg4: #fff3cd;
  --app-color-grey-bg5: #f3e5f5;
  --app-color-grey-bg6: #e3f2fd;
  --app-color-grey-bg7: #f1f8ff;
  --app-color-grey-bg8: #f0f8ff;
  --app-color-grey-bg9: #f8f9fa;
  --app-color-grey-bg10: #f5f5f5;
  --app-color-grey-bg11: #e0e0e0;
  --app-color-grey-bg12: #f0f0f0;
  --app-color-grey-bg13: #e9ecef;
  --app-color-grey-bg14: #fef5f5;
  --app-color-grey-bg15: #ffebee;
  --app-color-grey-bg16: #fff3e0;
  --app-color-grey-bg17: #e3f2fd;
  --app-color-grey-bg18: #f1f8ff;
  --app-color-grey-bg19: #f0fff4;
  --app-color-grey-bg20: #e6fffa;
  --app-color-grey-bg21: #f3e5f5;
  --app-color-grey-bg22: #faf2fb;
  --app-color-grey-bg23: #e8f5e8;
  --app-color-grey-bg24: #f0f9f0;
  --app-color-grey-bg25: #fff3cd;
  --app-color-grey-bg26: #fef9e7;
  --app-color-grey-bg27: #e9ecef;
  --app-color-grey-bg28: #f8f9fa;
  --app-color-grey-bg29: #e0e0e0;
  --app-color-grey-bg30: #f5f5f5;
  --app-color-grey-bg31: #e0e0e0;
  --app-color-grey-bg32: #f0f0f0;
  --app-color-grey-bg33: #e9ecef;
  --app-color-grey-bg34: #f3e5f5;
  --app-color-grey-bg35: #faf2fb;
  --app-color-grey-bg36: #e3f2fd;
  --app-color-grey-bg37: #f1f8ff;
  --app-color-grey-bg38: #e8f5e8;
  --app-color-grey-bg39: #f0f9f0;
  --app-color-grey-bg40: #fff3cd;
  --app-color-grey-bg41: #fef9e7;
  --app-color-grey-bg42: #e9ecef;
  --app-color-grey-bg43: #f8f9fa;
  --app-color-grey-bg44: #e0e0e0;
  --app-color-grey-bg45: #f5f5f5;
  --app-color-grey-bg46: #e0e0e0;
  --app-color-grey-bg47: #f0f0f0;
  --app-color-grey-bg48: #e9ecef;
  --app-color-grey-bg49: #f3e5f5;
  --app-color-grey-bg50: #faf2fb;
  --app-color-grey-bg51: #e3f2fd;
  --app-color-grey-bg52: #f1f8ff;
  --app-color-grey-bg53: #e8f5e8;
  --app-color-grey-bg54: #f0f9f0;
  --app-color-grey-bg55: #fff3cd;
  --app-color-grey-bg56: #fef9e7;
  --app-color-grey-bg57: #e9ecef;
  --app-color-grey-bg58: #f8f9fa;
  --app-color-grey-bg59: #e0e0e0;
  --app-color-grey-bg60: #f5f5f5;
  --app-color-grey-bg61: #e0e0e0;
  --app-color-grey-bg62: #f0f0f0;
  --app-color-grey-bg63: #e9ecef;
  --app-color-grey-bg64: #f3e5f5;
  --app-color-grey-bg65: #faf2fb;
  --app-color-grey-bg66: #e3f2fd;
  --app-color-grey-bg67: #f1f8ff;
  --app-color-grey-bg68: #e8f5e8;
  --app-color-grey-bg69: #f0f9f0;
  --app-color-grey-bg70: #fff3cd;
  --app-color-grey-bg71: #fef9e7;
  --app-color-grey-bg72: #e9ecef;
  --app-color-grey-bg73: #f8f9fa;
  --app-color-grey-bg74: #e0e0e0;
  --app-color-grey-bg75: #f5f5f5;
  --app-color-grey-bg76: #e0e0e0;
  --app-color-grey-bg77: #f0f0f0;
  --app-color-grey-bg78: #e9ecef;
  --app-color-grey-bg79: #f3e5f5;
  --app-color-grey-bg80: #faf2fb;
  --app-color-grey-bg81: #e3f2fd;
  --app-color-grey-bg82: #f1f8ff;
  --app-color-grey-bg83: #e8f5e8;
  --app-color-grey-bg84: #f0f9f0;
  --app-color-grey-bg85: #fff3cd;
  --app-color-grey-bg86: #fef9e7;
  --app-color-grey-bg87: #e9ecef;
  --app-color-grey-bg88: #f8f9fa;
  --app-color-grey-bg89: #e0e0e0;
  --app-color-grey-bg90: #f5f5f5;
  --app-color-grey-bg91: #e0e0e0;
  --app-color-grey-bg92: #f0f0f0;
  --app-color-grey-bg93: #e9ecef;
  --app-color-grey-bg94: #f3e5f5;
  --app-color-grey-bg95: #faf2fb;
  --app-color-grey-bg96: #e3f2fd;
  --app-color-grey-bg97: #f1f8ff;
  --app-color-grey-bg98: #e8f5e8;
  --app-color-grey-bg99: #f0f9f0;
  --app-color-grey-bg100: #fff3cd;
  --app-color-grey-bg101: #fef9e7;
  --app-color-grey-bg102: #e9ecef;
  --app-color-grey-bg103: #f8f9fa;
  --app-color-grey-bg104: #e0e0e0;
  --app-color-grey-bg105: #f5f5f5;
  --app-color-grey-bg106: #e0e0e0;
  --app-color-grey-bg107: #f0f0f0;
  --app-color-grey-bg108: #e9ecef;
  --app-color-grey-bg109: #f3e5f5;
  --app-color-grey-bg110: #faf2fb;
  --app-color-grey-bg111: #e3f2fd;
  --app-color-grey-bg112: #f1f8ff;
  --app-color-grey-bg113: #e8f5e8;
  --app-color-grey-bg114: #f0f9f0;
  --app-color-grey-bg115: #fff3cd;
  --app-color-grey-bg116: #fef9e7;
  --app-color-grey-bg117: #e9ecef;
  --app-color-grey-bg118: #f8f9fa;
  --app-color-grey-bg119: #e0e0e0;
  --app-color-grey-bg120: #f5f5f5;
  --app-color-grey-bg121: #e0e0e0;
  --app-color-grey-bg122: #f0f0f0;
  --app-color-grey-bg123: #e9ecef;
  --app-color-grey-bg124: #f3e5f5;
  --app-color-grey-bg125: #faf2fb;
  --app-color-grey-bg126: #e3f2fd;
  --app-color-grey-bg127: #f1f8ff;
  --app-color-grey-bg128: #e8f5e8;
  --app-color-grey-bg129: #f0f9f0;
  --app-color-grey-bg130: #fff3cd;
  --app-color-grey-bg131: #fef9e7;
  --app-color-grey-bg132: #e9ecef;
  --app-color-grey-bg133: #f8f9fa;
  --app-color-grey-bg134: #e0e0e0;
  --app-color-grey-bg135: #f5f5f5;
  --app-color-grey-bg136: #e0e0e0;
  --app-color-grey-bg137: #f0f0f0;
  --app-color-grey-bg138: #e9ecef;
  --app-color-grey-bg139: #f3e5f5;
  --app-color-grey-bg140: #faf2fb;
  --app-color-grey-bg141: #e3f2fd;
  --app-color-grey-bg142: #f1f8ff;
  --app-color-grey-bg143: #e8f5e8;
  --app-color-grey-bg144: #f0f9f0;
  --app-color-grey-bg145: #fff3cd;
  --app-color-grey-bg146: #fef9e7;
  --app-color-grey-bg147: #e9ecef;
  --app-color-grey-bg148: #f8f9fa;
  --app-color-grey-bg149: #e0e0e0;
  --app-color-grey-bg150: #f5f5f5;
  --app-color-grey-bg151: #e0e0e0;
  --app-color-grey-bg152: #f0f0f0;
  --app-color-grey-bg153: #e9ecef;
  --app-color-grey-bg154: #f3e5f5;
  --app-color-grey-bg155: #faf2fb;
  --app-color-grey-bg156: #e3f2fd;
  --app-color-grey-bg157: #f1f8ff;
  --app-color-grey-bg158: #e8f5e8;
  --app-color-grey-bg159: #f0f9f0;
  --app-color-grey-bg160: #fff3cd;
  --app-color-grey-bg161: #fef9e7;
  --app-color-grey-bg162: #e9ecef;
  --app-color-grey-bg163: #f8f9fa;
  --app-color-grey-bg164: #e0e0e0;
  --app-color-grey-bg165: #f5f5f5;
  --app-color-grey-bg166: #e0e0e0;
  --app-color-grey-bg167: #f0f0f0;
  --app-color-grey-bg168: #e9ecef;
  --app-color-grey-bg169: #f3e5f5;
  --app-color-grey-bg170: #faf2fb;
  --app-color-grey-bg171: #e3f2fd;
  --app-color-grey-bg172: #f1f8ff;
  --app-color-grey-bg173: #e8f5e8;
  --app-color-grey-bg174: #f0f9f0;
  --app-color-grey-bg175: #fff3cd;
  --app-color-grey-bg176: #fef9e7;
  --app-color-grey-bg177: #e9ecef;
  --app-color-grey-bg178: #f8f9fa;
  --app-color-grey-bg179: #e0e0e0;
  --app-color-grey-bg180: #f5f5f5;
  --app-color-grey-bg181: #e0e0e0;
  --app-color-grey-bg182: #f0f0f0;
  --app-color-grey-bg183: #e9ecef;
  --app-color-grey-bg184: #f3e5f5;
  --app-color-grey-bg185: #faf2fb;
  --app-color-grey-bg186: #e3f2fd;
  --app-color-grey-bg187: #f1f8ff;
  --app-color-grey-bg188: #e8f5e8;
  --app-color-grey-bg189: #f0f9f0;
  --app-color-grey-bg190: #fff3cd;
  --app-color-grey-bg191: #fef9e7;
  --app-color-grey-bg192: #e9ecef;
  --app-color-grey-bg193: #f8f9fa;
  --app-color-grey-bg194: #e0e0e0;
  --app-color-grey-bg195: #f5f5f5;
  --app-color-grey-bg196: #e0e0e0;
  --app-color-grey-bg197: #f0f0f0;
  --app-color-grey-bg198: #e9ecef;
  --app-color-grey-bg199: #f3e5f5;
  --app-color-grey-bg200: #faf2fb;
}

/*
 * Force Light Theme - Override any system dark mode preferences
 * This ensures the app always uses light theme regardless of device settings
 */
@media (prefers-color-scheme: dark) {
  :root {
    /* Override all dark theme variables to force light theme */
    --ion-background-color: #ffffff;
    --ion-background-color-rgb: 255, 255, 255;
    --ion-text-color: #000000;
    --ion-text-color-rgb: 0, 0, 0;
    --ion-border-color: #c1c4cd;
    --ion-item-background: #ffffff;
    --ion-toolbar-background: #ffffff;
    --ion-tab-bar-background: #ffffff;
    --ion-card-background: #ffffff;
    
    /* Force light theme color steps */
    --ion-color-step-50: #f2f2f2;
    --ion-color-step-100: #e6e6e6;
    --ion-color-step-150: #d9d9d9;
    --ion-color-step-200: #cccccc;
    --ion-color-step-250: #bfbfbf;
    --ion-color-step-300: #b3b3b3;
    --ion-color-step-350: #a6a6a6;
    --ion-color-step-400: #999999;
    --ion-color-step-450: #8c8c8c;
    --ion-color-step-500: #808080;
    --ion-color-step-550: #737373;
    --ion-color-step-600: #666666;
    --ion-color-step-650: #595959;
    --ion-color-step-700: #4d4d4d;
    --ion-color-step-750: #404040;
    --ion-color-step-800: #333333;
    --ion-color-step-850: #262626;
    --ion-color-step-900: #191919;
    --ion-color-step-950: #0d0d0d;
  }

  body {
    /* Force light theme on body */
    --ion-background-color: #ffffff;
    --ion-background-color-rgb: 255, 255, 255;
    --ion-text-color: #000000;
    --ion-text-color-rgb: 0, 0, 0;
    --ion-border-color: #c1c4cd;
    --ion-item-background: #ffffff;
    --ion-toolbar-background: #ffffff;
    --ion-tab-bar-background: #ffffff;
    --ion-card-background: #ffffff;
    
    /* Force app background colors to light theme */
    --app-background: #ffffff;
    --card-background: #ffffff;
    --section-background: #ffffff;
    --item-background: #ffffff;
    --header-background: #ffffff;
    --content-background: #ffffff;
    --modal-background: #ffffff;
    --form-background: #ffffff;
  }

  /* Force light theme for iOS */
  .ios body {
    --ion-background-color: #ffffff;
    --ion-background-color-rgb: 255, 255, 255;
    --ion-text-color: #000000;
    --ion-text-color-rgb: 0, 0, 0;
    --ion-item-background: #ffffff;
    --ion-card-background: #ffffff;
    --app-background: #ffffff;
    --card-background: #ffffff;
    --section-background: #ffffff;
    --item-background: #ffffff;
    --header-background: #ffffff;
    --content-background: #ffffff;
    --modal-background: #ffffff;
    --form-background: #ffffff;
  }

  /* Force light theme for Material Design */
  .md body {
    --ion-background-color: #ffffff;
    --ion-background-color-rgb: 255, 255, 255;
    --ion-text-color: #000000;
    --ion-text-color-rgb: 0, 0, 0;
    --ion-border-color: #c1c4cd;
    --ion-item-background: #ffffff;
    --ion-toolbar-background: #ffffff;
    --ion-tab-bar-background: #ffffff;
    --ion-card-background: #ffffff;
    --app-background: #ffffff;
    --card-background: #ffffff;
    --section-background: #ffffff;
    --item-background: #ffffff;
    --header-background: #ffffff;
    --content-background: #ffffff;
    --modal-background: #ffffff;
    --form-background: #ffffff;
  }
}

/* Additional force light theme for any remaining dark mode scenarios */
html[data-theme="dark"],
html[data-theme="dark"] body,
body[data-theme="dark"],
.dark,
.dark body,
[data-theme="dark"] {
  --ion-background-color: #ffffff !important;
  --ion-background-color-rgb: 255, 255, 255 !important;
  --ion-text-color: #000000 !important;
  --ion-text-color-rgb: 0, 0, 0 !important;
  --ion-border-color: #c1c4cd !important;
  --ion-item-background: #ffffff !important;
  --ion-toolbar-background: #ffffff !important;
  --ion-tab-bar-background: #ffffff !important;
  --ion-card-background: #ffffff !important;
  --app-background: #ffffff !important;
  --card-background: #ffffff !important;
  --section-background: #ffffff !important;
  --item-background: #ffffff !important;
  --header-background: #ffffff !important;
  --content-background: #ffffff !important;
  --modal-background: #ffffff !important;
  --form-background: #ffffff !important;
}
