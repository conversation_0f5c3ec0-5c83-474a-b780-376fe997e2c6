
declare global {
    interface Navigator {
        bluetooth: {
            requestDevice(options: RequestDeviceOptions): Promise<BluetoothDevice>;
        };
    }

    interface BluetoothDevice {
        gatt: BluetoothRemoteGATTServer;
        name?: string;
        id: string;
        // Add other properties you use
    }

    interface BluetoothRemoteGATTServer {
        connect(): Promise<BluetoothRemoteGATTServer>;
        disconnect(): void;
        getPrimaryService(service: BluetoothServiceUUID): Promise<BluetoothRemoteGATTService>;
    }

    interface BluetoothRemoteGATTService {
        getCharacteristic(characteristic: BluetoothCharacteristicUUID): Promise<BluetoothRemoteGATTCharacteristic>;
    }

    interface BluetoothRemoteGATTCharacteristic {
        readValue(): Promise<DataView>;
        writeValue(data: BufferSource): Promise<void>;
        // Add other methods you use
    }

    interface BluetoothRemoteGATTCharacteristic {
        readValue(): Promise<DataView>;
        writeValue(value: BufferSource): Promise<void>;
        startNotifications(): Promise<BluetoothRemoteGATTCharacteristic>;
        stopNotifications(): Promise<BluetoothRemoteGATTCharacteristic>;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
    }

    type RequestDeviceOptions = {
        acceptAllDevices?: boolean;
        filters?: BluetoothRequestDeviceFilter[];
        optionalServices?: BluetoothServiceUUID[];
    };

    type BluetoothRequestDeviceFilter = {
        services?: BluetoothServiceUUID[];
        name?: string;
        namePrefix?: string;
    };

    type BluetoothServiceUUID = string | number;
    type BluetoothCharacteristicUUID = string | number;
    // Add more types as needed

    interface BluetoothRemoteGATTServiceWithUUID extends BluetoothRemoteGATTService {
        uuid: string;
    }
}

export { };