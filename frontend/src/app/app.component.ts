import { Component } from '@angular/core';
import { AuthenticationService } from './shared/services/authentication.service';

import { TranslationService } from './shared/services/translation.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent {
  constructor(
    private authService: AuthenticationService,

    private translationService: TranslationService
  ) {}

  ngOnInit(): void {
    // Theme service will automatically initialize
    // Translation service will automatically initialize and apply language from metadata
  }
}
