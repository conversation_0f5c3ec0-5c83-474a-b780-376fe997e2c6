import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON><PERSON>hild,
  <PERSON>ement<PERSON>ef,
  ViewChildren,
  <PERSON><PERSON><PERSON>ist,
  OnDestroy
} from "@angular/core";
import { Platform, NavController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { RetailerClassService } from '../shared/services/retailer-class.service';
import { RouteService } from '../shared/services/route.service';
import { ToastService } from '../shared/services/toast.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-retailer-class-mgmt',
  templateUrl: './retailer-class-mgmt.page.html',
  styleUrls: ['./retailer-class-mgmt.page.scss'],
})
export class RetailerClassMgmtPage implements OnInit {
  @ViewChildren("inputCrates") inputCrates: QueryList<ElementRef>;

  // Original properties
  editId: any;
  data: any = [];
  margin: string = 'Margin';
  i: number = 0;

  // Enhanced UI properties
  searchTerm: string = '';
  selectedBrand: string = '';
  filteredData: any = [];
  uniqueBrands: string[] = [];
  originalData: any = []; // Store original data for reset functionality

  // Modal states
  isBulkModalOpen: boolean = false;
  isImportExportModalOpen: boolean = false;

  // Bulk action properties
  bulkMarginValue: number | null = null;
  percentageChange: number | null = null;
  selectedBrandForBulk: string = '';
  brandMarginValue: number | null = null;
  filteredMarginValue: number | null = null;

  constructor(
    private api: RetailerClassService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public nav: NavController,
    public routerService: RouteService,
    private route: ActivatedRoute,

  ) {
    this.route.queryParams.subscribe((params) => {
      if (params && params.id) {
        this.editId = JSON.parse(params.id);
      }
    });
  }

  ngOnInit() {
  }
  ionViewWillEnter() {
    console.log(this.editId);
    this.getData()
  }

  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getBuyerClassMargin(this.editId)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.data = res.data.map(item => ({
              ...item,
              isModified: false,
              originalMargin: item.final_margin
            }));
            this.originalData = JSON.parse(JSON.stringify(this.data)); // Deep copy
            this.extractUniqueBrands();
            this.filterProducts();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  // Extract unique brands from data
  extractUniqueBrands() {
    const brands = this.data.map(item => item.brand).filter(brand => brand);
    this.uniqueBrands = [...new Set(brands)].sort();
  }

  // Filter products based on search term and selected brand
  filterProducts() {
    this.filteredData = this.data.filter(item => {
      const matchesSearch = !this.searchTerm ||
        item.short_code?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.brand?.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesBrand = !this.selectedBrand || item.brand === this.selectedBrand;

      return matchesSearch && matchesBrand;
    });
  }

  numberOnlyValidation(event: any) {
    const pattern = /[0-9.]/;
    let inputChar = String.fromCharCode(event.charCode);
  
    // Prevent multiple dots
    if (inputChar === '.' && event.target.value.includes('.')) {
      event.preventDefault();
      return;
    }
  
    if (!pattern.test(inputChar)) {
      // Invalid character, prevent input
      event.preventDefault();
    }
  }
  changeFocusOnCrate(i) {
    let idxNext = i + 1; // Get index by class name
    if (idxNext) {
      let idx = idxNext == this.data.length ? 0 : idxNext;
      this.inputCrates.toArray()[idx]["el"].setFocus();
    }
  }

  focusNextInput(currentIndex: number) {
    const nextIndex = currentIndex + 1;
    if (nextIndex < this.filteredData.length && this.inputCrates) {
      const nextInput = this.inputCrates.toArray()[nextIndex];
      if (nextInput) {
        nextInput.nativeElement.setFocus();
      }
    }
  }

  modifyMargin(data, pd) {
    const originalValue = pd.originalMargin || pd.margin;
    pd.final_margin = data;
    pd.isModified = pd.final_margin !== originalValue;
  }

  // Utility methods for enhanced UI
  getModifiedCount(): number {
    return this.data.filter(item => item.isModified).length;
  }

  getAverageMargin(): string {
    if (this.filteredData.length === 0) return '0.0';
    const total = this.filteredData.reduce((sum, item) => sum + (item.final_margin || 0), 0);
    return (total / this.filteredData.length).toFixed(1);
  }

  hasUnsavedChanges(): boolean {
    return this.getModifiedCount() > 0;
  }

  resetMargin(pd: any) {
    pd.final_margin = pd.originalMargin || pd.margin;
    pd.isModified = false;
  }

  resetAllChanges() {
    this.alertService.alertConfirm(
      'Reset All Changes',
      'Are you sure you want to reset all margin changes? This action cannot be undone.',
      'Yes, Reset',
      'Cancel'
    ).then((confirmed) => {
      if (confirmed) {
        this.data.forEach(item => {
          item.final_margin = item.originalMargin || item.margin;
          item.isModified = false;
        });
        this.filterProducts();
        this.toast.toastServices('All changes have been reset', 'success', 'top');
      }
    });
  }

  copyMarginToSimilar(pd: any) {
    if (!pd.brand) {
      this.toast.toastServices('Cannot copy margin - product has no brand', 'warning', 'top');
      return;
    }

    this.alertService.alertConfirm(
      'Copy Margin',
      `Copy margin ${pd.final_margin}% to all products of brand "${pd.brand}"?`,
      'Yes, Copy',
      'Cancel'
    ).then((confirmed) => {
      if (confirmed) {
        let copiedCount = 0;
        this.data.forEach(item => {
          if (item.brand === pd.brand && item.id !== pd.id) {
            const originalValue = item.originalMargin || item.margin;
            item.final_margin = pd.final_margin;
            item.isModified = item.final_margin !== originalValue;
            copiedCount++;
          }
        });
        this.filterProducts();
        this.toast.toastServices(`Margin copied to ${copiedCount} products`, 'success', 'top');
      }
    });
  }
 async save(){
    // console.table(this.data);
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .postBuyerClassMargin(this.editId,this.data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.getData();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
    
  }
  back(){
    this.routerService.routerFunction('retailer-class')
  }

  // Modal methods
  openBulkActionsModal() {
    this.isBulkModalOpen = true;
  }

  closeBulkActionsModal() {
    this.isBulkModalOpen = false;
    // Reset bulk action values
    this.bulkMarginValue = null;
    this.percentageChange = null;
    this.selectedBrandForBulk = '';
    this.brandMarginValue = null;
    this.filteredMarginValue = null;
  }

  openImportExportModal() {
    this.isImportExportModalOpen = true;
  }

  closeImportExportModal() {
    this.isImportExportModalOpen = false;
  }

  onImportComplete() {
    this.closeImportExportModal();
    this.getData(); // Refresh data after import
    this.toast.toastServices('Import completed successfully', 'success', 'top');
  }

  // Bulk action methods
  applyMarginToAll() {
    if (!this.bulkMarginValue) return;

    this.alertService.alertConfirm(
      'Apply to All',
      `Set margin to ${this.bulkMarginValue}% for all ${this.data.length} products?`,
      'Yes, Apply',
      'Cancel'
    ).then((confirmed) => {
      if (confirmed) {
        this.data.forEach(item => {
          const originalValue = item.originalMargin || item.margin;
          item.final_margin = this.bulkMarginValue;
          item.isModified = item.final_margin !== originalValue;
        });
        this.filterProducts();
        this.toast.toastServices(`Margin applied to all products`, 'success', 'top');
        this.closeBulkActionsModal();
      }
    });
  }

  applyPercentageChange() {
    if (!this.percentageChange) return;

    this.alertService.alertConfirm(
      'Apply Percentage Change',
      `${this.percentageChange > 0 ? 'Increase' : 'Decrease'} all margins by ${Math.abs(this.percentageChange)}%?`,
      'Yes, Apply',
      'Cancel'
    ).then((confirmed) => {
      if (confirmed) {
        this.data.forEach(item => {
          const currentMargin = item.final_margin || 0;
          const newMargin = currentMargin + (currentMargin * this.percentageChange / 100);
          const originalValue = item.originalMargin || item.margin;
          item.final_margin = Math.max(0, Math.round(newMargin * 100) / 100); // Round to 2 decimal places, min 0
          item.isModified = item.final_margin !== originalValue;
        });
        this.filterProducts();
        this.toast.toastServices(`Percentage change applied to all products`, 'success', 'top');
        this.closeBulkActionsModal();
      }
    });
  }

  applyMarginToBrand() {
    if (!this.selectedBrandForBulk || !this.brandMarginValue) return;

    const brandProducts = this.data.filter(item => item.brand === this.selectedBrandForBulk);

    this.alertService.alertConfirm(
      'Apply to Brand',
      `Set margin to ${this.brandMarginValue}% for all ${brandProducts.length} products of brand "${this.selectedBrandForBulk}"?`,
      'Yes, Apply',
      'Cancel'
    ).then((confirmed) => {
      if (confirmed) {
        let appliedCount = 0;
        this.data.forEach(item => {
          if (item.brand === this.selectedBrandForBulk) {
            const originalValue = item.originalMargin || item.margin;
            item.final_margin = this.brandMarginValue;
            item.isModified = item.final_margin !== originalValue;
            appliedCount++;
          }
        });
        this.filterProducts();
        this.toast.toastServices(`Margin applied to ${appliedCount} products`, 'success', 'top');
        this.closeBulkActionsModal();
      }
    });
  }

  applyMarginToFiltered() {
    if (!this.filteredMarginValue || this.filteredData.length === 0) return;

    this.alertService.alertConfirm(
      'Apply to Filtered',
      `Set margin to ${this.filteredMarginValue}% for all ${this.filteredData.length} currently visible products?`,
      'Yes, Apply',
      'Cancel'
    ).then((confirmed) => {
      if (confirmed) {
        this.filteredData.forEach(item => {
          const originalValue = item.originalMargin || item.margin;
          item.final_margin = this.filteredMarginValue;
          item.isModified = item.final_margin !== originalValue;
        });
        this.toast.toastServices(`Margin applied to ${this.filteredData.length} products`, 'success', 'top');
        this.closeBulkActionsModal();
      }
    });
  }
}