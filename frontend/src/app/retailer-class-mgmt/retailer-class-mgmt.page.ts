import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  ViewChildren,
  QueryList,
  OnDestroy
} from "@angular/core";
import { Platform, NavController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { RetailerClassService } from '../shared/services/retailer-class.service';
import { RouteService } from '../shared/services/route.service';
import { ToastService } from '../shared/services/toast.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-retailer-class-mgmt',
  templateUrl: './retailer-class-mgmt.page.html',
  styleUrls: ['./retailer-class-mgmt.page.scss'],
})
export class RetailerClassMgmtPage implements OnInit {
  @ViewChildren("inputCrates") inputCrates: QueryList<ElementRef>;

  editId: any;
  data: any;
  margin: string = 'Margin';
  i: number = 0;

  constructor(
    private api: RetailerClassService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public nav: NavController,
    public routerService: RouteService,
    private route: ActivatedRoute,

  ) {
    this.route.queryParams.subscribe((params) => {
      if (params && params.id) {
        this.editId = JSON.parse(params.id);
      }
    });
  }

  ngOnInit() {
  }
  ionViewWillEnter() {
    console.log(this.editId);
    this.getData()
  }

  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getBuyerClassMargin(this.editId)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.data = res.data;
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  numberOnlyValidation(event: any) {
    const pattern = /[0-9.]/;
    let inputChar = String.fromCharCode(event.charCode);
  
    // Prevent multiple dots
    if (inputChar === '.' && event.target.value.includes('.')) {
      event.preventDefault();
      return;
    }
  
    if (!pattern.test(inputChar)) {
      // Invalid character, prevent input
      event.preventDefault();
    }
  }
  changeFocusOnCrate(i) {
    let idxNext = i + 1; // Get index by class name
    if (idxNext) {
      let idx = idxNext == this.data.length ? 0 : idxNext;
      this.inputCrates.toArray()[idx]["el"].setFocus();
    }
  }

  modifyMargin(data, pd) {
    pd.final_margin = data;
  }
 async save(){
    // console.table(this.data);
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .postBuyerClassMargin(this.editId,this.data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.getData();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
    
  }
  back(){
    this.routerService.routerFunction('retailer-class')
  }
}