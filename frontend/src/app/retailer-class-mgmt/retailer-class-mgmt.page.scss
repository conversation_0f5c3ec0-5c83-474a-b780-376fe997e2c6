// Enhanced Header Styles
.enhanced-header {
  padding: 16px;
  background: var(--ion-color-light-tint);
}

.search-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-content {
  padding: 20px;
}

.search-section {
  margin-bottom: 16px;
}

.custom-searchbar {
  --background: var(--ion-color-light);
  --border-radius: 8px;
  --box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.actions-row {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.brand-filter {
  flex: 1;
  min-width: 150px;
  --background: white;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
}

.bulk-action-btn, .import-export-btn {
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  height: 40px;
}

.stats-row {
  display: flex;
  gap: 20px;
  padding: 12px 0;
  border-top: 1px solid var(--ion-color-light);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 80px;
}

.stat-label {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-primary);
}

// Products Container Styles
.products-container {
  padding: 0 16px 16px;
}

.products-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.products-header {
  background: var(--ion-color-primary);
  color: white;
  padding: 16px 20px;
  margin: 0;
}

.header-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-col {
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-col {
  flex: 2;
  justify-content: flex-start;
}

.margin-col {
  flex: 1;
}

.actions-col {
  flex: 1;
}

.header-label {
  font-weight: 600;
  font-size: 14px;
}

.products-content {
  padding: 0;
}

.product-list {
  max-height: 60vh;
  overflow-y: auto;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid var(--ion-color-light);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--ion-color-light-tint);
  }

  &.modified {
    background-color: var(--ion-color-warning-tint);
    border-left: 4px solid var(--ion-color-warning);
  }
}

.product-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-main {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: var(--ion-color-dark);
}

.product-brand {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin: 0;
}

.product-meta {
  margin-top: 4px;
}

.original-margin {
  font-size: 11px;
  color: var(--ion-color-medium);
  font-style: italic;
}

.margin-input-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.margin-input {
  --background: var(--ion-color-light);
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  width: 80px;
  text-align: center;

  &.changed {
    --background: var(--ion-color-success-tint);
    --color: var(--ion-color-success-contrast);
    font-weight: 600;
  }
}

.margin-unit {
  font-size: 12px;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.item-actions {
  flex: 1;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.reset-btn, .copy-btn {
  --border-radius: 50%;
  width: 36px;
  height: 36px;
}

.reset-btn {
  --color: var(--ion-color-warning);
}

.copy-btn {
  --color: var(--ion-color-primary);
}

// Empty State
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

// Loading State
.loading-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);

  ion-spinner {
    width: 32px;
    height: 32px;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

// Enhanced Footer
.enhanced-footer {
  --background: white;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
}

.footer-content {
  padding: 16px;
}

.footer-actions {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  align-items: center;
}

.footer-btn {
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  height: 44px;
  flex: 1;
  max-width: 150px;
}

.save-btn {
  position: relative;

  ion-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 10px;
    min-width: 20px;
    height: 20px;
  }
}

// Modal Styles
.bulk-modal-content, .import-export-modal-content {
  --background: var(--ion-color-light-tint);
}

.bulk-action-section {
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

  h4 {
    margin: 0 0 12px 0;
    color: var(--ion-color-primary);
    font-size: 16px;
    font-weight: 600;
  }
}

.bulk-input-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.bulk-input, .bulk-select {
  flex: 1;
  min-width: 120px;
  --background: var(--ion-color-light);
  --border-radius: 6px;
}

.help-text {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin: 8px 0;
  font-style: italic;
}

// Responsive Design
@media (max-width: 768px) {
  .actions-row {
    flex-direction: column;
    gap: 8px;
  }

  .brand-filter {
    width: 100%;
  }

  .bulk-action-btn, .import-export-btn {
    width: 100%;
  }

  .stats-row {
    justify-content: space-around;
  }

  .product-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .margin-input-section, .item-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .footer-actions {
    flex-direction: column;
  }

  .footer-btn {
    max-width: none;
    width: 100%;
  }

  .bulk-input-row {
    flex-direction: column;
  }

  .bulk-input, .bulk-select {
    width: 100%;
  }
}
  