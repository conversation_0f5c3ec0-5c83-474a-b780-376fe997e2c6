<app-header [title]="'Retailer Class Mgmt'" [returnUrl]="'tabs/home'"></app-header>

<ion-content>
  <!-- Enhanced Header with Search and Bulk Actions -->
  <div class="enhanced-header">
    <ion-card class="search-card">
      <ion-card-content class="search-content">
        <!-- Search Bar -->
        <div class="search-section">
          <ion-searchbar
            [(ngModel)]="searchTerm"
            (ionInput)="filterProducts()"
            placeholder="Search products by name or code..."
            show-clear-button="focus"
            class="custom-searchbar">
          </ion-searchbar>
        </div>

        <!-- Filter and Bulk Actions Row -->
        <div class="actions-row">
          <!-- Brand Filter -->
          <ion-select
            [(ngModel)]="selectedBrand"
            (ionChange)="filterProducts()"
            placeholder="Filter by Brand"
            interface="popover"
            class="brand-filter">
            <ion-select-option value="">All Brands</ion-select-option>
            <ion-select-option *ngFor="let brand of uniqueBrands" [value]="brand">{{brand}}</ion-select-option>
          </ion-select>

          <!-- Bulk Actions -->
          <ion-button
            fill="outline"
            size="small"
            (click)="openBulkActionsModal()"
            class="bulk-action-btn">
            <ion-icon name="settings-outline" slot="start"></ion-icon>
            Bulk Actions
          </ion-button>

          <!-- Import/Export -->
          <ion-button
            fill="outline"
            size="small"
            (click)="openImportExportModal()"
            class="import-export-btn">
            <ion-icon name="swap-horizontal-outline" slot="start"></ion-icon>
            Import/Export
          </ion-button>
        </div>

        <!-- Quick Stats -->
        <div class="stats-row" *ngIf="isDataLoaded">
          <div class="stat-item">
            <span class="stat-label">Total Products:</span>
            <span class="stat-value">{{filteredData?.length || 0}}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Modified:</span>
            <span class="stat-value">{{getModifiedCount()}}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Avg Margin:</span>
            <span class="stat-value">{{getAverageMargin()}}%</span>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Enhanced Product List -->
  <div class="products-container">
    <ion-card class="products-card">
      <ion-card-header class="products-header">
        <div class="header-row">
          <div class="header-col product-col">
            <ion-label class="header-label">Product Details</ion-label>
          </div>
          <div class="header-col margin-col">
            <ion-label class="header-label">Margin (%)</ion-label>
          </div>
          <div class="header-col actions-col">
            <ion-label class="header-label">Actions</ion-label>
          </div>
        </div>
      </ion-card-header>

      <ion-card-content class="products-content">
        <div class="product-list" *ngIf="isDataLoaded">
          <div *ngFor="let pd of filteredData; let i = index; trackBy: trackByProductId" class="product-item" [class.modified]="pd?.isModified">
            <div class="product-info">
              <div class="product-main">
                <h3 class="product-name">{{pd?.short_code || 'Unknown Product'}}</h3>
                <p class="product-brand" *ngIf="pd?.brand">{{pd.brand}}</p>
              </div>
              <div class="product-meta">
                <span class="original-margin" *ngIf="pd?.margin !== pd?.final_margin">
                  Original: {{pd?.margin}}%
                </span>
              </div>
            </div>

            <div class="margin-input-section">
              <ion-input
                #inputCrates
                type="number"
                [min]="0"
                [step]="0.1"
                [placeholder]="margin"
                [(ngModel)]="pd?.final_margin"
                (ngModelChange)="modifyMargin($event, pd)"
                (keyup.enter)="focusNextInput(i)"
                (keypress)="numberOnlyValidation($event)"
                class="margin-input"
                [class.changed]="pd?.margin !== pd?.final_margin">
              </ion-input>
              <span class="margin-unit">%</span>
            </div>

            <div class="item-actions">
              <ion-button
                fill="clear"
                size="small"
                (click)="resetMargin(pd)"
                [disabled]="pd?.margin === pd?.final_margin"
                class="reset-btn">
                <ion-icon name="refresh-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button
                fill="clear"
                size="small"
                (click)="copyMarginToSimilar(pd)"
                class="copy-btn">
                <ion-icon name="copy-outline" slot="icon-only"></ion-icon>
              </ion-button>
            </div>
          </div>

          <!-- Empty State -->
          <div *ngIf="isDataLoaded && (!filteredData || filteredData.length === 0)" class="empty-state">
            <ion-icon name="search-outline" class="empty-icon"></ion-icon>
            <h3>No products found</h3>
            <p>Try adjusting your search or filter criteria</p>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="!isDataLoaded" class="loading-state">
          <ion-spinner name="crescent"></ion-spinner>
          <p>Loading products...</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
<!-- Enhanced Footer -->
<ion-footer class="enhanced-footer">
  <ion-toolbar>
    <div class="footer-content">
      <div class="footer-actions">
        <ion-button
          fill="outline"
          color="medium"
          (click)="back()"
          class="footer-btn">
          <ion-icon name="arrow-back" slot="start"></ion-icon>
          Back
        </ion-button>

        <ion-button
          fill="outline"
          color="warning"
          (click)="resetAllChanges()"
          [disabled]="!hasUnsavedChanges()"
          class="footer-btn">
          <ion-icon name="refresh" slot="start"></ion-icon>
          Reset All
        </ion-button>

        <ion-button
          fill="solid"
          color="primary"
          (click)="save()"
          [disabled]="!hasUnsavedChanges()"
          class="footer-btn save-btn">
          <ion-icon name="save" slot="start"></ion-icon>
          Save Changes
          <ion-badge *ngIf="getModifiedCount() > 0" color="light">{{getModifiedCount()}}</ion-badge>
        </ion-button>
      </div>
    </div>
  </ion-toolbar>
  <app-floating-menu></app-floating-menu>
</ion-footer>

<!-- Bulk Actions Modal -->
<ion-modal [isOpen]="isBulkModalOpen" (willDismiss)="closeBulkActionsModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Bulk Margin Actions</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeBulkActionsModal()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="bulk-modal-content">
      <ion-card>
        <ion-card-header>
          <ion-card-title>Apply Margin to Multiple Products</ion-card-title>
        </ion-card-header>

        <ion-card-content>
          <!-- Apply to All -->
          <div class="bulk-action-section">
            <h4>Apply to All Products</h4>
            <div class="bulk-input-row">
              <ion-input
                type="number"
                [(ngModel)]="bulkMarginValue"
                placeholder="Enter margin %"
                class="bulk-input">
              </ion-input>
              <ion-button
                fill="solid"
                (click)="applyMarginToAll()"
                [disabled]="!bulkMarginValue">
                Apply to All
              </ion-button>
            </div>
          </div>

          <!-- Apply by Percentage -->
          <div class="bulk-action-section">
            <h4>Increase/Decrease by Percentage</h4>
            <div class="bulk-input-row">
              <ion-input
                type="number"
                [(ngModel)]="percentageChange"
                placeholder="Enter % change (e.g., 10 or -5)"
                class="bulk-input">
              </ion-input>
              <ion-button
                fill="solid"
                (click)="applyPercentageChange()"
                [disabled]="!percentageChange">
                Apply Change
              </ion-button>
            </div>
          </div>

          <!-- Apply by Brand -->
          <div class="bulk-action-section">
            <h4>Apply to Specific Brand</h4>
            <div class="bulk-input-row">
              <ion-select
                [(ngModel)]="selectedBrandForBulk"
                placeholder="Select Brand"
                class="bulk-select">
                <ion-select-option *ngFor="let brand of uniqueBrands" [value]="brand">{{brand}}</ion-select-option>
              </ion-select>
              <ion-input
                type="number"
                [(ngModel)]="brandMarginValue"
                placeholder="Margin %"
                class="bulk-input">
              </ion-input>
              <ion-button
                fill="solid"
                (click)="applyMarginToBrand()"
                [disabled]="!selectedBrandForBulk || !brandMarginValue">
                Apply to Brand
              </ion-button>
            </div>
          </div>

          <!-- Apply to Selected -->
          <div class="bulk-action-section">
            <h4>Apply to Filtered Products</h4>
            <p class="help-text">This will apply to all currently visible products ({{filteredData.length}} products)</p>
            <div class="bulk-input-row">
              <ion-input
                type="number"
                [(ngModel)]="filteredMarginValue"
                placeholder="Enter margin %"
                class="bulk-input">
              </ion-input>
              <ion-button
                fill="solid"
                (click)="applyMarginToFiltered()"
                [disabled]="!filteredMarginValue || filteredData.length === 0">
                Apply to Filtered
              </ion-button>
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    </ion-content>
  </ng-template>
</ion-modal>

<!-- Import/Export Modal -->
<ion-modal [isOpen]="isImportExportModalOpen" (willDismiss)="closeImportExportModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Import/Export Margins</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeImportExportModal()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="import-export-modal-content">
      <app-retailer-margin-import-export
        [buyerClassId]="editId"
        (importComplete)="onImportComplete()">
      </app-retailer-margin-import-export>
    </ion-content>
  </ng-template>
</ion-modal>