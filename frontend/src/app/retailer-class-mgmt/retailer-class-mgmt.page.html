<app-header [title]="'Retailer Class Mgmt'" [returnUrl]="'tabs/home'"></app-header>


<ion-content>
  <ion-grid class="ion-text-center ion-no-padding">
    <ion-grid class="selection ion-no-padding">
      <ion-row class="ion-no-padding">
        <ion-col size="12" class="ion-no-padding">
          <div style="max-width: 100%; overflow-y: scroll">
            <ion-grid class="ion-no-padding main-content invoice-item">
              <ion-row class="header-row ">
                <ng-container>
                  <ion-col size="8">
                    <ion-label>Name</ion-label>
                  </ion-col>
                  <ion-col size="4">
                    <ion-label>Margin</ion-label>
                  </ion-col>
                </ng-container>
              </ion-row>
              <ion-row *ngFor="let pd of data">
                <ion-col size="8">
                  <ion-text>{{pd.short_code }}</ion-text>
                </ion-col>
                <ion-col size="4">
                  <ion-input  #inputCrates type="number" [min]="0" [step]="1"
                    [placeholder]="margin" [(ngModel)]="pd.final_margin" (ngModelChange)="modifyMargin($event, pd)"
                    (keyup.enter)="changeFocusOnCrate(i)" (keypress)="numberOnlyValidation($event)"></ion-input>
                </ion-col>
              </ion-row>
            </ion-grid>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-grid>
</ion-content>
<ion-footer>
  <ion-item>
    <ion-button color="primary" (click)="back()" size="small">
      <ion-icon name="arrow-back" class="ion-padding-start"></ion-icon>
      Back
    </ion-button>
    <ion-button color="primary" (click)="save()" size="small">
      Save
      <ion-icon name="save" class="ion-padding-start"></ion-icon>
    </ion-button>
  </ion-item>
  <app-floating-menu></app-floating-menu>
</ion-footer>