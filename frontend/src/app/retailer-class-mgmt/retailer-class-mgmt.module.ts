import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RetailerClassMgmtPageRoutingModule } from './retailer-class-mgmt-routing.module';

import { RetailerClassMgmtPage } from './retailer-class-mgmt.page';
import { SharedModule } from '../shared/modules/shared/shared.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RetailerClassMgmtPageRoutingModule,
    SharedModule
  ],
  declarations: [RetailerClassMgmtPage]
})
export class RetailerClassMgmtPageModule {}
