import { Component, OnInit } from '@angular/core';
import { Platform, NavController } from '@ionic/angular';
import * as moment from 'moment';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { RouteService } from '../shared/services/route.service';
import { ToastService } from '../shared/services/toast.service';
import { SalesOrderService } from '../shared/services/sales-order.service';
import { BuyerService } from '../shared/services/buyer.service';
import { PrintServiceService } from '../shared/services/print-service.service';
import { UtilService } from '../shared/services/util.service';

@Component({
  selector: 'app-sales-order',
  templateUrl: './sales-order.page.html',
  styleUrls: ['./sales-order.page.scss'],
})
export class SalesOrderPage implements OnInit {
  product_data: any;
  data: any;
  date: any;
  previous_balance_total: any;
  bill_amount_total: any;
  receivable_amount_total: any;
  received_amount_total: any;
  current_balance_total: any;
  role: any = localStorage.getItem('role');
  routeData: any;

  salesPersonConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a Sales Person',
    customComparator: () => { }, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  routeConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a Route',
    customComparator: () => { }, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  selectedRoute = '';
  routes: any;
  selectedSalesPerson = '';
  salesPersons: any;
  mobile: boolean = localStorage.getItem('deviceId') != null && this.utilService.isAndroid();
  constructor(
    private api: SalesOrderService,
    private buyer_api: BuyerService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public nav: NavController,
    public routerService: RouteService,
    private printerService: PrintServiceService,
    private utilService: UtilService,
  ) {

  }

  ngOnInit() { }

  addSalesOrder() {
    localStorage.setItem('state', "sales-order");
    this.nav.navigateForward(`/create-invoice`);
  }

  ionViewWillEnter() {
    this.getData();
    this.getRoute();
  }
  filterData() {
    this.getData();
  }
  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getSalesOrder(this.selectedSalesPerson, this.selectedRoute)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.data = res.data;
            this.salesPersons = res.sales_person;
            this.getTotal()
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async getRoute() {
    // await this.ionLoaderService.startLoader().then(async () => {
    await this.buyer_api
      .getRoute()
      .then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.routes = res.data
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        // this.ionLoaderService.dismissLoader();
      })
      .catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top')
        // this.ionLoaderService.dismissLoader();
        // console.log(err);
      });
    // });
  }
  async getTotal() {
    this.previous_balance_total = 0;
    this.bill_amount_total = 0;
    this.receivable_amount_total = 0;
    this.received_amount_total = 0;
    this.current_balance_total = 0;
    this.data.forEach((element) => {
      this.previous_balance_total += element.previous_balance;
      this.bill_amount_total += element.bill_amount;
      this.receivable_amount_total += element.receivable_amount;
      this.received_amount_total += element.received_amount;
      this.current_balance_total += element.current_balance;
    });

  }
  editInvoice(id: number) {
    localStorage.setItem('state', 'sales-order');
    this.nav.navigateForward(`/edit-invoice?id=${id}`);
  }
  editInvoiceSO(id: number) {
    localStorage.setItem('state', 'edit-sales-order');
    this.nav.navigateForward(`/edit-invoice?id=${id}`);
  }
  delete(data) {
    this.alertService
      .alertConfirm(
        "Alert",
        "Do you want to delete the invoice !!!",
        "Yes",
        "No"
      )
      .then((res) => {
        if (res) {
          this.deleteSalesInvoice(data);
        }
      });
  }
  async deleteSalesInvoice(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .deleteSalesInvoice({ id: data })
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.getData();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  printInvoice(data) {
    this.printerService.print(data, this.mobile, data.name + " - " + data.date);
  }

}
