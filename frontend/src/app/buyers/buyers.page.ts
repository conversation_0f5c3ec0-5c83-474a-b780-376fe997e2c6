import { Component, OnInit } from '@angular/core';
import { Platform, ModalController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { BuyerService } from '../shared/services/buyer.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { ToastService } from '../shared/services/toast.service';
import { IonicSelectableComponent } from 'ionic-selectable';
import { AuthenticationService } from '../shared/services/authentication.service';
import { ImportExportModalComponent } from '../shared/components/import-export-modal/import-export-modal.component';
import { BuyerBrandService, BuyerBrand } from '../shared/services/buyer-brand.service';
import { ProductService } from '../shared/services/product.service';
import { TranslationService } from '../shared/services/translation.service';
import { firstValueFrom } from 'rxjs';


@Component({
  selector: 'app-buyers',
  templateUrl: './buyers.page.html',
  styleUrls: ['./buyers.page.scss'],
})
export class BuyersPage implements OnInit {
  isModalOpen = false;
  isEditModalOpen = false;
  isAssetModalOpen = false;
  isDepositModalOpen = false;
  isBuyerModalOpen = false;
  isBrandModalOpen = false;
  data: any;
  editData: any;
  displayData: any;
  filterEmpty: boolean;
  routeData: any;
  buyer_class: any;
  assetFormData: any = '';
  depositFormData: any = '';

  // Route filter properties
  routes: any[] = [];
  selectedRoute: any = { id: null, name: 'All Routes' }; // Initialize with default "All Routes"
  originalData: any;
  currentSearchTerm: string = '';
  assetFormSchema = [
    { name: 'asset_type', label: 'Asset Type', type: 'text', required: true },
    { name: 'serial_no', label: 'Asset Serial No', type: 'text', required: true },
    { name: 'capacity', label: 'Asset Capacity', type: 'text', required: true },
    { name: 'model', label: 'Asset Model', type: 'text', required: true },
    { name: 'asset_file', label: 'Asset Picture', type: 'file', multiple: true },
  ]
  depositFormSchema = [
    { name: 'amount', label: 'Deposit Amount', type: 'number', required: true },
    { name: 'date', label: 'Deposit Date', type: 'date', required: true },
    { name: 'reference', label: 'Enter Reference Number', type: 'text', required: true },
    { name: 'reference_file', label: 'Asset Picture', type: 'file', multiple: false },
  ]
  selectedBuyer: any;
  customButtons: { label: string; action: string; color: string; }[];
  showSearch: boolean = false;
  showSummary: boolean = false;

  // Brand management properties
  selectedBuyerForBrands: any = null;
  brands: any[] = [];
  buyerBrands: BuyerBrand[] = [];
  selectedBrandIds: number[] = [];
  // formSchema = [
  //   { name: 'name', label: 'Full Name', type: 'text', required: true },
  //   { name: 'email', label: 'Email', type: 'text', required: true },
  //   { name: 'bio', label: 'Bio', type: 'textarea' },
  //   { name: 'dob', label: 'Date of Birth', type: 'date', required: true },
  //   { name: 'subscribe', label: 'Subscribe', type: 'toggle' },
  //   { name: 'gender', label: 'Gender', type: 'select', options: [
  //       { value: 'male', label: 'Male' },
  //       { value: 'female', label: 'Female' },
  //       { value: 'other', label: 'Other' }
  //     ]
  //   },
  //   { name: 'profilePicture', label: 'Profile Picture', type: 'file', multiple: false },
  //   { name: 'gallery', label: 'Gallery Images', type: 'file', multiple: true }
  // ];
  constructor(
    private api: BuyerService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public authentication: AuthenticationService,
    private modalController: ModalController,
    private buyerBrandService: BuyerBrandService,
    private productService: ProductService,
    public translate: TranslationService
  ) { }


  ngOnInit() {
    this.getData()
  }
  setAssetOpen(isAssetModalOpen: boolean) {
    this.isAssetModalOpen = isAssetModalOpen;
  }
  async handleAssetFormSubmit(data) {
    console.log(data);
    this.assetFormData ?
      await this.ionLoaderService.startLoader().then(async () => {
        await this.api
          .putBuyerAssetData(data)
          .then(async (res: any) => {
            if (res.success) {
              console.log(res);
              this.toast.toastServices(res.message, 'success', 'top');
              this.setAssetOpen(false);
              this.open(this.selectedBuyer.id)
            }
            else {
              this.toast.toastServices(res.message, 'danger', 'top')
            }
            this.ionLoaderService.dismissLoader();
          })
          .catch(async (err) => {
            this.toast.toastServices(err, 'danger', 'top')
            this.ionLoaderService.dismissLoader();
            // console.log(err);
          });
      }) :
      await this.ionLoaderService.startLoader().then(async () => {
        await this.api
          .postBuyerAssetData(this.selectedBuyer.id, data)
          .then(async (res: any) => {
            if (res.success) {
              console.log(res);
              this.toast.toastServices(res.message, 'success', 'top');
              this.setAssetOpen(false);
              this.open(this.selectedBuyer.id)
            }
            else {
              this.toast.toastServices(res.message, 'danger', 'top')
            }
            this.ionLoaderService.dismissLoader();
          })
          .catch(async (err) => {
            this.toast.toastServices(err, 'danger', 'top')
            this.ionLoaderService.dismissLoader();
            // console.log(err);
          });
      });
  }
  setDepositOpen(isDepositModalOpen: boolean) {
    this.isDepositModalOpen = isDepositModalOpen
  }
  async handleDepositFormSubmit(data) {
    console.log(data);
    this.depositFormData ?
      await this.ionLoaderService.startLoader().then(async () => {
        await this.api
          .putBuyerDepositData(data)
          .then(async (res: any) => {
            if (res.success) {
              console.log(res);
              this.toast.toastServices(res.message, 'success', 'top');
              this.setDepositOpen(false);
              this.open(this.selectedBuyer.id)
            }
            else {
              this.toast.toastServices(res.message, 'danger', 'top')
            }
            this.ionLoaderService.dismissLoader();
          })
          .catch(async (err) => {
            this.toast.toastServices(err, 'danger', 'top')
            this.ionLoaderService.dismissLoader();
            // console.log(err);
          });
      }) :
      await this.ionLoaderService.startLoader().then(async () => {
        await this.api
          .postBuyerDepositData(this.selectedBuyer.id, data)
          .then(async (res: any) => {
            if (res.success) {
              console.log(res);
              this.toast.toastServices(res.message, 'success', 'top');
              this.setDepositOpen(false);
              this.open(this.selectedBuyer.id)
            }
            else {
              this.toast.toastServices(res.message, 'danger', 'top')
            }
            this.ionLoaderService.dismissLoader();
          })
          .catch(async (err) => {
            this.toast.toastServices(err, 'danger', 'top')
            this.ionLoaderService.dismissLoader();
            // console.log(err);
          });
      })
      ;
  }

  handleObjectDeleteClicked(data) {
    let parsedData = JSON.parse(data)
    switch (parsedData.key) {
      case 'buyer_asset':
        this.deleteBuyerAsset(parsedData.data)
        break;
      case 'buyer_deposit':
        this.deleteBuyerDeposit(parsedData.data)
        break;
      default:
        break;
    }

  }
  async deleteBuyerDeposit(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .deleteBuyerDepositData(data.id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.open(this.selectedBuyer.id)
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async deleteBuyerAsset(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .deleteBuyerAssetData(data.id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.open(this.selectedBuyer.id)
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  handleObjectEditClicked(data) {
    let parsedData = JSON.parse(data)
    console.log(JSON.parse(data));
    switch (parsedData.key) {
      case 'buyer_asset':
        this.setAssetOpen(true);
        this.assetFormData = parsedData.data;
        break;
      case 'buyer_deposit':
        this.setDepositOpen(true);
        this.depositFormData = parsedData.data;
        break;
      default:
        break;
    }

  }

  async open(id) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getBuyerById(id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.setBuyerOpen(true);
            this.selectedBuyer = res.data
            this.customButtons = [
              { label: 'Add Deposit', action: 'add_deposit', color: 'primary' },
              { label: 'Add Asset', action: 'add_asset', color: 'primary' },
            ];
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  handleButtonClick(action: string) {
    console.log(`Button Clicked: ${action}`);

    switch (action) {
      case 'add_deposit':
        this.setDepositOpen(true);
        break;
      case 'add_asset':
        this.setAssetOpen(true);
        break;
      default:
        console.log('Unknown action');
    }
  }

  setBuyerOpen(isBuyerModalOpen: boolean) {
    this.isBuyerModalOpen = isBuyerModalOpen;
  }
  setOpen(isOpen: boolean) {
    this.getEditData();
    this.isModalOpen = isOpen;
  }
  setEditOpen(isOpen: boolean) {
    this.editData.route = { id: this.editData.route, name: this.editData.route_name }
    this.editData.buyer_class = { id: this.editData.buyer_class, name: this.editData.buyer_class_name }
    console.log(this.editData);
    this.getEditData();
    this.isEditModalOpen = isOpen;
  }

  async addRoute(event: { component: IonicSelectableComponent }) {
    const name = event.component.searchText;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .addRoute(name)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.getEditData();
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async getEditData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .geteditData()
        .subscribe(async (res: any) => {
          if (res) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.routeData = res[0].data;
            this.buyer_class = res[1].data;
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        }, (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  async getData() {
    try {
      const result = await this.ionLoaderService.withLoader(async () => {
        return await this.api.getBuyer();
      }, 'Loading buyers...') as any;

      if (result.success) {
        console.log(result);
        this.toast.toastServices(result.message, 'success', 'top');
        this.data = result.data;
        this.originalData = result.data;
        this.displayData = this.data;
        this.extractRoutes();
        this.filterEmpty = this.data.length <= 0;
      } else {
        this.toast.toastServices(result.message, 'danger', 'top');
        this.filterEmpty = true;
      }
    } catch (err: any) {
      this.toast.toastServices('Error loading buyers', 'danger', 'top');
      console.error('Error loading buyers:', err);
      this.filterEmpty = true;
    }
  }
  edit(data) {
    this.editData = data
    this.setEditOpen(true)
  }
  delete(data) {
    this.alertService.alertConfirm('Alert', 'Are you sure you want to delete the buyer !!!', 'yes', 'no').then((res) => {
      if (res) {
        this.deleteBuyer(data)
      }
    })
  }

  async addBuyer(data) {
    data.route_id = data.route.id;
    data.buyer_class_id = data.buyer_class.id;
    delete data.route;
    delete data.buyer_class;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .saveBuyer(data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.isModalOpen = false;
            this.getData()
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });

  }
  async editBuyer(data) {
    data.route_id = data.route.id;
    data.buyer_class_id = data.buyer_class.id;
    delete data.buyer_class;
    delete data.route;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.editBuyer(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.isEditModalOpen = false;
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      });
    })
  }
  async deleteBuyer(id) {
    let data = {
      id: id
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.deleteBuyer(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      });
    });
  }
  async editBuyerStatus(status, id) {
    let data = {
      id: id,
      active: status
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.editBuyerStatus(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      })
    })

  }
  // Debounce timer for search
  private searchTimeout: any;

  filterItems(event) {
    const val = event.target.value;
    this.currentSearchTerm = val;

    // Clear existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search for 300ms (shorter since it's client-side)
    this.searchTimeout = setTimeout(() => {
      this.applyFilters();
    }, 300);
  }

  // Clear search and reset filters
  clearSearch() {
    this.currentSearchTerm = '';
    this.selectedRoute = { id: null, name: 'All Routes' };
    this.applyFilters();
    this.toast.toastServices('Search cleared', 'success', 'top');
  }

  // Extract unique routes from buyer data
  extractRoutes() {
    const uniqueRoutes = [];
    const routeNames = new Set();

    // Add "All Routes" option
    const allRoutesOption = { id: null, name: 'All Routes' };
    uniqueRoutes.push(allRoutesOption);

    // Extract unique routes from buyers
    this.originalData.forEach((buyer: any) => {
      if (buyer.route_name && !routeNames.has(buyer.route_name)) {
        routeNames.add(buyer.route_name);
        uniqueRoutes.push({
          id: buyer.route,
          name: buyer.route_name
        });
      }
    });

    this.routes = uniqueRoutes;

    // Ensure selectedRoute is properly set and matches an object from the routes array
    if (!this.selectedRoute || this.selectedRoute.id === null) {
      this.selectedRoute = allRoutesOption;
    } else {
      // Find the matching route object from the new routes array
      const matchingRoute = this.routes.find(route => route.id === this.selectedRoute.id);
      if (matchingRoute) {
        this.selectedRoute = matchingRoute;
      } else {
        this.selectedRoute = allRoutesOption;
      }
    }
  }

  // Filter by route - handles ngModelChange event like other working examples
  filterByRoute(selectedRoute: any) {
    this.selectedRoute = selectedRoute;
    this.applyFilters();
  }



  // Apply both search and route filters
  applyFilters() {
    let filteredData = [...this.originalData]; // Create a copy to avoid mutation

    // Apply route filter first
    if (this.selectedRoute && this.selectedRoute.id !== null) {
      filteredData = filteredData.filter((item: any) => {
        return item.route === this.selectedRoute.id;
      });
    }

    // Apply search filter
    if (this.currentSearchTerm && this.currentSearchTerm.length >= 1) {
      filteredData = filteredData.filter((item: any) => {
        return item.name.toLowerCase().indexOf(this.currentSearchTerm.toLowerCase()) > -1 ||
               item.place.toLowerCase().indexOf(this.currentSearchTerm.toLowerCase()) > -1 ||
               item.contact_person.toLowerCase().indexOf(this.currentSearchTerm.toLowerCase()) > -1 ||
               item.phone_no.toString().indexOf(this.currentSearchTerm) > -1;
      });
    }

    this.displayData = filteredData;
    console.log('Filtered data length:', this.displayData.length); // Debug log
    this.data = filteredData; // Update data for compatibility with existing code
    this.filterEmpty = filteredData.length <= 0;
  }

  toggleSearch() {
    this.showSearch = !this.showSearch;
  }

  toggleSummary() {
    this.showSummary = !this.showSummary;
  }

  // Summary calculation methods
  getTotalBuyers(): number {
    return this.displayData ? this.displayData.length : 0;
  }

  getActiveBuyers(): number {
    return this.displayData ? this.displayData.filter(buyer => buyer.active).length : 0;
  }

  getTotalOutstanding(): number {
    if (!this.displayData) return 0;
    return this.displayData.reduce((total, buyer) => {
      return total + (buyer.current_balance > 0 ? buyer.current_balance : 0);
    }, 0);
  }

  getAverageBalance(): number {
    if (!this.displayData || this.displayData.length === 0) return 0;
    const total = this.displayData.reduce((sum, buyer) => sum + buyer.current_balance, 0);
    return total / this.displayData.length;
  }

  // Import/Export Modal Methods
  async openImportExportModal() {
    const modal = await this.modalController.create({
      component: ImportExportModalComponent,
      componentProps: {
        appLabel: 'master',
        modelName: 'buyer'
      },
      cssClass: 'import-export-modal'
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.imported) {
        // Refresh data after successful import
        this.getData();
      }
    });

    return await modal.present();
  }

  callBuyer(phone_no) {
    window.location.href = `tel:${phone_no}`;
  }

  // Brand Management Methods
  setBrandModalOpen(isOpen: boolean) {
    this.isBrandModalOpen = isOpen;
  }

  async manageBrands(buyer: any) {
    this.selectedBuyerForBrands = buyer;
    await this.loadBrandsData();
    this.setBrandModalOpen(true);
  }

  async loadBrandsData() {
    try {
      console.log('Loading brands data for buyer:', this.selectedBuyerForBrands);
      
      // Load all brands
      const brandsResponse: any = await this.productService.getBrand();
      console.log('Brands response:', brandsResponse);
      if (brandsResponse.success) {
        this.brands = brandsResponse.data;
        console.log('All brands loaded:', this.brands);
      }

      // Load buyer brands
      if (this.selectedBuyerForBrands) {
        console.log('Loading buyer brands for buyer ID:', this.selectedBuyerForBrands.id);
        const buyerBrandsResponse: any = await firstValueFrom(this.buyerBrandService.getBuyerBrands(this.selectedBuyerForBrands.id));
        console.log('Buyer brands response:', buyerBrandsResponse);
        if (buyerBrandsResponse.success) {
          this.buyerBrands = buyerBrandsResponse.data;
          this.selectedBrandIds = this.buyerBrands.map(bb => bb.brand);
          console.log('Buyer brands loaded:', this.buyerBrands);
          console.log('Selected brand IDs:', this.selectedBrandIds);
        } else {
          console.log('Buyer brands response not successful:', buyerBrandsResponse);
          this.buyerBrands = [];
          this.selectedBrandIds = [];
        }
      }
    } catch (error) {
      console.error('Error loading brands data:', error);
      this.toast.toastServices('Error loading brands data', 'danger', 'top');
      this.buyerBrands = [];
      this.selectedBrandIds = [];
    }
  }

  isBrandSelected(brandId: number): boolean {
    const isSelected = this.selectedBrandIds.includes(brandId);
    console.log(`Brand ${brandId} selected: ${isSelected}, selectedBrandIds:`, this.selectedBrandIds);
    return isSelected;
  }

  toggleBrand(brandId: number) {
    const index = this.selectedBrandIds.indexOf(brandId);
    console.log(`Toggling brand ${brandId}, current index: ${index}, selectedBrandIds:`, this.selectedBrandIds);
    if (index > -1) {
      this.selectedBrandIds.splice(index, 1);
    } else {
      this.selectedBrandIds.push(brandId);
    }
    console.log(`After toggle, selectedBrandIds:`, this.selectedBrandIds);
  }

  async saveBrandChanges() {
    await this.ionLoaderService.startLoader();

    try {
      const currentBrandIds = this.buyerBrands.map(bb => bb.brand);
      const toAdd = this.selectedBrandIds.filter(id => !currentBrandIds.includes(id));
      const toRemove = this.buyerBrands.filter(bb => !this.selectedBrandIds.includes(bb.brand));

      // Add new relationships
      if (toAdd.length > 0) {
        await firstValueFrom(this.buyerBrandService.createBulkBuyerBrands(this.selectedBuyerForBrands.id, toAdd));
      }

      // Remove old relationships
      if (toRemove.length > 0) {
        const idsToRemove = toRemove.map(bb => bb.id!);
        await firstValueFrom(this.buyerBrandService.deleteBulkBuyerBrands(idsToRemove));
      }

      this.toast.toastServices('Buyer brands updated successfully', 'success', 'top');
      this.setBrandModalOpen(false);

    } catch (error) {
      console.error('Error saving buyer brands:', error);
      this.toast.toastServices('Error updating buyer brands', 'danger', 'top');
    } finally {
      await this.ionLoaderService.dismissLoader();
    }
  }

  getBrandName(brandId: number): string {
    const brand = this.brands.find(b => b.id === brandId);
    console.log(`Getting brand name for ID ${brandId}:`, brand);
    return brand ? brand.name : `Brand ${brandId}`;
  }
}
