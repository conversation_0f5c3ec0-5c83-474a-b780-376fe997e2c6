<app-header [title]="'buyers.title' | translate" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="buyers-content">
  <!-- Collapsible Summary Section - Moved to Top -->
  <div class="summary-section" *ngIf="displayData && displayData.length > 0">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        Buyers Summary
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card total-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Buyers</h3>
                <h1>{{getTotalBuyers()}}</h1>
                <p>Registered buyers</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="people" class="summary-icon total"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card active-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Active Buyers</h3>
                <h1>{{getActiveBuyers()}}</h1>
                <p>Currently active</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="person-circle" class="summary-icon active"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card outstanding-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Outstanding</h3>
                <h1>{{getTotalOutstanding() | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Pending collections</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="wallet" class="summary-icon outstanding"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card average-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Avg Balance</h3>
                <h1>{{getAverageBalance() | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Average per buyer</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trending-up" class="summary-icon average"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    </div>
  </div>

  <!-- Collapsible Search Section -->
  <div class="search-section">
    <div class="search-header" (click)="toggleSearch()">
      <h3 class="section-title">
        <ion-icon name="search-outline" class="section-icon"></ion-icon>
        Search & Options
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSearch ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="search-content" [class.expanded]="showSearch">
      <ion-grid>
        <ion-row>
          <ion-col size="12">
            <ion-searchbar
              (ionInput)="filterItems($event)"
              showCancelButton="focus"
              placeholder="Search buyers..."
              class="custom-searchbar">
            </ion-searchbar>
          </ion-col>

          <!-- Route Filter -->
          <ion-col size="6">
            <ion-item class="filter-item">
              <ion-label position="stacked">Filter by Route</ion-label>
              <ionic-selectable
                name="route"
                [shouldFocusSearchbar]="true"
                [(ngModel)]="selectedRoute"
                placeholder="All Routes"
                [items]="routes"
                itemValueField="id"
                itemTextField="name"
                [canSearch]="true"
                (ngModelChange)="filterByRoute($event)"
                class="route-filter">
              </ionic-selectable>
            </ion-item>
          </ion-col>

          <!-- Import/Export Button -->
          <ion-col size="6">
            <ion-button fill="outline" expand="block" class="filter-button" (click)="openImportExportModal()">
              <ion-label>Import/Export</ion-label>
              <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  </div>

  <!-- No Data Message -->
  <ion-item lines="none" *ngIf="filterEmpty" class="no-data-item">
    <ion-icon name="search-outline" slot="start" color="medium"></ion-icon>
    <ion-text color="medium">
      <h6>
        <span *ngIf="currentSearchTerm && selectedRoute && selectedRoute.id !== null">
          No buyers found for "{{currentSearchTerm}}" in route "{{selectedRoute.name}}".
        </span>
        <span *ngIf="currentSearchTerm && (!selectedRoute || selectedRoute.id === null)">
          No buyers found for "{{currentSearchTerm}}".
        </span>
        <span *ngIf="!currentSearchTerm && selectedRoute && selectedRoute.id !== null">
          No buyers found for route "{{selectedRoute.name}}".
        </span>
        <span *ngIf="!currentSearchTerm && (!selectedRoute || selectedRoute.id === null)">
          No buyers found.
        </span>
      </h6>
      <p *ngIf="selectedRoute && selectedRoute.id !== null" style="margin-top: 8px; font-size: 14px;">
        Try selecting "All Routes" to see all buyers or check if buyers are assigned to this route.
      </p>
    </ion-text>
  </ion-item>

  <!-- Buyers List -->
  <div class="buyers-list">
    <ion-item
      *ngFor="let buyer of displayData"
      class="buyer-item"
      lines="none"
      (click)="open(buyer.id)">

      <!-- Buyer Icon -->
      <div class="buyer-icon" slot="start">
        <ion-icon name="person-circle-outline" class="person-icon"></ion-icon>
      </div>

      <!-- Buyer Details -->
      <ion-label class="buyer-details">
        <h2 class="buyer-name">{{buyer.name}}</h2>

        <!-- Location and Contact -->
        <div class="buyer-meta">
          <div class="location-info">
            <ion-icon name="location-outline" class="meta-icon"></ion-icon>
            <span class="location-text">{{buyer.place}}</span>
          </div>
          <div class="contact-info">
            <ion-icon name="call-outline" class="meta-icon"></ion-icon>
            <span class="contact-text">{{buyer.phone_no}}</span>
          </div>
        </div>

        <!-- GST and Contact Person -->
        <div class="buyer-details-row">
          <span class="buyer-detail" *ngIf="buyer.gst_no">GST: {{buyer.gst_no}}</span>
          <span class="buyer-detail" *ngIf="buyer.contact_person">Contact: {{buyer.contact_person}}</span>
        </div>

        <!-- Balance Information -->
        <div class="balance-details">
          <div class="balance-row">
            <span class="balance-label">Current Balance:</span>
            <span class="balance-value" [ngClass]="{
              'balance-positive': buyer.current_balance > 0,
              'balance-zero': buyer.current_balance === 0,
              'balance-negative': buyer.current_balance < 0
            }">
              {{buyer.current_balance | currency: 'INR':'symbol':'1.0-0'}}
            </span>
          </div>
        </div>

        <!-- Status and Profile Completion -->
        <div class="status-row">
          <ion-badge [color]="buyer.active ? 'success' : 'medium'" class="status-badge">
            {{buyer.active ? 'Active' : 'Inactive'}}
          </ion-badge>

          <!-- Profile Completion Status -->
          <div class="profile-completion" *ngIf="buyer.profile_completion">
            <ion-badge
              [color]="buyer.profile_completion.is_complete ? 'success' :
                       buyer.profile_completion.completion_percentage >= 66 ? 'warning' : 'danger'"
              class="completion-badge">
              <ion-icon name="checkmark-circle" *ngIf="buyer.profile_completion.is_complete"></ion-icon>
              <ion-icon name="warning" *ngIf="!buyer.profile_completion.is_complete && buyer.profile_completion.completion_percentage >= 66"></ion-icon>
              <ion-icon name="alert-circle" *ngIf="buyer.profile_completion.completion_percentage < 66"></ion-icon>
              {{buyer.profile_completion.completion_percentage}}% Complete
            </ion-badge>
          </div>
        </div>

        <!-- Missing Modules Info -->
        <div class="missing-modules" *ngIf="buyer.profile_completion && !buyer.profile_completion.is_complete">
          <small class="missing-text">
            Missing: {{buyer.profile_completion.missing_modules.join(', ')}}
          </small>
        </div>
      </ion-label>

      <!-- Actions -->
      <div class="buyer-actions" slot="end">
        <ion-button
          fill="clear"
          size="small"
          color="secondary"
          class="action-button brands-button"
          (click)="$event.stopPropagation(); manageBrands(buyer)"
          title="Manage Brands">
          <ion-icon name="pricetag-outline"></ion-icon>
        </ion-button>
        <ion-button
          fill="clear"
          size="small"
          color="primary"
          class="action-button view-button"
          (click)="$event.stopPropagation(); open(buyer.id)">
          <ion-icon name="eye-outline"></ion-icon>
        </ion-button>
        <ion-button
          fill="clear"
          size="small"
          color="warning"
          class="action-button edit-button"
          (click)="$event.stopPropagation(); edit(buyer)">
          <ion-icon name="create-outline"></ion-icon>
        </ion-button>
        <ion-button
          fill="clear"
          size="small"
          color="danger"
          class="action-button delete-button"
          (click)="$event.stopPropagation(); delete(buyer.id)">
          <ion-icon name="trash-outline"></ion-icon>
        </ion-button>
        <ion-badge (click)="callBuyer(buyer.phone_no)" class="status-badge">
            <ion-icon name="call-outline"></ion-icon>
        </ion-badge>
        <ion-toggle
          [(ngModel)]="buyer.active"
          (ngModelChange)="editBuyerStatus($event, buyer.id)"
          (click)="$event.stopPropagation()"
          class="status-toggle">
        </ion-toggle>
      </div>
    </ion-item>
  </div>

  <!-- Add Buyer Modal -->
  <ion-modal [isOpen]="isModalOpen" class="buyer-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="primary">
          <ion-title>Add New Buyer</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <form #form="ngForm" (ngSubmit)="addBuyer(form.value)">
          <div class="form-section">
            <h3 class="section-title">Basic Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Name
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="name" required="true" placeholder="Enter buyer name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Place
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="place" required="true" placeholder="Enter location"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">GST Number
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="gst_no" required="true" placeholder="Enter GST number"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Contact Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Contact Person
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="contact_person" required="true" placeholder="Enter contact person name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Phone Number
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="tel" name="phone_no" required="true" placeholder="Enter phone number"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Additional Details</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Sort Order
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="number" name="sort_order" required="true" placeholder="Enter sort order"></ion-input>
            </ion-item>

            <ion-item class="form-item" *ngIf="authentication.checkPermission('change','creditlimit')">
              <ion-label position="stacked">Credit Limit
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="number" name="credit_limit" required="true" placeholder="Enter credit limit"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label>Route</ion-label>
              <ionic-selectable
                itemValueField="id"
                itemTextField="name"
                [items]="routeData"
                ngModel
                name="route"
                [canSearch]="true"
                [canAddItem]="true"
                (onAddItem)="addRoute($event)"
                placeholder="Select or add a route">
              </ionic-selectable>
            </ion-item>

            <ion-item class="form-item">
              <ion-label>Buyer Class</ion-label>
              <ionic-selectable
                itemValueField="id"
                itemTextField="name"
                [items]="buyer_class"
                ngModel
                name="buyer_class"
                [canSearch]="true"
                placeholder="Select buyer class">
              </ionic-selectable>
            </ion-item>
          </div>

          <div class="form-actions">
            <ion-button
              type="submit"
              [disabled]="form.invalid"
              expand="block"
              fill="solid"
              shape="round"
              color="primary"
              class="submit-button">
              <ion-icon name="add-outline" slot="start"></ion-icon>
              Add Buyer
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
  <!-- Edit Buyer Modal -->
  <ion-modal [isOpen]="isEditModalOpen" class="buyer-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="warning">
          <ion-title>Edit Buyer</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setEditOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <form #form="ngForm" (ngSubmit)="editBuyer(form.value)">
          <input type="hidden" name="id" [(ngModel)]="editData.id" />

          <div class="form-section">
            <h3 class="section-title">Basic Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Name
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.name" type="text" name="name" required="true" placeholder="Enter buyer name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Place
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.place" type="text" name="place" required="true" placeholder="Enter location"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">GST Number
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.gst_no" type="text" name="gst_no" required="true" placeholder="Enter GST number"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Contact Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Contact Person
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.contact_person" type="text" name="contact_person" required="true" placeholder="Enter contact person name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Phone Number
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.phone_no" type="tel" name="phone_no" required="true" placeholder="Enter phone number"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Additional Details</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Sort Order
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.sort_order" type="number" name="sort_order" required="true" placeholder="Enter sort order"></ion-input>
            </ion-item>

            <ion-item class="form-item" *ngIf="authentication.checkPermission('change','creditlimit')">
              <ion-label position="stacked">Credit Limit
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.credit_limit" type="number" name="credit_limit" required="true" placeholder="Enter credit limit"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label>Route</ion-label>
              <ionic-selectable
                itemValueField="id"
                itemTextField="name"
                [items]="routeData"
                [(ngModel)]="editData.route"
                name="route"
                [canSearch]="true"
                [canAddItem]="true"
                (onAddItem)="addRoute($event)"
                placeholder="Select or add a route">
              </ionic-selectable>
            </ion-item>

            <ion-item class="form-item">
              <ion-label>Buyer Class</ion-label>
              <ionic-selectable
                itemValueField="id"
                itemTextField="name"
                [items]="buyer_class"
                [(ngModel)]="editData.buyer_class"
                name="buyer_class"
                [canSearch]="true"
                placeholder="Select a buyer class">
              </ionic-selectable>
            </ion-item>
          </div>

          <div class="form-actions">
            <ion-button
              type="submit"
              [disabled]="form.invalid"
              expand="block"
              fill="solid"
              shape="round"
              color="warning"
              class="submit-button">
              <ion-icon name="save-outline" slot="start"></ion-icon>
              Update Buyer
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
  <!-- Asset Modal -->
  <ion-modal [isOpen]="isAssetModalOpen" class="asset-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="secondary">
          <ion-title>Buyer Assets</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setAssetOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <app-dynamic-form
          [formConfig]="assetFormSchema"
          [formData]="assetFormData"
          (formSubmit)="handleAssetFormSubmit($event)">
        </app-dynamic-form>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Deposit Modal -->
  <ion-modal [isOpen]="isDepositModalOpen" class="deposit-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="tertiary">
          <ion-title>Buyer Deposits</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setDepositOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <app-dynamic-form
          [formConfig]="depositFormSchema"
          [formData]="depositFormData"
          (formSubmit)="handleDepositFormSubmit($event)">
        </app-dynamic-form>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Buyer Details Modal -->
  <ion-modal [isOpen]="isBuyerModalOpen" class="buyer-details-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="primary">
          <ion-title>Buyer Details</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setBuyerOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <app-dynamic-view
          [data]="selectedBuyer"
          [buttons]="customButtons"
          (buttonClicked)="handleButtonClick($event)"
          (objectDeleteClicked)="handleObjectDeleteClicked($event)"
          (objectEditClicked)="handleObjectEditClicked($event)">
        </app-dynamic-view>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Brand Management Modal -->
  <ion-modal [isOpen]="isBrandModalOpen" class="brand-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="secondary">
          <ion-title>Manage Brands - {{selectedBuyerForBrands?.name}}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setBrandModalOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding brand-modal-content">
        <div class="brand-section">
          <div class="section-header">
            <h3>Select Brands for this Buyer</h3>
            <p>Choose which brands this buyer can purchase products from.</p>
          </div>

          <div class="brands-list" *ngIf="brands.length > 0">
            <ion-item
              *ngFor="let brand of brands"
              class="brand-item"
              [class.selected]="isBrandSelected(brand.id)">
              <ion-checkbox
                slot="start"
                [checked]="isBrandSelected(brand.id)"
                (ionChange)="toggleBrand(brand.id)">
              </ion-checkbox>
              <ion-label>
                <h3>{{brand.name}}</h3>
              </ion-label>
            </ion-item>
          </div>

          <div class="no-brands" *ngIf="brands.length === 0">
            <ion-icon name="pricetag-outline" class="no-brands-icon"></ion-icon>
            <h3>No Brands Available</h3>
            <p>Please create some brands first before assigning them to buyers.</p>
          </div>

          <div class="selected-summary" *ngIf="selectedBrandIds.length > 0">
            <h4>Selected Brands ({{selectedBrandIds.length}}):</h4>
            <div class="selected-brands">
              <ion-chip
                *ngFor="let brandId of selectedBrandIds"
                color="secondary">
                <ion-label>{{getBrandName(brandId)}}</ion-label>
              </ion-chip>
            </div>
          </div>
        </div>

        <div class="brand-actions">
          <ion-button
            expand="block"
            fill="outline"
            (click)="setBrandModalOpen(false)"
            class="cancel-button">
            Cancel
          </ion-button>
          <ion-button
            expand="block"
            (click)="saveBrandChanges()"
            color="secondary"
            class="save-button">
            <ion-icon name="save-outline" slot="start"></ion-icon>
            Save Changes
          </ion-button>
        </div>
      </ion-content>
    </ng-template>
  </ion-modal>


</ion-content>

<ion-footer>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button (click)="setOpen(true)" color="primary">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>