/* Mobile-First Purchase Order Styling */
.mobile-po-content {
  --background: var(--ion-background-color);
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 80px;
}

/* Mobile Header Section */
.mobile-header-section {
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .mobile-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 56px;

    ion-label {
      font-weight: 500;
      color: #333;
    }

    ion-select, ion-input {
      --color: #333;
      font-size: 16px;
    }
  }

  .supplier-info {
    padding: 12px 16px;

    ion-chip {
      margin: 0;
    }
  }
}

/* Product Section */
.product-section {
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 16px;

    ion-icon {
      font-size: 20px;
    }
  }

  .mobile-searchbar {
    --background: #f5f5f5;
    --border-radius: 8px;
    margin-bottom: 16px;
  }
}

/* Product Grid Section - Invoice Style */
.product-grid-section {
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .sync-debug-section {
    padding: 8px 16px;
    background: #f0f8ff;
    border-bottom: 1px solid #e0e0e0;
    text-align: center;

    ion-button {
      --height: 28px;
      font-size: 11px;
      font-weight: 500;
    }
  }

  .main-content {
    --ion-grid-padding: 0;
    --ion-grid-padding-xs: 0;
    --ion-grid-padding-sm: 0;
    --ion-grid-padding-md: 0;
    --ion-grid-padding-lg: 0;
    --ion-grid-padding-xl: 0;

    .header-row {
      background: linear-gradient(135deg, #1976d2, #1565c0);
      border-bottom: 3px solid #0d47a1;
      min-height: 48px;
      position: sticky;
      top: 0;
      z-index: 10;

      .header-col {
        --ion-grid-column-padding: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid rgba(255, 255, 255, 0.2);

        &:first-child {
          justify-content: flex-start;
        }

        &:last-child {
          border-right: none;
        }

        .header-label {
          font-weight: 700;
          color: white;
          font-size: 13px;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .product-row {
      border-bottom: 1px solid #f0f0f0;
      min-height: 70px;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.has-quantity {
        background: #f0f8ff;
        border-left: 4px solid #1976d2;

        &:hover {
          background: #e3f2fd;
        }
      }

      &.crate-based {
        border-left: 3px solid #ff9800;

        &.has-quantity {
          border-left: 4px solid #1976d2;
        }
      }

      .product-col {
        --ion-grid-column-padding: 12px;
        display: flex;
        align-items: center;
        border-right: 1px solid #f0f0f0;
        cursor: pointer;

        .product-info {
          width: 100%;

          .product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;

            .product-name {
              font-weight: 700;
              color: #1976d2;
              font-size: 15px;
            }

            .product-badges {
              display: flex;
              gap: 4px;

              ion-badge {
                font-size: 9px;
                font-weight: 600;
                padding: 2px 6px;
              }
            }
          }

          .product-details {
            .product-full-name {
              display: block;
              font-weight: 500;
              color: #333;
              font-size: 13px;
              margin-bottom: 4px;
              line-height: 1.3;
            }

            .product-meta {
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: 8px;

              .product-stock {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #28a745;
                font-size: 11px;
                font-weight: 500;

                &.low-stock {
                  color: #dc3545;
                }

                ion-icon {
                  font-size: 12px;
                }
              }

              .product-rate {
                display: flex;
                align-items: center;
                gap: 4px;
                font-weight: 600;
                color: #4caf50;
                font-size: 12px;

                ion-icon {
                  font-size: 12px;
                }
              }
            }
          }

          .product-crate-info {
            margin-top: 6px;

            ion-chip {
              --background: rgba(156, 39, 176, 0.1);
              --color: #9c27b0;
              font-size: 10px;

              ion-icon {
                font-size: 12px;
                margin-right: 2px;
              }
            }
          }

          .product-total-qty {
            margin-top: 4px;

            ion-chip {
              --background: rgba(0, 150, 136, 0.1);
              --color: #009688;
              font-size: 10px;

              ion-icon {
                font-size: 12px;
                margin-right: 2px;
              }
            }
          }
        }
      }

      .input-col {
        --ion-grid-column-padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #f0f0f0;

        &:last-child {
          border-right: none;
        }

        .input-wrapper {
          width: 100%;
          position: relative;

          .quantity-input {
            --background: #f8f9fa;
            --border-radius: 8px;
            --padding-start: 8px;
            --padding-end: 8px;
            width: 100%;
            height: 40px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.2s ease;

            &:focus-within {
              --background: var(--item-background);
              border-color: #1976d2;
              box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
              transform: scale(1.02);
            }

            &.has-value {
              --background: #e3f2fd;
              border-color: #1976d2;
              color: #1976d2;
            }
          }

          .input-label {
            position: absolute;
            bottom: -16px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 9px;
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }
  }
}

/* No Supplier Message */
.no-supplier-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-icon {
    font-size: 64px;
    color: #1976d2;
    margin-bottom: 16px;
  }

  h3 {
    color: #1976d2;
    margin: 16px 0 8px 0;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-style: italic;
    color: #999;
  }
}

/* No Products Message */
.no-products-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;

  ion-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 12px;
  }

  p {
    margin: 4px 0;

    &:first-of-type {
      font-weight: 500;
    }

    small {
      font-style: italic;
      color: #999;
    }
  }
}

/* Selected Items Preview Table */
.selected-items-preview {
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    span {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #1976d2;
      flex: 1;

      ion-icon {
        font-size: 20px;
      }
    }
  }

  .preview-table {
    .table-header {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr 60px;
      gap: 8px;
      padding: 12px 16px;
      background: #f0f0f0;
      font-weight: 600;
      font-size: 12px;
      color: #333;
      text-align: center;

      .col-product {
        text-align: left;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr 60px;
      gap: 8px;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      align-items: center;

      &:hover {
        background: #f8f9fa;
      }

      .col-product {
        .product-info {
          .product-name {
            display: block;
            font-weight: 600;
            color: #333;
            font-size: 14px;
            margin-bottom: 2px;
          }

          .product-code {
            display: block;
            font-size: 12px;
            color: #666;
          }
        }
      }

      .col-qty {
        text-align: center;

        .qty-display {
          font-weight: 600;
          color: #1976d2;
        }

        // Crate-based quantity input
        .crate-qty-input {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;

          .quantity-input {
            --padding-start: 8px;
            --padding-end: 8px;
            font-size: 14px;
            text-align: center;
            min-height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
          }

          .input-label {
            font-size: 10px;
            color: #666;
            font-weight: 500;
          }
        }

        // Regular product quantity inputs
        .regular-qty-inputs {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .qty-input-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;

            .quantity-input {
              --padding-start: 6px;
              --padding-end: 6px;
              font-size: 12px;
              text-align: center;
              min-height: 28px;
              border: 1px solid #ddd;
              border-radius: 4px;
              background: #fff;
              width: 60px;
            }

            .input-label {
              font-size: 9px;
              color: #666;
              font-weight: 500;
            }
          }
        }

        // Total quantity display
        .total-qty-display {
          margin-top: 8px;
          padding-top: 6px;
          border-top: 1px solid #eee;

          .total-label {
            font-size: 10px;
            color: #666;
            display: block;
          }

          .total-value {
            font-weight: 600;
            color: #1976d2;
            font-size: 14px;
          }
        }
      }

      .col-rate {
        .rate-input {
          --background: transparent;
          --border-radius: 4px;
          --padding-start: 8px;
          --padding-end: 8px;
          height: 32px;
          font-size: 14px;
          text-align: center;
          border: 1px solid #ddd;
          border-radius: 4px;

          &:focus-within {
            --background: var(--item-background);
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
          }
        }
      }

      .col-total {
        text-align: center;

        .total-display {
          font-weight: 600;
          color: #4caf50;
        }
      }

      .col-action {
        display: flex;
        justify-content: center;
      }
    }
  }
}

/* Selected Items Section */
.selected-items-section {
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 16px;

    ion-icon {
      font-size: 20px;
    }
  }
}

/* Mobile Items List */
.mobile-items-list {
  .mobile-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 12px;
    background: var(--item-background);

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px 8px 0 0;
      border-bottom: 1px solid #e0e0e0;

      .item-info {
        flex: 1;

        .item-name {
          font-weight: 600;
          color: #333;
          font-size: 14px;
          display: block;
        }

        .item-code {
          font-size: 12px;
          color: #666;
          margin-top: 2px;
        }
      }
    }

    .item-details {
      padding: 12px;

      .detail-row {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;

        .detail-group {
          flex: 1;

          label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
            font-weight: 500;
          }

          ion-input {
            --background: #f5f5f5;
            --border-radius: 6px;
            --padding-start: 8px;
            --padding-end: 8px;
            height: 36px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 6px;
          }
        }
      }

      .item-total {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #e3f2fd;
        border-radius: 6px;

        .total-label {
          font-size: 14px;
          color: #1976d2;
          font-weight: 500;
        }

        .total-amount {
          font-size: 16px;
          font-weight: 600;
          color: #1976d2;
        }
      }
    }
  }
}

/* Order Summary */
.order-summary {
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;

  .summary-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 16px;

    ion-icon {
      font-size: 20px;
    }
  }

  .summary-content {
    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      font-size: 14px;

      &:not(:last-child) {
        border-bottom: 1px solid #f0f0f0;
      }

      &.total-row {
        font-size: 16px;
        font-weight: 600;
        color: #1976d2;
        border-top: 2px solid #e0e0e0;
        padding-top: 12px;
        margin-top: 8px;
      }
    }
  }
}

/* Additional Details */
.additional-details {
  background: var(--section-background);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .details-content {
    padding: 0 16px 16px 16px;

    ion-item {
      --padding-start: 0;
      --padding-end: 0;
      --inner-padding-end: 0;

      ion-label {
        font-weight: 500;
        color: #333;
      }

      ion-textarea {
        --background: #f5f5f5;
        --border-radius: 6px;
        --padding-start: 12px;
        --padding-end: 12px;
        --padding-top: 8px;
        --padding-bottom: 8px;
        margin-top: 8px;
        border: 1px solid #ddd;
        border-radius: 6px;
      }
    }
  }
}

// Item Cards
.item-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 0 16px 0;
  border-left: 4px solid #1976d2;

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .item-title {
      font-size: 16px;
      font-weight: 600;
      color: #1976d2;
      margin: 0;
    }
  }

  .item-totals {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin: 16px 0;

    .total-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .total-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }

      .total-value {
        font-size: 14px;
        font-weight: 600;
        color: #1976d2;
      }
    }
  }

  .stock-info {
    background: #e8f5e8;
    border-radius: 8px;
    padding: 12px;
    margin: 16px 0;

    .stock-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .stock-label {
        font-size: 12px;
        color: #4caf50;
        font-weight: 500;
      }

      .stock-value {
        font-size: 12px;
        font-weight: 600;
        color: #2e7d32;
      }
    }
  }
}

// No Items Card
.no-items-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0;

  .no-items-content {
    text-align: center;
    padding: 32px 16px;

    .no-items-icon {
      font-size: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #333;
      font-weight: 600;
    }

    p {
      margin: 0 0 24px 0;
      color: #666;
      font-size: 14px;
    }
  }
}

// Summary Card
.summary-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0;
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;

  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    &:not(:last-child) {
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    &.total-row {
      font-size: 18px;
      font-weight: 600;
      padding-top: 16px;
      border-top: 2px solid rgba(255, 255, 255, 0.3);
    }

    .summary-label {
      font-weight: 500;
    }

    .summary-value {
      font-weight: 600;
    }
  }
}

/* Mobile Footer */
ion-footer {
  ion-toolbar {
    --background: var(--header-background);
    --border-color: #e0e0e0;
    --border-width: 1px 0 0 0;
    padding: 8px 16px;

    .mobile-footer-actions {
      display: flex;
      gap: 8px;

      ion-button {
        flex: 1;
        margin: 0;
        height: 40px;
        font-weight: 600;
        font-size: 14px;

        &[fill="outline"] {
          --border-width: 2px;
          --border-color: #1976d2;
          --color: #1976d2;
        }
      }
    }
  }
}

/* Responsive Design */
@media (min-width: 768px) {
  .mobile-po-content {
    max-width: 800px;
    margin: 0 auto;
    --padding-start: 16px;
    --padding-end: 16px;
  }

  .mobile-footer-actions {
    max-width: 400px;
    margin: 0 auto;
  }

  .product-list-item {
    .quantity-inputs {
      .input-group {
        ion-input {
          width: 80px;
        }
      }
    }
  }

  .mobile-item {
    .item-details {
      .detail-row {
        .detail-group {
          ion-input {
            height: 40px;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .mobile-po-content {
    --padding-start: 4px;
    --padding-end: 4px;
  }

  .product-list-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .quantity-inputs {
      justify-content: center;

      .input-group {
        ion-input {
          width: 50px;
        }
      }
    }
  }

  .mobile-item {
    .item-details {
      .detail-row {
        flex-direction: column;
        gap: 12px;
      }
    }
  }
}

/* Form Validation */
ion-item.ion-invalid.ion-touched {
  --border-color: #f44336;
  --highlight-color-focused: #f44336;

  ion-label {
    color: #f44336;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-16 {
  margin-bottom: 16px;
}

/* Search Section - Invoice Style */
.search-section {
  background: var(--section-background);
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;

  ion-searchbar {
    --background: #f5f5f5;
    --border-radius: 8px;
    --box-shadow: none;
    --color: #333;
    --placeholder-color: #666;
    --icon-color: #666;
    --clear-button-color: #666;
    margin: 0;
    padding: 0;
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: #666;

    p {
      margin: 0 0 12px 0;
      font-size: 14px;
    }

    ion-button {
      --height: 32px;
      font-size: 12px;
    }
  }
}

/* Sticky Summary - Invoice Style */
.sticky-summary {
  background: var(--section-background);
  border-top: 1px solid #e0e0e0;
  padding: 12px 16px;

  .summary-row {
    margin: 0;

    .summary-col {
      padding: 4px 8px;

      .summary-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .summary-icon {
          font-size: 16px;
          color: #1976d2;

          &.tax-icon {
            color: #ff9800;
          }

          &.net-icon {
            color: #4caf50;
          }
        }

        .summary-content {
          text-align: center;
          flex: 1;

          .summary-label {
            display: block;
            font-size: 10px;
            color: #666;
            margin-bottom: 2px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
          }

          .summary-value {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #333;

            &.highlight {
              color: #4caf50;
              font-size: 15px;
              font-weight: 700;
            }
          }
        }

        // Net amount styling
        &.net-amount {
          background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
          border-radius: 8px;
          padding: 8px;
          border: 1px solid #c8e6c9;
        }
      }
    }
  }

  // Statistics row styling
  .stats-row {
    .summary-col:not(:last-child) {
      border-right: 1px solid #f0f0f0;
    }
  }

  // Financial row styling
  .financial-row {
    border-top: 1px solid #f0f0f0;
    padding-top: 8px;
    margin-top: 8px;

    .summary-col {
      .summary-item {
        .summary-content {
          .summary-label {
            color: #1976d2;
          }

          .summary-value {
            color: #1976d2;
            font-weight: 700;
          }
        }
      }
    }
  }
}

/* Mobile Footer Actions */
.mobile-footer-actions {
  display: flex;
  gap: 12px;
  padding: 16px;

  ion-button {
    flex: 1;
    height: 44px;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0.5px;

    &[fill="outline"] {
      --border-width: 2px;
      --border-color: #1976d2;
      --color: #1976d2;

      &:hover {
        --background: rgba(25, 118, 210, 0.04);
      }
    }

    &[color="primary"] {
      --background: #1976d2;
      --background-hover: #1565c0;
      --background-activated: #0d47a1;
    }

    ion-icon {
      margin-right: 8px;
    }
  }
}



.order-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.order-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}

.status-pending {
  color: var(--ion-color-warning);
}

.status-approved {
  color: var(--ion-color-success);
}

.status-primary {
  color: var(--ion-color-primary);
}