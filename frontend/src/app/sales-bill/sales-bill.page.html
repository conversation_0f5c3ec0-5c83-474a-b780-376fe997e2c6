<app-header [title]="'sales.title' | translate" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="sales-bill-content">
  <!-- Collapsible Summary Section - Moved to Top -->
  <div class="summary-section" *ngIf="data && data.length > 0">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        {{ 'sales.dailySummary' | translate }}
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card bills-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Total Bills</h3>
                <h1>{{data.length}}</h1>
                <p>Invoices processed</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="receipt" class="summary-icon bills"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card amount-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Bill Amount</h3>
                <h1>{{bill_amount_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Total billed amount</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="cash" class="summary-icon amount"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card received-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Received</h3>
                <h1>{{received_amount_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Amount collected</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="checkmark-circle" class="summary-icon received"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card outstanding-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Outstanding</h3>
                <h1>{{current_balance_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Pending collection</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="time" class="summary-icon outstanding"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    </div>
  </div>

  <!-- Route Summary Section -->
  <div class="route-summary-section" *ngIf="routeSummaries && routeSummaries.length > 0">
    <div class="summary-header" (click)="toggleRouteSummary()">
      <h3 class="section-title">
        <ion-icon name="map-outline" class="section-icon"></ion-icon>
        Route Summary - {{currentWeekday | titlecase}}
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showRouteSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="route-summary-content" [class.expanded]="showRouteSummary">
      <ion-card *ngFor="let routeSummary of routeSummaries" class="route-card">
        <ion-card-header>
          <div class="route-header">
            <div class="route-info">
              <ion-card-title>{{routeSummary.route_name}}</ion-card-title>
              <ion-card-subtitle>{{routeSummary.date | date:'dd/MM/yyyy'}}</ion-card-subtitle>
            </div>
            <div class="route-status">
              <ion-chip [color]="getCompletionColor(routeSummary.completion_percentage)" size="small">
                <ion-label>{{routeSummary.completion_percentage | number:'1.0-1'}}%</ion-label>
              </ion-chip>
            </div>
          </div>
        </ion-card-header>

        <ion-card-content>
          <div class="route-stats">
            <div class="stat-item">
              <ion-icon name="storefront-outline" class="stat-icon"></ion-icon>
              <div class="stat-info">
                <span class="stat-label">Total Shops</span>
                <span class="stat-value">{{routeSummary.total_shops}}</span>
              </div>
            </div>

            <div class="stat-item">
              <ion-icon name="checkmark-circle-outline" class="stat-icon success"></ion-icon>
              <div class="stat-info">
                <span class="stat-label">Billed</span>
                <span class="stat-value">{{routeSummary.billed_shops}}</span>
              </div>
            </div>

            <div class="stat-item">
              <ion-icon name="time-outline" class="stat-icon warning"></ion-icon>
              <div class="stat-info">
                <span class="stat-label">Pending</span>
                <span class="stat-value">{{routeSummary.unbilled_shops}}</span>
              </div>
            </div>

            <div class="stat-item">
              <ion-icon name="cash-outline" class="stat-icon primary"></ion-icon>
              <div class="stat-info">
                <span class="stat-label">Amount</span>
                <span class="stat-value">{{routeSummary.total_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
              </div>
            </div>
          </div>

          <div class="route-actions">
            <ion-button
              fill="clear"
              size="small"
              (click)="viewUnbilledShops($event, routeSummary)"
              [disabled]="routeSummary.unbilled_shops === 0">
              <ion-icon name="list-outline" slot="start"></ion-icon>
              View Unbilled ({{routeSummary.unbilled_shops}})
            </ion-button>

            <ion-button
              fill="clear"
              size="small"
              (click)="refreshRouteSummary(routeSummary)">
              <ion-icon name="refresh-outline" slot="start"></ion-icon>
              Refresh
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>

  <!-- Collapsible Filter Section -->
  <div class="filter-section">
    <div class="filter-header" (click)="toggleFilters()">
      <h3 class="section-title">
        <ion-icon name="filter-outline" class="section-icon"></ion-icon>
        Filter & Options
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showFilters ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="filter-content" [class.expanded]="showFilters">
      <!-- Filter Controls -->
      <div class="filter-controls">
        <ion-row>
          <ion-col size="8">
            <ion-item class="date-filter-item" lines="none">
              <ion-label position="stacked">Select Date</ion-label>
              <ion-input
                [(ngModel)]="date"
                [value]="date | date : 'YYYY-MM-dd'"
                placeholder="Enter Date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>

          <ion-col size="4">
            <ion-button
              fill="solid"
              expand="block"
              class="filter-button"
              (click)="filterData()">
              <ion-icon name="search" slot="start"></ion-icon>
              Filter
            </ion-button>
          </ion-col>
        </ion-row>
      </div>

      <!-- Options Row -->
      <div class="options-row">
        <ion-row>
          <ion-col size="6">
            <ion-item lines="none" class="option-item">
              <ion-icon name="phone-portrait-outline" slot="start" class="option-icon"></ion-icon>
              <ion-label>Mobile Printing</ion-label>
              <ion-toggle [(ngModel)]="mobile" slot="end"></ion-toggle>
            </ion-item>
          </ion-col>

          <ion-col size="6">
            <ion-button
              fill="outline"
              expand="block"
              class="action-button"
              (click)="routerService.routerFunction('create-invoice')">
              <ion-icon name="add-circle-outline" slot="start"></ion-icon>
              {{ 'sales.createInvoice' | translate }}
            </ion-button>
          </ion-col>

          <ion-col size="6">
            <ion-button
              fill="solid"
              expand="block"
              class="scanner-button"
              (click)="openBarcodeScanner()">
              <ion-icon name="scan-outline" slot="start"></ion-icon>
              Scan Invoice
            </ion-button>
          </ion-col>
        </ion-row>
      </div>
    </div>
  </div>
  <!-- No Data Message -->
  <ion-item lines="none" *ngIf="!data || data.length === 0" class="no-data-item">
    <ion-text color="medium">
      <h6>No sales invoices found for the selected date.</h6>
    </ion-text>
  </ion-item>

  <!-- Sales Invoice List -->
  <div class="invoice-list">
    <ion-card
      *ngFor="let invoice of data"
      class="invoice-card">

      <!-- Invoice Content -->
      <ion-card-content class="invoice-content" (click)="editInvoice(invoice.id)">
        <div class="invoice-header">
          <!-- Invoice Icon -->
          <div class="invoice-icon">
            <ion-icon name="receipt-outline" class="receipt-icon"></ion-icon>
          </div>

          <!-- Invoice Details -->
          <div class="invoice-details">
        <h2 class="customer-name">{{invoice.name}}</h2>

        <!-- Invoice ID and Date -->
        <div class="invoice-meta">
          <span class="invoice-id">INV-{{invoice.id}}</span>
          <span class="invoice-date">{{invoice.date | date:'dd/MM/yyyy'}}</span>
        </div>

        <!-- Amount Details -->
        <div class="amount-details">
          <div class="amount-row">
            <span class="amount-label">Bill Amount:</span>
            <span class="amount-value bill-amount">{{invoice.bill_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
          </div>
          <div class="amount-row">
            <span class="amount-label">Received:</span>
            <span class="amount-value received-amount">{{invoice.received_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
          </div>
          <div class="amount-row">
            <span class="amount-label">Balance:</span>
            <span class="amount-value" [ngClass]="{'balance-positive': invoice.current_balance > 0, 'balance-zero': invoice.current_balance === 0}">
              {{invoice.current_balance | currency: 'INR':'symbol':'1.0-0'}}
            </span>
          </div>
          <!-- Payment Status -->
          <div class="amount-row">
            <span class="amount-label">Status:</span>
            <ion-badge [color]="getPaymentStatusColor(getPaymentStatus(invoice))" class="payment-status-badge">
              {{getPaymentStatusText(getPaymentStatus(invoice))}}
            </ion-badge>
          </div>
        </div>
          </div>
        </div>
      </ion-card-content>

      <!-- Action Buttons - Horizontal Layout at Bottom -->
      <div class="invoice-actions-footer">
        <!-- Payment Receipt Button - Only show if there's outstanding balance -->
        <ion-button
          fill="clear"
          size="small"
          class="action-button payment-button"
          (click)="openPaymentModal(invoice); $event.stopPropagation()"
          title="Add Payment Receipt">
          <ion-icon name="card-outline" slot="icon-only"></ion-icon>
        </ion-button>





        <ion-button
          fill="clear"
          size="small"
          class="action-button print-button"
          (click)="printInvoice(invoice); $event.stopPropagation()"
          title="Print Invoice">
          <ion-icon name="print-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button receipt-button"
          (click)="printCollectionReceipt(invoice); $event.stopPropagation()"
          title="Print Collection Receipt">
          <ion-icon name="receipt-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button edit-button"
          (click)="editInvoice(invoice.id); $event.stopPropagation()"
          title="Edit Invoice">
          <ion-icon name="create-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button delivery-button"
          (click)="deliveryInvoice(invoice.id); $event.stopPropagation()"
          title="Mark as Delivered">
          <ion-icon name="checkmark-circle-outline" slot="icon-only"></ion-icon>
        </ion-button>



        <ion-button
          fill="clear"
          size="small"
          class="action-button image-button"
          (click)="openImageModal(invoice); $event.stopPropagation()"
          title="Upload Images">
          <ion-icon name="images-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button delete-button"
          (click)="delete(invoice); $event.stopPropagation()"
          title="Delete Invoice">
          <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
        </ion-button>
      </div>
    </ion-card>
  </div>
</ion-content>

<!-- Photo Upload Footer -->
<app-photo-upload-footer
  [date]="date"
  [routeSummaries]="routeSummaries">
</app-photo-upload-footer>



<!-- Payment Receipt Modal -->
<app-payment-receipt-modal
  [isOpen]="isPaymentModalOpen"
  [invoiceData]="selectedInvoiceForPayment"
  (modalClosed)="closePaymentModal()"
  (paymentCreated)="onPaymentCreated($event)">
</app-payment-receipt-modal>

<ion-footer>
  <app-floating-menu></app-floating-menu>
</ion-footer>
