import { RouteService } from "./../shared/services/route.service";
import { Component, OnInit } from "@angular/core";
import { Nav<PERSON><PERSON>roller, <PERSON>u<PERSON>ontroller, ModalController } from "@ionic/angular";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../shared/services/alert.service";
import { IonLoaderService } from "../shared/services/ion-loader.service";
import { SalesInvoiceService } from "../shared/services/sales-invoice.service";
import { PaymentReceiptService } from "../shared/services/payment-receipt.service";
import { ToastService } from "../shared/services/toast.service";
import * as moment from "moment";
import { SocialSharing } from '@awesome-cordova-plugins/social-sharing/ngx';
import { PrintServiceService } from "../shared/services/print-service.service";
import { UtilService } from "../shared/services/util.service";
import { Location } from '@angular/common';
import { BarcodeScannerService } from "../shared/services/barcode-scanner.service";
import { BarcodeScannerModalComponent } from "../shared/components/barcode-scanner-modal/barcode-scanner-modal.component";
import { BillPreviewModalComponent } from "../shared/components/bill-preview-modal/bill-preview-modal.component";
import { RouteBillingService, RouteBillingSummary } from '../shared/services/route-billing.service';
import { BuyerService } from '../shared/services/buyer.service';
import { UnbilledShop } from '../shared/components/unbilled-shops-popover/unbilled-shops-popover.component';
import { UnbilledShopsModalComponent } from '../shared/components/unbilled-shops-modal/unbilled-shops-modal.component';
import { TranslationService } from '../shared/services/translation.service';
import { InvoiceImageModalComponent } from '../shared/components/invoice-image-modal/invoice-image-modal.component';


@Component({
    selector: "app-sales-bill",
    templateUrl: "./sales-bill.page.html",
    styleUrls: ["./sales-bill.page.scss"],
})
export class SalesBillPage implements OnInit {
    product_data: any;
    data: any;
    date: any;
    previous_balance_total: any;
    bill_amount_total: any;
    receivable_amount_total: any;
    received_amount_total: any;
    current_balance_total: any;
    mobile: boolean = localStorage.getItem('deviceId') != null && this.utilService.isAndroid();
    showFilters: boolean = false;
    showSummary: boolean = false;

    // Payment receipt modal properties
    isPaymentModalOpen: boolean = false;
    selectedInvoiceForPayment: any = null;

    // Route billing summary properties
    routeSummaries: RouteBillingSummary[] = [];
    showRouteSummary: boolean = false;
    selectedRoute: any = null;
    routes: any[] = [];
    currentWeekday: string = '';


    constructor(
        private api: SalesInvoiceService,
        private paymentReceiptService: PaymentReceiptService,
        private toast: ToastService,
        public ionLoaderService: IonLoaderService,
        public alertService: AlertService,
        public nav: NavController,
        public routerService: RouteService,
        private socialSharing: SocialSharing,
        private printerService: PrintServiceService,
        private utilService: UtilService,
        private menuController: MenuController,
        private location: Location,
        private modalController: ModalController,
        private barcodeScanner: BarcodeScannerService,
        private routeBillingService: RouteBillingService,
        private buyerService: BuyerService,
        public translate: TranslationService,
        private route: ActivatedRoute
    ) {
        this.date = moment().format("YYYY-MM-DD");
        this.currentWeekday = moment().format('dddd').toLowerCase();
    }

    ngOnInit() { }
    
    ionViewWillEnter() {
        // Check if we need to force refresh (coming from create/edit invoice)
        this.route.queryParams.subscribe(params => {
            if (params['refresh']) {
                // Force refresh when coming back from create/edit invoice
                console.log('Forcing refresh of sales bill data');
                this.loadAllData();
            } else {
                // Normal load
                this.loadAllData();
            }
        });
    }

    async loadAllData() {
        await Promise.all([
            this.getData(this.date),
            this.loadRouteSummaries(),
            this.loadRoutes()
        ]);
    }
    filterData() {
        this.getData(this.date);
    }

    toggleFilters() {
        this.showFilters = !this.showFilters;
    }

    toggleSummary() {
        this.showSummary = !this.showSummary;
    }
    async getData(date) {
        try {
            const result = await this.ionLoaderService.withLoader(async () => {
                return await this.api.getSalesInvoice(date);
            }, 'Loading sales data...') as any;

            if (result.success) {
                console.log(result);
                this.toast.toastServices(result.message, "success", "top");
                this.data = result.data;
                this.getTotal();
            } else {
                this.toast.toastServices(result.message, "danger", "top");
            }
        } catch (err: any) {
            this.toast.toastServices(err.message || 'Failed to load sales data', "danger", "top");
            console.error('Error loading sales data:', err);
        }
    }
    async getTotal() {
        this.previous_balance_total = 0;
        this.bill_amount_total = 0;
        this.receivable_amount_total = 0;
        this.received_amount_total = 0;
        this.current_balance_total = 0;
        this.data.forEach((element) => {
            this.previous_balance_total += element.previous_balance;
            this.bill_amount_total += element.bill_amount;
            this.receivable_amount_total += element.receivable_amount;
            this.received_amount_total += element.received_amount;
            this.current_balance_total += element.current_balance;
        });

    }
    editInvoice(id: number) {
        this.nav.navigateForward(`/edit-invoice?id=${id}`);
    }
    delete(data) {
        this.alertService
            .alertConfirm(
                "Alert",
                "Do you want to delete the invoice !!!",
                "Yes",
                "No"
            )
            .then((res) => {
                if (res) {
                    this.deleteSalesInvoice(data);
                }
            });
    }
    async sendMessage() {
        console.log("HI hello");

        this.socialSharing.shareViaWhatsAppToPhone('+919629174525', 'Test Message via WhatsApp', null /* img */, null /* url */).then((res: any) => {
            // Sharing via email is possible
            console.log(res);

        }).catch(() => {
            // Sharing via email is not possible
        });
    }
    async deleteSalesInvoice(data) {
        try {
            const result = await this.ionLoaderService.withLoader(async () => {
                return await this.api.deleteSalesInvoice({ id: data });
            }, 'Deleting invoice...') as any;

            if (result.success) {
                console.log(result);
                this.toast.toastServices(result.message, "success", "top");
                this.getData(this.date);
            } else {
                this.toast.toastServices(result.message, "danger", "top");
            }
        } catch (err: any) {
            this.toast.toastServices(err.message || 'Failed to delete invoice', "danger", "top");
            console.error('Error deleting invoice:', err);
        }
    }
    async deliveryInvoice(invoice_id) {
        try {
            const result = await this.ionLoaderService.withLoader(async () => {
                return await this.api.changeInvoiceType(invoice_id);
            }, 'Updating invoice...') as any;

            if (result.success) {
                console.log(result);
                this.toast.toastServices(result.message, "success", "top");
                this.getData(this.date);
            } else {
                this.toast.toastServices(result.message, "danger", "top");
            }
        } catch (err: any) {
            this.toast.toastServices(err.message || 'Failed to update invoice', "danger", "top");
            console.error('Error updating invoice:', err);
        }
    }

    printInvoice(data) {
        this.printerService.print(data, this.mobile, data.name + " - " + data.date);
        // localStorage.getItem('deviceId') != null && this.mobile ? this.printerService.printBillSlip(data) : this.printerService.printDocument(htmlContent, )
    }

    printCollectionReceipt(data) {
        this.printerService.printCollectionReceipt(data);
    }

    // Barcode Scanner Methods
    async openBarcodeScanner() {
        try {
            const modal = await this.modalController.create({
                component: BarcodeScannerModalComponent,
                cssClass: 'barcode-scanner-modal'
            });

            await modal.present();

            const { data } = await modal.onWillDismiss();

            if (data && data.success) {
                await this.handleScannedBarcode(data);
            }
        } catch (error) {
            console.error('Error opening barcode scanner:', error);
            this.toast.toastServices('Failed to open barcode scanner', 'danger', 'top');
        }
    }

    async handleScannedBarcode(scanResult: any) {
        try {
            const { data: parsedData, rawData } = scanResult;

            if (parsedData && parsedData.invoiceId) {
                // Open bill preview modal with scanned invoice
                await this.openBillPreview(parsedData.invoiceId, rawData);
            } else {
                this.toast.toastServices('Invalid barcode format', 'danger', 'top');
            }
        } catch (error) {
            console.error('Error handling scanned barcode:', error);
            this.toast.toastServices('Failed to process scanned barcode', 'danger', 'top');
        }
    }

    async openBillPreview(invoiceId: number, barcodeData: string) {
        try {
            const modal = await this.modalController.create({
                component: BillPreviewModalComponent,
                componentProps: {
                    invoiceId: invoiceId,
                    barcodeData: barcodeData
                },
                cssClass: 'bill-preview-modal'
            });

            await modal.present();
        } catch (error) {
            console.error('Error opening bill preview:', error);
            this.toast.toastServices('Failed to open invoice preview', 'danger', 'top');
        }
    }



    // Payment Receipt Methods
    openPaymentModal(invoice: any) {
        this.selectedInvoiceForPayment = invoice;
        this.isPaymentModalOpen = true;
    }

    closePaymentModal() {
        this.isPaymentModalOpen = false;
        this.selectedInvoiceForPayment = null;
    }

    onPaymentCreated(paymentData: any) {
        // Refresh the invoice data to show updated payment status
        this.getData(this.date);
        this.toast.toastServices('Payment receipt created successfully', 'success', 'top');
    }



    getPaymentStatus(invoice: any): string {
        const billAmount = invoice.bill_amount || 0;
        const receivedAmount = invoice.received_amount || 0;
        const outstandingBalance = billAmount - receivedAmount;

        if (outstandingBalance <= 0) {
            return 'paid';
        } else if (receivedAmount > 0) {
            return 'partial';
        } else {
            return 'unpaid';
        }
    }

    getPaymentStatusColor(status: string): string {
        switch (status) {
            case 'paid':
                return 'success';
            case 'partial':
                return 'warning';
            case 'unpaid':
                return 'danger';
            default:
                return 'medium';
        }
    }

    getPaymentStatusText(status: string): string {
        switch (status) {
            case 'paid':
                return 'Paid';
            case 'partial':
                return 'Partial';
            case 'unpaid':
                return 'Unpaid';
            default:
                return 'Unknown';
        }
    }

    // Navigation methods
    toggleMenu() {
        this.menuController.toggle();
    }

    goBack() {
        this.location.back();
    }

    // Route Summary Methods
    async loadRouteSummaries() {
        try {
            const response: any = await this.routeBillingService.getWeekdayRouteSummaries(this.currentWeekday, this.date);
            if (response.success) {
                this.routeSummaries = response.data;
            } else {
                this.toast.toastServices('Failed to load route summaries', 'danger', 'top');
            }
        } catch (error) {
            console.error('Error loading route summaries:', error);
            this.toast.toastServices('Error loading route summaries', 'danger', 'top');
        }
    }

    async loadRoutes() {
        try {
            const response: any = await this.buyerService.getRoute();
            if (response.success) {
                this.routes = response.data;
            }
        } catch (error) {
            console.error('Error loading routes:', error);
        }
    }

    toggleRouteSummary() {
        this.showRouteSummary = !this.showRouteSummary;
    }

    async refreshRouteSummary(route: any) {
        try {
            const response = await this.ionLoaderService.withLoader(async () => {
                return await this.routeBillingService.refreshBillingSummary(route.route, this.date);
            }, 'Refreshing route summary...') as any;
            
            if (response.success) {
                await this.loadRouteSummaries();
                this.toast.toastServices('Route summary refreshed', 'success', 'top');
            } else {
                this.toast.toastServices('Failed to refresh route summary', 'danger', 'top');
            }
        } catch (error) {
            console.error('Error refreshing route summary:', error);
            this.toast.toastServices('Error refreshing route summary', 'danger', 'top');
        }
    }

    getCompletionColor(percentage: number): string {
        if (percentage >= 100) return 'success';
        if (percentage >= 75) return 'warning';
        if (percentage >= 50) return 'primary';
        return 'danger';
    }





    async viewUnbilledShops(event: Event, routeSummary: RouteBillingSummary) {
        try {
            const response = await this.routeBillingService.getUnbilledShops(routeSummary.route, this.date);

            if (response.success) {
                const unbilledShops: UnbilledShop[] = response.data.map(shop => ({
                    id: shop.id,
                    name: shop.name,
                    phone_no: shop.phone_no,
                    place: shop.place,
                    route_name: routeSummary.route_name,
                    route_id: routeSummary.route,
                    expected_amount: shop.expected_amount || 0,
                    last_billed_date: shop.last_billed_date
                }));

                const modal = await this.modalController.create({
                    component: UnbilledShopsModalComponent,
                    componentProps: {
                        shops: unbilledShops,
                        routeName: routeSummary.route_name,
                        date: this.date
                    },
                    cssClass: 'unbilled-shops-modal'
                });

                await modal.present();

                // Handle modal events
                const { data } = await modal.onWillDismiss();
                if (data && data.action) {
                    await this.handleUnbilledShopAction(data);
                }
            } else {
                this.toast.toastServices('All shops in this route have been billed', 'success', 'top');
            }
        } catch (error) {
            console.error('Error loading unbilled shops:', error);
            this.toast.toastServices('Error loading unbilled shops', 'danger', 'top');
        }
    }

    async handleUnbilledShopAction(data: any) {
        switch (data.action) {
            case 'shop_selected':
            case 'create_invoice':
                // Navigate to create invoice for the selected shop
                this.nav.navigateForward(`/create-invoice?buyer_id=${data.shop.id}`);
                break;
            case 'call_shop':
                // Handle phone call
                if (data.shop.phone_no) {
                    window.open(`tel:${data.shop.phone_no}`, '_system');
                }
                break;
            case 'view_all_sales_bill':
                // Refresh the current page to show all sales bills
                await this.loadAllData();
                break;
            default:
                break;
        }
    }

    async openImageModal(invoice: any) {
        try {
            const modal = await this.modalController.create({
                component: InvoiceImageModalComponent,
                componentProps: {
                    invoiceId: invoice.id,
                    invoiceData: invoice
                },
                cssClass: 'invoice-image-modal'
            });

            await modal.present();
        } catch (error) {
            console.error('Error opening image modal:', error);
            this.toast.toastServices('Failed to open image upload modal', 'danger', 'top');
        }
    }
}
