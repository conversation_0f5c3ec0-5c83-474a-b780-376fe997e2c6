/* Sales Bill Page Styling */
.sales-bill-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}

/* Collapsible Summary Section - Now at Top */
.summary-section {
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--card-background, #ffffff) 0%, var(--content-background, #f8f9fa) 100%);
  border-bottom: 1px solid var(--ion-color-step-200, #e0e0e0);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.summary-header:hover {
  background: var(--ion-color-step-50, #f8f9fa);
}

.summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.summary-content.expanded {
  max-height: 500px;
  padding: 16px;
}

/* Collapsible Filter Section */
.filter-section {
  margin: 0 16px 16px 16px;
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--card-background, #ffffff) 0%, var(--content-background, #f8f9fa) 100%);
  border-bottom: 1px solid var(--ion-color-step-200, #e0e0e0);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.filter-header:hover {
  background: var(--ion-color-step-50, #f8f9fa);
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.filter-content.expanded {
  max-height: 300px;
  padding: 16px;
}

/* Section Header */
.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Filter Controls Container */
.filter-controls {
  margin-bottom: 12px;
}

.date-filter-item {
  --background: transparent;
  --border-radius: 12px;
  margin-bottom: 8px;
}

.filter-button {
  --border-radius: 12px;
  height: 44px;
  margin-top: 20px;
}

/* Options Row */
.options-row {
  margin-top: 12px;
}

.option-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
}

.option-icon {
  color: var(--ion-color-medium);
  margin-right: 8px;
}

.action-button {
  --border-radius: 12px;
  height: 44px;
}

.scanner-button {
  --background: var(--ion-color-secondary);
  --color: white;
  --border-radius: 12px;
  height: 44px;

  &:hover {
    --background: var(--ion-color-secondary-shade);
  }
}

.date-filter-item {
  --background: var(--item-background);
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 12px;
  --inner-padding-end: 12px;
  --border-radius: 12px;
  --border-color: #e0e0e0;
  --border-style: solid;
  --border-width: 1px;
  margin: 0;
  flex: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.date-filter-item ion-label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-bottom: 4px;
}

.date-input {
  --background: transparent;
  --color: #333;
  --padding-start: 0;
  --padding-end: 0;
  font-size: 14px;
  font-weight: 500;
}

.filter-button {
  --border-radius: 12px;
  --background: linear-gradient(135deg, var(--ion-color-primary) 0%, #1976d2 100%);
  --color: white;
  --box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
  height: 48px;
  min-width: 100px;
  font-size: 14px;
  font-weight: 600;
  text-transform: none;
  margin: 0;
  transition: all 0.3s ease;
}

.filter-button:hover {
  --box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
  transform: translateY(-1px);
}

.filter-button ion-icon {
  margin-right: 6px;
}

/* Mobile Toggle Section */
.mobile-toggle-row {
  margin: 0;
}

.mobile-toggle-item {
  --background: var(--item-background);
  --padding-start: 12px;
  --padding-end: 12px;
  --inner-padding-start: 12px;
  --inner-padding-end: 12px;
  --border-radius: 12px;
  --border-color: #e0e0e0;
  --border-style: solid;
  --border-width: 1px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.mobile-toggle-item ion-label {
  font-weight: 600;
  color: #666;
}

.mobile-toggle-item ion-toggle {
  --background: #e0e0e0;
  --background-checked: var(--ion-color-primary);
  --handle-background: var(--item-background);
  --handle-background-checked: var(--item-background);
}

/* No Data Item */
.no-data-item {
  --background: var(--item-background);
  --padding-start: 16px;
  --padding-end: 16px;
  margin: 16px;
  border-radius: 8px;
}

/* Invoice List */
.invoice-list {
  padding: 8px 16px;
  margin-bottom: 80px; /* Space for floating menu */
}

/* Invoice Card */
.invoice-card {
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.invoice-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.invoice-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 12px;
}

.invoice-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* Invoice Icon */
.invoice-icon {
  width: 60px;
  height: 60px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e3f2fd;
  border-radius: 12px;
}

.receipt-icon {
  font-size: 28px;
  color: #1976d2;
}

/* Invoice Details */
.invoice-details {
  flex: 1;
  margin: 0;
}

.customer-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.invoice-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 6px 0 12px 0;
  font-size: 13px;
}

.invoice-id {
  color: #007bff;
  font-weight: 600;
  background: #e7f3ff;
  padding: 3px 8px;
  border-radius: 12px;
}

.invoice-date {
  color: #666;
  font-weight: 500;
}

.amount-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.amount-label {
  color: #666;
  font-weight: 500;
}

.amount-value {
  font-weight: 600;
  color: #333;
}

.bill-amount {
  color: #2196f3;
}

.received-amount {
  color: #4caf50;
}

.balance-positive {
  color: #f44336;
}

.balance-zero {
  color: #666;
}

/* Invoice Actions - Horizontal Footer */
.invoice-actions-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  gap: 8px;
  flex-wrap: wrap;
}

/* Legacy Invoice Actions (if still used) */
.invoice-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 50px;
}

.action-button {
  --border-radius: 8px;
  --padding-start: 8px;
  --padding-end: 8px;
  // width: 40px;
  height: 40px;
}

.preview-button {
  --color: #9c27b0;
  --background: #f3e5f5;
}

.test-preview-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.print-button {
  --color: #2196f3;
  --background: #e3f2fd;
}

.receipt-button {
  --color: #9c27b0;
  --background: #f3e5f5;
}



.edit-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.delivery-button {
  --color: #4caf50;
  --background: #e8f5e8;
}

.image-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.delete-button {
  --color: #f44336;
  --background: #ffebee;
}

.payment-button {
  --color: #ff9800;
  --background: #fff3e0;
}

// Payment status badge styling
.payment-status-badge {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
}



.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.bills {
  color: #6f42c1;
}

.summary-icon.amount {
  color: #007bff;
}

.summary-icon.received {
  color: #28a745;
}

.summary-icon.outstanding {
  color: #dc3545;
}

.bills-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.amount-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

.received-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.outstanding-card {
  background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
}

/* Responsive Design */
@media (max-width: 576px) {
  .filter-section {
    padding: 12px;
  }

  .section-title {
    font-size: 16px;
  }

  .section-icon {
    font-size: 18px;
  }

  .filter-button,
  .action-button {
    height: 40px;
    font-size: 12px;
  }

  .summary-card-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }
}

@media (min-width: 768px) {
  .filter-section {
    padding: 20px;
  }

  .section-title {
    font-size: 20px;
  }

  .section-icon {
    font-size: 22px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }
}

@media (min-width: 1024px) {
  .filter-section {
    padding: 24px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 24px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-section {
    margin: 0 12px 12px 12px;
  }

  .filter-header {
    padding: 12px;
  }

  .filter-content.expanded {
    padding: 12px;
  }

  .invoice-list {
    padding: 8px 12px;
  }

  .no-data-item {
    margin: 12px;
  }

  .invoice-card {
    margin-bottom: 8px;
  }

  .invoice-content {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 12px;
    --padding-bottom: 8px;
  }

  .invoice-actions-footer {
    padding: 8px 12px;
    gap: 6px;
  }

  .invoice-icon {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .receipt-icon {
    font-size: 24px;
  }

  .customer-name {
    font-size: 16px;
  }

  .invoice-meta {
    font-size: 12px;
    gap: 8px;
  }

  .amount-row {
    font-size: 13px;
  }

  .invoice-actions {
    gap: 6px;
    min-width: 45px;
  }

  .action-button {
    // width: 36px;
    height: 36px;
  }

  .summary-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .summary-item {
    padding: 10px;
  }

  .summary-value {
    font-size: 14px;
  }

  .summary-section {
    padding: 12px;
  }
}

/* Route Summary Section */
.route-summary-section {
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.route-summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.route-summary-content.expanded {
  max-height: 800px;
  padding: 16px;
}

.route-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.route-info {
  flex: 1;
}

.route-status {
  margin-left: 16px;
}

.route-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  font-size: 20px;
  color: var(--ion-color-medium);

  &.success {
    color: var(--ion-color-success);
  }

  &.warning {
    color: var(--ion-color-warning);
  }

  &.primary {
    color: var(--ion-color-primary);
  }
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 12px;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.route-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}



/* Responsive design for route stats */
@media (max-width: 768px) {
  .route-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-item {
    padding: 10px;
  }

  .stat-value {
    font-size: 14px;
  }
}

/* Unbilled Shops Modal Styles */
.unbilled-shops-modal {
  --height: 90%;
  --max-height: 800px;
  --border-radius: 16px 16px 0 0;
}

/* Shop Image Upload Modal Styles */
.shop-image-upload-modal {
  --height: 85%;
  --max-height: 700px;
  --border-radius: 16px 16px 0 0;
}

@media (max-width: 768px) {
  .unbilled-shops-modal {
    --height: 95%;
    --border-radius: 12px 12px 0 0;
  }

  .shop-image-upload-modal {
    --height: 90%;
    --border-radius: 12px 12px 0 0;
  }
}

/* Photo Upload Footer Spacing */
.sales-bill-content {
  padding-bottom: 100px; // Space for photo upload footer
}

.invoice-list {
  padding-bottom: 120px; // Extra space for photo upload footer
}

.bill-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.status-pending {
  color: var(--ion-color-warning);
}

.status-approved {
  color: var(--ion-color-success);
}

.status-rejected {
  color: var(--ion-color-danger);
}

.status-draft {
  color: var(--ion-color-medium);
}

.status-completed {
  color: var(--ion-color-primary);
}