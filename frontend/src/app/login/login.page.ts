import { AuthenticationService } from './../shared/services/authentication.service';
import { Component, OnInit } from '@angular/core';
import { MenuController } from '@ionic/angular';
import { LoadingController, ToastController } from '@ionic/angular';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { ToastService } from '../shared/services/toast.service';

import { TranslationService } from '../shared/services/translation.service';


@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {

  constructor(
    public loadingController: LoadingController,
    private api: AuthenticationService,
    private fb: FormBuilder,
    private router: Router,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,

    private translationService: TranslationService
  ) { }

  loginError: boolean = false;
  myForm: FormGroup;

  async ngOnInit() {
    this.myForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
    });
    const tok = localStorage.getItem('token');
  }
  async onSubmit(form: FormGroup) {
    form.markAllAsTouched();
    if (form.valid) {
      await this.ionLoaderService.startLoader().then(async () => {
        await this.api
          .login({ username: form.value.username, password: form.value.password })
          .then(async (res: any) => {
            if (res.success == true) {
              this.toast.toastServices('Successfully Logged In.', 'success', 'top')
              this.ionLoaderService.dismissLoader();
              let company = res.user.company ? res.user.company : res.user;
              localStorage.setItem('token', res.token);
              localStorage.setItem('contact_no_left', company.contact_no_left);
              localStorage.setItem('contact_no_right', company.contact_no_right);
              localStorage.setItem('address', company.address);
              localStorage.setItem('company_name', company.company_name);
              localStorage.setItem('company_logo', company.company_logo);
              localStorage.setItem('gst_no', company.gst_no);
              localStorage.setItem('fssai_no', company.fssai_no);
              localStorage.setItem('role', res.user.role);
              localStorage.setItem('metadata', JSON.stringify(res.metadata));
              localStorage.setItem('paper_type', res.metadata.bill_type.find(item => item.selected).type);
              localStorage.setItem('paper_size', res.metadata.bill_type.find(item => item.selected)?.size?.find(item => item.selected)?.size);
              localStorage.setItem('permissions', JSON.stringify(res.user.user_permissions))
              
              // Apply language preference from metadata if available
              if (res.metadata?.userPreferences?.language) {
                this.translationService.setLanguage(res.metadata.userPreferences.language);
              }
              
              this.loginError = false;
              // console.log(localStorage.getItem('token'));
              if (localStorage.getItem('token')) {
                this.router.navigate(['/']).then(() => {
                  // window.location.reload();
                });
              }
            } else {
              this.ionLoaderService.dismissLoader();
              this.toast.toastServices('Error logging in.', 'danger', 'top')
              this.loginError = true;
            }
          })
          .catch(async (err) => {
            this.toast.toastServices('Error logging in.', 'danger', 'top')
            this.ionLoaderService.dismissLoader();
            // console.log(err);
          });
      });
    } else {
      this.toast.toastServices('Please fill up all the fields.', 'danger', 'top')
    }
  }

  // hide and show password
  passwordType: string = 'password';
  passwordIcon: string = 'eye-off';

  hideShowPassword() {
    this.passwordType = this.passwordType === 'text' ? 'password' : 'text';
    this.passwordIcon = this.passwordIcon === 'eye-off' ? 'eye' : 'eye-off';
  }

}
