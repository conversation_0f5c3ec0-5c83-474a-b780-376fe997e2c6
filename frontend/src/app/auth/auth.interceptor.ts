import {
  <PERSON>tt<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { throwError as observableThrowError, Observable, timeout, TimeoutError } from "rxjs";
import { catchError } from "rxjs/operators";
import { Router } from '@angular/router';
import { ToastService } from "../shared/services/toast.service";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  token = localStorage.getItem("token");
  private readonly REQUEST_TIMEOUT = 30000; // 30 seconds timeout
  
  constructor(private router: Router, private toast: ToastService) {}
  
  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    req = req.clone({
      setHeaders: {
        // 'Content-Type': 'application/json; charset=utf-8',
        // Accept: 'application/json',
        Authorization: `token ${
          this.token ? this.token : localStorage.getItem("token")
        }`,
      },
    });
    
    return next.handle(req).pipe(
      timeout(this.REQUEST_TIMEOUT),
      catchError((err) => {
        if (err instanceof TimeoutError) {
          console.error('Request timeout:', req.url);
          this.toast.toastServices('Request timeout. Please try again.', 'warning', 'top');
        } else if (err.status === 401) {
          this.router.navigate(["/login"]); //go to login fail on 401
        }
        return observableThrowError(err);
      })
    );
  }
}
