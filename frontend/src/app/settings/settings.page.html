<app-header [title]="'Settings'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="settings-content">
  <!-- Printer Settings Section -->
  <div class="settings-section">
    <h3 class="section-title">
      <ion-icon name="print-outline" class="section-icon"></ion-icon>
      Printer Settings
    </h3>

    <ion-card class="settings-card">
      <ion-card-content>
        <ion-item lines="none" class="settings-item">
          <ion-icon name="bluetooth-outline" slot="start" class="item-icon"></ion-icon>
          <ion-label>
            <h3>Connected Printer</h3>
            <p>{{bluetoothService.getSelectedDevice()?.name || 'No device selected'}}</p>
          </ion-label>
          <ion-button
            (click)="selectDevice()"
            fill="outline"
            size="small"
            class="action-button">
            <ion-icon name="settings-outline" slot="start"></ion-icon>
            Select
          </ion-button>
        </ion-item>
      </ion-card-content>
    </ion-card>
  </div>



  <!-- Language Settings Section -->
  <div class="settings-section">
    <h3 class="section-title">
      <ion-icon name="language-outline" class="section-icon"></ion-icon>
      Language Settings / மொழி அமைப்புகள்
    </h3>

    <ion-card class="settings-card">
      <ion-card-content>
        <ion-item lines="none" class="settings-item">
          <ion-icon name="globe-outline" slot="start" class="item-icon"></ion-icon>
          <ion-label>
            <h3>App Language / பயன்பாட்டு மொழி</h3>
            <p>Current: {{getCurrentLanguageName()}} | நடப்பு: {{getCurrentLanguageName()}}</p>
            <p style="font-size: 12px; color: var(--ion-color-medium);">
              <ion-icon name="cloud-outline" style="vertical-align: middle;"></ion-icon>
              Stored in server metadata • சர்வர் மெட்டாடேட்டாவில் சேமிக்கப்பட்டது
            </p>
          </ion-label>
          <ion-button
            (click)="selectLanguage()"
            fill="outline"
            size="small"
            class="action-button">
            <ion-icon name="settings-outline" slot="start"></ion-icon>
            Change / மாற்று
          </ion-button>
        </ion-item>
        
        <!-- Language Preview -->
        <div class="language-preview" style="margin-top: 16px;">
          <ion-card style="margin: 0; box-shadow: none; border: 1px solid var(--ion-color-light);">
            <ion-card-header style="padding: 12px;">
              <ion-card-subtitle>Language Preview / மொழி முன்னோட்டம்</ion-card-subtitle>
            </ion-card-header>
            <ion-card-content style="padding: 12px; padding-top: 0;">
              <div class="preview-text" style="font-size: 14px; line-height: 1.4;">
                <p style="margin: 4px 0;"><strong>{{ 'nav.products' | translate }}</strong> • {{ 'common.search' | translate }} • {{ 'action.save' | translate }}</p>
                <p style="margin: 4px 0;"><strong>{{ 'business.invoice' | translate }}</strong> • {{ 'business.customer' | translate }} • {{ 'business.payment' | translate }}</p>
              </div>
            </ion-card-content>
          </ion-card>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Paper Settings Section -->
  <div class="settings-section">
    <h3 class="section-title">
      <ion-icon name="document-outline" class="section-icon"></ion-icon>
      Paper Settings
    </h3>

    <ion-card class="settings-card">
      <ion-card-content>
        <ion-item lines="none" class="settings-item">
          <ion-icon name="resize-outline" slot="start" class="item-icon"></ion-icon>
          <ion-label>
            <h3>Paper Configuration</h3>
            <p>Size: {{ paperSize }} | Type: {{ paperType }}</p>
          </ion-label>
          <ion-button
            (click)="selectPaperSettings()"
            fill="outline"
            size="small"
            class="action-button">
            <ion-icon name="settings-outline" slot="start"></ion-icon>
            Configure
          </ion-button>
        </ion-item>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Print Configuration Section -->
  <div class="settings-section">
    <h3 class="section-title">
      <ion-icon name="settings-outline" class="section-icon"></ion-icon>
      Print Configuration
    </h3>

    <ion-card class="settings-card">
      <ion-card-content>
        <!-- UPI Configuration -->
        <ion-item lines="none" class="settings-item">
          <ion-icon name="qr-code-outline" slot="start" class="item-icon"></ion-icon>
          <ion-label>
            <h3>UPI Payment QR Code</h3>
            <p>{{upiId || 'Not configured - QR codes will not appear on bills'}}</p>
          </ion-label>
          <ion-button
            (click)="configureUpi()"
            fill="outline"
            size="small"
            class="action-button">
            <ion-icon name="settings-outline" slot="start"></ion-icon>
            Configure
          </ion-button>
        </ion-item>

        <!-- Barcode Configuration -->
        <ion-item lines="none" class="settings-item">
          <ion-icon name="barcode-outline" slot="start" class="item-icon"></ion-icon>
          <ion-label>
            <h3>Invoice Barcode</h3>
            <p>{{companyCode || 'Not configured - Barcodes will not appear on bills'}}</p>
          </ion-label>
          <ion-button
            (click)="configureBarcode()"
            fill="outline"
            size="small"
            class="action-button">
            <ion-icon name="settings-outline" slot="start"></ion-icon>
            Configure
          </ion-button>
        </ion-item>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Payment Modes Section -->
  <div class="settings-section">
    <h3 class="section-title">
      <ion-icon name="card-outline" class="section-icon"></ion-icon>
      Payment Modes
    </h3>

    <ion-card class="settings-card">
      <ion-card-header>
        <div class="card-header-content">
          <ion-card-title>Manage Payment Methods</ion-card-title>
          <ion-button
            (click)="addModeOfPayment()"
            fill="solid"
            size="small"
            class="add-button">
            <ion-icon name="add-circle-outline" slot="start"></ion-icon>
            Add New
          </ion-button>
        </div>
      </ion-card-header>

      <ion-card-content>
        <div class="payment-modes-list" *ngIf="modeOfPayment && modeOfPayment.length > 0">
          <ion-item
            *ngFor="let item of modeOfPayment; let idx = index;"
            lines="none"
            class="payment-item">
            <ion-icon name="wallet-outline" slot="start" class="payment-icon"></ion-icon>
            <ion-label>
              <h3>{{item.name}}</h3>
              <p>Payment method {{idx + 1}}</p>
            </ion-label>
            <div class="payment-actions" slot="end">
              <ion-button
                (click)="modifyModeOfPayment(item.name,idx)"
                fill="clear"
                size="small"
                class="edit-button">
                <ion-icon name="create-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button
                (click)="deleteModeOfPayment(idx)"
                fill="clear"
                size="small"
                color="danger"
                class="delete-button">
                <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
              </ion-button>
            </div>
          </ion-item>
        </div>

        <div class="no-payment-modes" *ngIf="!modeOfPayment || modeOfPayment.length === 0">
          <ion-icon name="card-outline" class="empty-icon"></ion-icon>
          <p>No payment modes configured</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Route Management Section -->
  <div class="settings-section">
    <h3 class="section-title">
      <ion-icon name="map-outline" class="section-icon"></ion-icon>
      Route Management
    </h3>

    <ion-card class="settings-card">
      <ion-card-content>
        <ion-item lines="none" class="settings-item">
          <ion-icon name="calendar-outline" slot="start" class="item-icon"></ion-icon>
          <ion-label>
            <h3>Route Schedules</h3>
            <p>Manage routes and assign weekdays for billing</p>
          </ion-label>
          <ion-button
            (click)="navigateToRouteManagement()"
            fill="outline"
            size="small"
            class="action-button">
            <ion-icon name="settings-outline" slot="start"></ion-icon>
            Manage
          </ion-button>
        </ion-item>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Menu Sorting Section -->
  <div class="settings-section" *ngIf="componentSorting">
    <h3 class="section-title">
      <ion-icon name="menu-outline" class="section-icon"></ion-icon>
      Menu Management
    </h3>

    <ion-card class="settings-card">
      <ion-card-header>
        <ion-card-title>Menu Order Configuration</ion-card-title>
        <ion-card-subtitle>Arrange menu items in your preferred order</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <div class="menu-items-list">
          <ion-item
            *ngFor="let item of component"
            lines="none"
            class="menu-item">
            <ion-icon name="apps-outline" slot="start" class="menu-icon"></ion-icon>
            <ion-label>
              <h3>{{ item.title }}</h3>
              <p>Order: {{ item.sort_order }}</p>
            </ion-label>
            <div class="menu-actions" slot="end">
              <ion-button
                (click)="moveUp(item)"
                [disabled]="item.sort_order === 1"
                fill="clear"
                size="small"
                class="move-button">
                <ion-icon name="chevron-up-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button
                (click)="moveDown(item)"
                [disabled]="item.sort_order === component.length"
                fill="clear"
                size="small"
                class="move-button">
                <ion-icon name="chevron-down-outline" slot="icon-only"></ion-icon>
              </ion-button>
            </div>
          </ion-item>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>