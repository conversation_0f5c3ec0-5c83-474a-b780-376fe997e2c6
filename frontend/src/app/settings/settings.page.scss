/* Settings Page Styling */
.settings-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

/* Settings Section */
.settings-section {
  --background: var(--ion-card-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Settings Cards */
.settings-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  --background: white;
}

.card-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Settings Items */
.settings-item {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 12px;
}

.item-icon {
  color: var(--ion-color-primary);
  font-size: 24px;
  margin-right: 16px;
}

.settings-item ion-label h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.settings-item ion-label p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.action-button {
  --border-radius: 12px;
  --padding-start: 12px;
  --padding-end: 12px;
  height: 36px;
  font-size: 12px;
  font-weight: 500;
}

/* Payment Modes Section */
.add-button {
  --border-radius: 12px;
  --padding-start: 12px;
  --padding-end: 12px;
  height: 36px;
  font-size: 12px;
  font-weight: 500;
}

.payment-modes-list {
  max-height: 300px;
  overflow-y: auto;
}

.payment-item {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  margin-bottom: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.payment-item:hover {
  --background: #e9ecef;
  transform: translateX(4px);
}

.payment-icon {
  color: var(--ion-color-success);
  font-size: 20px;
  margin-right: 12px;
}

.payment-item ion-label h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.payment-item ion-label p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.payment-actions {
  display: flex;
  gap: 8px;
}

.edit-button,
.delete-button {
  --border-radius: 8px;
  width: 36px;
  height: 36px;
}

.no-payment-modes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 12px;
}

.no-payment-modes p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Menu Management Section */
.menu-items-list {
  max-height: 400px;
  overflow-y: auto;
}

.menu-item {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  margin-bottom: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.menu-item:hover {
  --background: #e9ecef;
  transform: translateX(4px);
}

.menu-icon {
  color: var(--ion-color-tertiary);
  font-size: 20px;
  margin-right: 12px;
}

.menu-item ion-label h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.menu-item ion-label p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.menu-actions {
  display: flex;
  gap: 4px;
}

.move-button {
  --border-radius: 8px;
  width: 36px;
  height: 36px;
  --color: var(--ion-color-medium);
}

.move-button:not([disabled]):hover {
  --color: var(--ion-color-primary);
}

/* Responsive Design */
@media (max-width: 576px) {
  .settings-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .card-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .action-button,
  .add-button {
    height: 32px;
    font-size: 11px;
  }

  .payment-actions,
  .menu-actions {
    gap: 4px;
  }

  .edit-button,
  .delete-button,
  .move-button {
    width: 32px;
    height: 32px;
  }
}

@media (min-width: 768px) {
  .settings-content {
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }
}

@media (min-width: 1024px) {
  .settings-content {
    --padding-start: 32px;
    --padding-end: 32px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }
}