<app-header [title]="'Analytics'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="analytics-content">
  <!-- Analytics Overview Section -->
  <div class="analytics-overview-section">
    <h3 class="section-title">
      <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
      Business Analytics Overview
    </h3>

    <ion-row *ngIf="comprehensiveDashboardData">
      <ion-col size="6">
        <ion-card class="summary-card revenue-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Revenue</h3>
                <h1>{{comprehensiveDashboardData.financial_overview.total_revenue | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Overall business revenue</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trending-up" class="summary-icon revenue"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card profit-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Net Profit</h3>
                <h1>{{comprehensiveDashboardData.financial_overview.net_profit | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Total profit margin</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trophy" class="summary-icon profit"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row *ngIf="comprehensiveDashboardData">
      <ion-col size="6">
        <ion-card class="summary-card customers-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Active Customers</h3>
                <h1>{{comprehensiveDashboardData.summary_metrics.active_buyers}}</h1>
                <p>Total active customers</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="people-circle" class="summary-icon customers"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card transactions-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Transactions</h3>
                <h1>{{comprehensiveDashboardData.activity_summary.total_transactions}}</h1>
                <p>Recent transactions</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="receipt" class="summary-icon transactions"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>

  <!-- Date Filter Section -->
  <div class="date-filter-section">
    <ion-card class="date-filter-card">
      <ion-card-header>
        <ion-card-title class="filter-title">
          <div class="filter-title-content">
            <ion-icon name="calendar-outline" class="filter-icon"></ion-icon>
            <span>Date Range Filter</span>
            <div class="filter-summary" *ngIf="!showDateFilter">
              <ion-chip color="primary" outline="true" size="small">
                <ion-label>{{getPresetLabel()}}</ion-label>
              </ion-chip>
            </div>
          </div>
          <ion-button fill="clear" size="small" (click)="toggleDateFilter()" class="toggle-button">
            <ion-icon [name]="showDateFilter ? 'chevron-up' : 'chevron-down'" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-card-title>
      </ion-card-header>

      <ion-card-content *ngIf="showDateFilter">
        <!-- Date Preset Selection -->
        <div class="date-preset-section">
          <ion-label class="preset-label">Quick Select:</ion-label>
          <div class="segment-wrapper">
            <ion-segment [(ngModel)]="dateFilterPreset" (ionChange)="onDatePresetChange()" class="date-preset-segment" scrollable="true">
              <ion-segment-button value="last_7_days">
                <ion-label>7 Days</ion-label>
              </ion-segment-button>
              <ion-segment-button value="last_30_days">
                <ion-label>30 Days</ion-label>
              </ion-segment-button>
              <ion-segment-button value="last_3_months">
                <ion-label>3 Months</ion-label>
              </ion-segment-button>
              <ion-segment-button value="last_6_months">
                <ion-label>6 Months</ion-label>
              </ion-segment-button>
              <ion-segment-button value="last_year">
                <ion-label>1 Year</ion-label>
              </ion-segment-button>
              <ion-segment-button value="custom">
                <ion-label>Custom</ion-label>
              </ion-segment-button>
            </ion-segment>
          </div>
        </div>

        <!-- Custom Date Selection -->
        <div class="custom-date-section" *ngIf="dateFilterPreset === 'custom'">
          <ion-row>
            <ion-col size="12" size-md="6">
              <ion-item lines="none" class="date-item">
                <ion-label position="stacked">From Date</ion-label>
                <ion-input
                  [(ngModel)]="fromDate"
                  (ngModelChange)="onFromDateChange($event)"
                  [value]="fromDate | date : 'YYYY-MM-dd'"
                  placeholder="Select from date"
                  type="date"
                  [max]="toDate"
                  class="date-input">
                </ion-input>
              </ion-item>
            </ion-col>
            <ion-col size="12" size-md="6">
              <ion-item lines="none" class="date-item">
                <ion-label position="stacked">To Date</ion-label>
                <ion-input
                  [(ngModel)]="toDate"
                  (ngModelChange)="onToDateChange($event)"
                  [value]="toDate | date : 'YYYY-MM-dd'"
                  placeholder="Select to date"
                  type="date"
                  [min]="fromDate"
                  [max]="getCurrentDate()"
                  class="date-input">
                </ion-input>
              </ion-item>
            </ion-col>
          </ion-row>
        </div>

        <!-- Filter Actions -->
        <div class="filter-actions">
          <ion-button
            expand="block"
            (click)="applyDateFilter()"
            [disabled]="isLoading"
            class="apply-filter-button">
            <ion-icon name="analytics" slot="start"></ion-icon>
            Apply Filter
          </ion-button>
          <ion-button
            fill="outline"
            expand="block"
            (click)="resetDateFilter()"
            [disabled]="isLoading"
            class="reset-filter-button">
            <ion-icon name="refresh" slot="start"></ion-icon>
            Reset to Default
          </ion-button>
        </div>

        <!-- Current Filter Info -->
        <div class="current-filter-info">
          <ion-chip color="primary" outline="true">
            <ion-icon name="calendar" slot="start"></ion-icon>
            <ion-label>{{getDateRangeDisplay()}}</ion-label>
          </ion-chip>
          <ion-chip color="secondary" outline="true">
            <ion-icon name="time" slot="start"></ion-icon>
            <ion-label>{{getPresetLabel()}}</ion-label>
          </ion-chip>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Charts Section -->
  <div class="charts-section">
    <h3 class="section-title">
      <ion-icon name="bar-chart-outline" class="section-icon"></ion-icon>
      Analytics Charts
    </h3>

    <ion-row>
      <ion-col size="12">
        <ion-card class="chart-card">
          <ion-card-header>
            <ion-card-title class="chart-title">
              <ion-icon name="trending-up-outline" class="chart-icon"></ion-icon>
              Sales Trend by Brand
            </ion-card-title>
            <ion-card-subtitle>Sales performance across different brands over time</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div class="chart-controls">
              <ion-segment [(ngModel)]="selectedTimePeriod" (ionChange)="onTimePeriodChange()">
                <ion-segment-button value="monthly">
                  <ion-label>Monthly</ion-label>
                </ion-segment-button>
                <ion-segment-button value="quarterly">
                  <ion-label>Quarterly</ion-label>
                </ion-segment-button>
                <ion-segment-button value="yearly">
                  <ion-label>Yearly</ion-label>
                </ion-segment-button>
              </ion-segment>
            </div>
            <div class="chart-container">
              <div *ngIf="isLoading" class="chart-loading">
                <ion-spinner name="crescent"></ion-spinner>
                <p>Updating chart...</p>
              </div>
              <canvas #salesTrendChart width="400" height="300" [style.display]="isLoading ? 'none' : 'block'"></canvas>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="12" size-md="6">
        <ion-card class="chart-card">
          <ion-card-header>
            <ion-card-title class="chart-title">
              <ion-icon name="pie-chart-outline" class="chart-icon"></ion-icon>
              Customer Distribution
            </ion-card-title>
            <ion-card-subtitle>Active vs inactive customers</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div class="chart-container">
              <canvas #userDistributionChart width="300" height="300"></canvas>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="12" size-md="6">
        <ion-card class="chart-card">
          <ion-card-header>
            <ion-card-title class="chart-title">
              <ion-icon name="bar-chart-outline" class="chart-icon"></ion-icon>
              Financial Overview
            </ion-card-title>
            <ion-card-subtitle>Revenue, expenses, and profit analysis</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div class="chart-container">
              <canvas #revenueChart width="400" height="300"></canvas>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>

  <!-- Brand Performance Section -->
  <div class="brand-performance-section" *ngIf="brandSummaryData && brandSummaryData.length > 0">
    <h3 class="section-title">
      <ion-icon name="storefront-outline" class="section-icon"></ion-icon>
      Brand Performance Analysis
    </h3>
    <ion-row>
      <ion-col size="12">
        <ion-card class="brand-summary-card">
          <ion-card-header>
            <ion-card-title class="brand-title">
              <ion-icon name="trophy-outline" class="brand-icon"></ion-icon>
              Top Performing Brands
            </ion-card-title>
            <ion-card-subtitle>Sales performance by brand ({{selectedTimePeriod}})</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div class="brand-list">
              <ion-item *ngFor="let brand of brandSummaryData; let i = index" class="brand-item" lines="none">
                <div class="brand-rank" slot="start">
                  <span class="rank-number">{{i + 1}}</span>
                </div>
                <ion-label>
                  <h4 class="brand-name">{{brand.product__brand__name}}</h4>
                  <p class="brand-stats">
                    {{brand.transaction_count}} transactions • {{brand.total_quantity | number:'1.0-0'}} units
                  </p>
                </ion-label>
                <div class="brand-performance" slot="end">
                  <div class="brand-amount">{{brand.total_sales_amount | currency: 'INR':'symbol':'1.0-0'}}</div>
                  <div class="brand-growth" [class.positive]="getBrandGrowth(brand.product__brand__id) > 0"
                       [class.negative]="getBrandGrowth(brand.product__brand__id) < 0">
                    <ion-icon [name]="getBrandGrowth(brand.product__brand__id) > 0 ? 'trending-up' : 'trending-down'"></ion-icon>
                    {{getBrandGrowth(brand.product__brand__id) | number:'1.1-1'}}%
                  </div>
                </div>
              </ion-item>
            </div>

            <!-- No brand data message -->
            <div class="no-brand-data" *ngIf="!brandSummaryData || brandSummaryData.length === 0">
              <ion-icon name="storefront-outline" class="no-data-icon"></ion-icon>
              <p>No brand performance data available</p>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>
</ion-content>



<ion-footer>
  <app-floating-menu></app-floating-menu>
</ion-footer>
