import { Component, OnInit, ViewChild, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { DashboardService, SalesTrendByBrandData, BrandSummary } from '../shared/services/dashboard.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { ToastService } from '../shared/services/toast.service';

// Import Chart.js
declare var Chart: any;

@Component({
  selector: 'app-analytics',
  templateUrl: './analytics.page.html',
  styleUrls: ['./analytics.page.scss'],
})
export class AnalyticsPage implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('salesTrendChart', { static: false }) salesTrendChart!: ElementRef<HTMLCanvasElement>;
  @ViewChild('userDistributionChart', { static: false }) userDistributionChart!: ElementRef<HTMLCanvasElement>;
  @ViewChild('revenueChart', { static: false }) revenueChart!: ElementRef<HTMLCanvasElement>;

  // Sales trend by brand data
  salesTrendData: SalesTrendByBrandData | null = null;
  brandSummaryData: BrandSummary[] = [];
  selectedTimePeriod: string = 'monthly';
  brandGrowthData: { [brandId: number]: number } = {};

  // Date filter properties
  fromDate: string = '';
  toDate: string = '';
  maxDate: string = new Date().toISOString();
  showDateFilter: boolean = false;
  dateFilterPreset: string = 'last_6_months';



  // Additional analytics data
  isLoading: boolean = false;
  comprehensiveDashboardData: any = null;

  // Chart instances to manage destruction
  private salesTrendChartInstance: any = null;
  private userDistributionChartInstance: any = null;
  private revenueChartInstance: any = null;

  constructor(
    private dashboardService: DashboardService,
    private ionLoaderService: IonLoaderService,
    private toast: ToastService
  ) { }

  ngOnInit() {
    this.initializeDateFilter();
    this.loadAnalyticsData();
  }

  ngAfterViewInit() {
    // Initialize charts after view is ready
    setTimeout(() => {
      this.initializeCharts();
    }, 500);
  }

  async loadAnalyticsData() {
    try {
      this.isLoading = true;
      await this.ionLoaderService.startLoader();

      // Load comprehensive dashboard data
      this.comprehensiveDashboardData = await this.dashboardService.getComprehensiveDashboardData();

      // Load sales trend by brand data
      await this.loadSalesTrendByBrand();

      this.ionLoaderService.dismissLoader();
      this.isLoading = false;

      // Initialize charts after data is loaded
      setTimeout(() => {
        this.initializeCharts();
      }, 100);

    } catch (error) {
      console.error('Error loading analytics data:', error);
      this.ionLoaderService.dismissLoader();
      this.isLoading = false;
      this.toast.toastServices('Error loading analytics data', 'danger', 'top');
    }
  }

  // Load sales trend by brand data
  async loadSalesTrendByBrand() {
    try {
      this.salesTrendData = await this.dashboardService.getSalesTrendByBrand(
        this.fromDate || undefined, // Use selected from date or default
        this.toDate || undefined,   // Use selected to date or default
        this.selectedTimePeriod
      );

      if (this.salesTrendData) {
        this.brandSummaryData = this.salesTrendData.brand_summary;
        this.brandGrowthData = this.salesTrendData.brand_growth;
      }
    } catch (error) {
      console.error('Error loading sales trend by brand data:', error);
    }
  }

  // Handle time period change
  async onTimePeriodChange() {
    try {
      // Show loading state
      this.isLoading = true;

      // Load new data with current date filter
      await this.loadSalesTrendByBrand();

      // Destroy existing chart before creating new one
      this.destroySalesTrendChart();

      // Recreate the chart with new data
      setTimeout(() => {
        this.createSalesTrendChart();
        this.isLoading = false;
      }, 100);

    } catch (error) {
      console.error('Error updating time period:', error);
      this.isLoading = false;
      this.toast.toastServices('Error updating chart data', 'danger', 'top');
    }
  }

  // Get brand growth percentage
  getBrandGrowth(brandId: number): number {
    return this.brandGrowthData[brandId] || 0;
  }

  // Initialize date filter with default values
  initializeDateFilter() {
    const today = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(today.getMonth() - 6);

    this.maxDate = today.toISOString();
    this.toDate = this.formatDateForInput(today);
    this.fromDate = this.formatDateForInput(sixMonthsAgo);
  }

  // Helper method to format date for input
  private formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Toggle date filter visibility
  toggleDateFilter() {
    this.showDateFilter = !this.showDateFilter;
  }

  // Handle date filter preset changes
  onDatePresetChange() {
    const today = new Date();
    let fromDate = new Date();

    switch (this.dateFilterPreset) {
      case 'last_7_days':
        fromDate.setDate(today.getDate() - 7);
        break;
      case 'last_30_days':
        fromDate.setDate(today.getDate() - 30);
        break;
      case 'last_3_months':
        fromDate.setMonth(today.getMonth() - 3);
        break;
      case 'last_6_months':
        fromDate.setMonth(today.getMonth() - 6);
        break;
      case 'last_year':
        fromDate.setFullYear(today.getFullYear() - 1);
        break;
      case 'this_year':
        fromDate = new Date(today.getFullYear(), 0, 1);
        break;
      case 'custom':
        // Don't change dates for custom selection
        return;
      default:
        fromDate.setMonth(today.getMonth() - 6);
    }

    this.maxDate = today.toISOString();
    this.fromDate = this.formatDateForInput(fromDate);
    this.toDate = this.formatDateForInput(today);

    // Auto-apply filter for preset changes
    this.applyDateFilter();
  }

  // Apply date filter
  async applyDateFilter() {
    if (!this.fromDate || !this.toDate) {
      this.toast.toastServices('Please select both from and to dates', 'warning', 'top');
      return;
    }

    if (new Date(this.fromDate) > new Date(this.toDate)) {
      this.toast.toastServices('From date cannot be later than to date', 'warning', 'top');
      return;
    }

    try {
      this.isLoading = true;
      await this.loadSalesTrendByBrand();
      this.destroySalesTrendChart();
      setTimeout(() => {
        this.createSalesTrendChart();
        this.isLoading = false;
      }, 100);
      this.toast.toastServices('Analytics updated successfully', 'success', 'top');
    } catch (error) {
      console.error('Error applying date filter:', error);
      this.isLoading = false;
      this.toast.toastServices('Error updating analytics', 'danger', 'top');
    }
  }

  // Reset date filter to default
  resetDateFilter() {
    this.dateFilterPreset = 'last_6_months';
    const today = new Date();
    this.maxDate = today.toISOString();
    this.initializeDateFilter();
    this.applyDateFilter();
  }

  // Get formatted date range for display
  getDateRangeDisplay(): string {
    if (!this.fromDate || !this.toDate) return 'No date range selected';

    const from = new Date(this.fromDate);
    const to = new Date(this.toDate);

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };

    return `${from.toLocaleDateString('en-US', options)} - ${to.toLocaleDateString('en-US', options)}`;
  }

  // Get preset label for display
  getPresetLabel(): string {
    const labels: { [key: string]: string } = {
      'last_7_days': 'Last 7 Days',
      'last_30_days': 'Last 30 Days',
      'last_3_months': 'Last 3 Months',
      'last_6_months': 'Last 6 Months',
      'last_year': 'Last Year',
      'this_year': 'This Year',
      'custom': 'Custom Range'
    };
    return labels[this.dateFilterPreset] || 'Unknown';
  }

  // Format date for display
  getFormattedDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Check if dates are valid for filtering
  areDatesValid(): boolean {
    return !!(this.fromDate && this.toDate);
  }

  // Get current date in YYYY-MM-DD format for max date constraint
  getCurrentDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  // Handle from date change
  onFromDateChange(newDate: string) {
    
    this.fromDate = newDate;
    this.toast.toastServices(`From date updated to ${this.getFormattedDate(newDate)}`, 'success', 'top');
  }

  // Handle to date change
  onToDateChange(newDate: string) {
  
    this.toDate = newDate;
    this.toast.toastServices(`To date updated to ${this.getFormattedDate(newDate)}`, 'success', 'top');
  }

  // Initialize charts with Chart.js
  private initializeCharts() {
    // Load Chart.js dynamically and then create charts
    this.loadChartJS().then(() => {
      this.createSalesTrendChart();
      this.createUserDistributionChart();
      this.createRevenueChart();
    }).catch(() => {
      console.warn('Chart.js failed to load, charts will not be displayed');
    });
  }

  private loadChartJS(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof Chart !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
      script.onload = () => resolve();
      script.onerror = () => reject();
      document.head.appendChild(script);
    });
  }

  private createSalesTrendChart() {
    if (!this.salesTrendChart?.nativeElement || !this.salesTrendData) return;

    // Destroy existing chart if it exists
    this.destroySalesTrendChart();

    const ctx = this.salesTrendChart.nativeElement.getContext('2d');
    if (!ctx) return;

    // Prepare data for Chart.js
    const brands = [...new Set(this.salesTrendData.time_series_data.map(item => item.product__brand__name))];
    const periods = [...new Set(this.salesTrendData.time_series_data.map(item => item.period))].sort();

    const datasets = brands.map((brand, index) => {
      const brandData = periods.map(period => {
        const dataPoint = this.salesTrendData!.time_series_data.find(
          item => item.product__brand__name === brand && item.period === period
        );
        return dataPoint ? dataPoint.total_sales_amount : 0;
      });

      // Generate colors for each brand
      const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
        '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
      ];

      return {
        label: brand,
        data: brandData,
        borderColor: colors[index % colors.length],
        backgroundColor: colors[index % colors.length] + '20',
        fill: false,
        tension: 0.1
      };
    });

    this.salesTrendChartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels: periods.map(period => new Date(period).toLocaleDateString('en-US', {
          month: 'short',
          year: 'numeric'
        })),
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context: any) {
                return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString('en-IN');
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Time Period'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Sales Amount (₹)'
            },
            ticks: {
              callback: function(value: any) {
                return '₹' + value.toLocaleString('en-IN');
              }
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    });
  }

  private destroySalesTrendChart() {
    if (this.salesTrendChartInstance) {
      this.salesTrendChartInstance.destroy();
      this.salesTrendChartInstance = null;
    }
  }

  private createUserDistributionChart() {
    if (!this.userDistributionChart?.nativeElement || !this.comprehensiveDashboardData) return;

    // Destroy existing chart if it exists
    if (this.userDistributionChartInstance) {
      this.userDistributionChartInstance.destroy();
      this.userDistributionChartInstance = null;
    }

    const ctx = this.userDistributionChart.nativeElement.getContext('2d');
    if (!ctx) return;

    const activeUsers = this.comprehensiveDashboardData.summary_metrics.active_buyers;
    const totalUsers = this.comprehensiveDashboardData.summary_metrics.total_buyers;
    const inactiveUsers = totalUsers - activeUsers;

    this.userDistributionChartInstance = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Active Users', 'Inactive Users'],
        datasets: [{
          data: [activeUsers, inactiveUsers],
          backgroundColor: ['#28a745', '#ffc107'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });
  }

  private createRevenueChart() {
    if (!this.revenueChart?.nativeElement || !this.comprehensiveDashboardData) return;

    // Destroy existing chart if it exists
    if (this.revenueChartInstance) {
      this.revenueChartInstance.destroy();
      this.revenueChartInstance = null;
    }

    const ctx = this.revenueChart.nativeElement.getContext('2d');
    if (!ctx) return;

    const data = this.comprehensiveDashboardData.financial_overview;

    this.revenueChartInstance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Revenue', 'Purchases', 'Expenses', 'Profit'],
        datasets: [{
          label: 'Amount (₹)',
          data: [
            data.total_revenue,
            data.total_purchases,
            data.total_expenses,
            data.net_profit
          ],
          backgroundColor: [
            '#28a745',
            '#007bff',
            '#dc3545',
            '#ffc107'
          ],
          borderColor: [
            '#1e7e34',
            '#0056b3',
            '#bd2130',
            '#e0a800'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context: any) {
                return context.label + ': ₹' + context.parsed.y.toLocaleString('en-IN');
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value: any) {
                return '₹' + value.toLocaleString('en-IN');
              }
            }
          }
        }
      }
    });
  }

  ngOnDestroy() {
    // Clean up all chart instances when component is destroyed
    this.destroyAllCharts();
  }

  private destroyAllCharts() {
    if (this.salesTrendChartInstance) {
      this.salesTrendChartInstance.destroy();
      this.salesTrendChartInstance = null;
    }
    if (this.userDistributionChartInstance) {
      this.userDistributionChartInstance.destroy();
      this.userDistributionChartInstance = null;
    }
    if (this.revenueChartInstance) {
      this.revenueChartInstance.destroy();
      this.revenueChartInstance = null;
    }
  }
}
