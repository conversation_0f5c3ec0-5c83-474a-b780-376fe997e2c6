/* Analytics Content */
.analytics-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 16px;
  --padding-bottom: 16px;
  background: #f8f9fa;
}

/* Section Titles */
.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Date Filter Section */
.date-filter-section {
  margin-bottom: 24px;
}

.date-filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.filter-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 600;
}

.filter-title-content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.filter-summary {
  margin-left: auto;
  margin-right: 8px;
}

.filter-summary ion-chip {
  --background: rgba(var(--ion-color-primary-rgb), 0.1);
  --color: var(--ion-color-primary);
  font-size: 11px;
  height: 24px;
}

.filter-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.toggle-button {
  --color: var(--ion-color-medium);
  margin: 0;
}

.date-preset-section {
  margin-bottom: 20px;
}

.segment-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.segment-wrapper::-webkit-scrollbar {
  display: none;
}

.preset-label {
  display: block;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.date-preset-segment {
  --background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
  width: max-content;
  min-width: 100%;
}

.date-preset-segment ion-segment-button {
  --color: #6c757d;
  --color-checked: var(--ion-color-primary);
  --background-checked: rgba(var(--ion-color-primary-rgb), 0.1);
  --border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  min-width: 60px;
  padding: 8px 4px;
}

.custom-date-section {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.date-item {
  --background: transparent;
  --border-radius: 8px;
  margin-bottom: 8px;
}

.date-input {
  --background: #ffffff;
  --border-radius: 8px;
  --color: var(--ion-color-dark);
  --placeholder-color: #6c757d;
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  font-size: 14px;
}

.date-input:focus {
  --border-color: var(--ion-color-primary);
  border-color: var(--ion-color-primary);
}

.date-item {
  --background: transparent;
  --border-radius: 8px;
  margin-bottom: 8px;
}

.date-item ion-label {
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 4px;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-placeholder ion-spinner {
  margin-bottom: 16px;
}

.loading-placeholder p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.debug-info {
  margin-top: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #6c757d;
}

.filter-actions {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.apply-filter-button {
  --background: var(--ion-color-primary);
  --color: white;
  font-weight: 600;
}

.reset-filter-button {
  --color: var(--ion-color-medium);
  --border-color: var(--ion-color-medium);
}

.current-filter-info {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.current-filter-info ion-chip {
  --background: rgba(var(--ion-color-primary-rgb), 0.1);
  --color: var(--ion-color-primary);
  font-size: 12px;
}

/* Analytics Overview Section */
.analytics-overview-section {
  margin-bottom: 24px;
}

.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.revenue {
  color: #28a745;
}

.summary-icon.profit {
  color: #ffc107;
}

.summary-icon.customers {
  color: #6f42c1;
}

.summary-icon.transactions {
  color: #007bff;
}

.revenue-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.profit-card {
  background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
}

.customers-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.transactions-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

/* Charts Section */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.chart-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.chart-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  position: relative;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.chart-loading ion-spinner {
  margin-bottom: 8px;
}

.chart-loading p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.chart-controls {
  margin-bottom: 16px;
}

.chart-controls ion-segment {
  --background: #f8f9fa;
  border-radius: 8px;
}

.chart-controls ion-segment-button {
  --color: #6c757d;
  --color-checked: var(--ion-color-primary);
  --background-checked: rgba(var(--ion-color-primary-rgb), 0.1);
  --border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* Brand Performance Section */
.brand-performance-section {
  margin-bottom: 24px;
}

.brand-summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.brand-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.brand-icon {
  font-size: 20px;
  margin-right: 8px;
  color: #ffc107;
}

.brand-list {
  max-height: 400px;
  overflow-y: auto;
}

.brand-item {
  --background: transparent;
  --border-radius: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.brand-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.brand-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(var(--ion-color-primary-rgb), 0.1);
  border-radius: 50%;
  margin-right: 12px;
}

.rank-number {
  font-size: 14px;
  font-weight: 700;
  color: var(--ion-color-primary);
}

.brand-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.brand-stats {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.brand-performance {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.brand-amount {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.brand-growth {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.brand-growth.positive {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.brand-growth.negative {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.brand-growth ion-icon {
  font-size: 14px;
}

/* No brand data message */
.no-brand-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-data-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 12px;
}

.no-brand-data p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-container {
    min-height: 250px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .brand-list {
    max-height: 300px;
  }

  .date-preset-segment {
    --background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .date-preset-segment::-webkit-scrollbar {
    display: none;
  }

  .date-preset-segment ion-segment-button {
    font-size: 10px;
    min-width: 55px;
    padding: 6px 2px;
    flex-shrink: 0;
  }

  .filter-actions {
    gap: 8px;
  }

  .custom-date-section {
    padding: 12px;
  }

  .filter-title-content {
    flex-wrap: wrap;
    gap: 4px;
  }

  .filter-summary {
    margin-left: 0;
    margin-right: 0;
    margin-top: 4px;
  }

  .analytics-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .date-item {
    margin-bottom: 12px;
  }
}

@media (max-width: 480px) {
  .date-preset-segment ion-segment-button {
    font-size: 9px;
    min-width: 45px;
    padding: 4px 1px;
  }

  .filter-title {
    font-size: 16px;
  }

  .section-title {
    font-size: 18px;
  }

  .custom-date-section {
    padding: 8px;
  }
}

.analytics-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.analytics-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}