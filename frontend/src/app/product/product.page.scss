/* Product List Page Styling */
.product-list-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}

/* Collapsible Summary Section - Now at Top */
.summary-section {
  background: var(--ion-card-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.summary-header:hover {
  background: #f8f9fa;
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.summary-content.expanded {
  max-height: 400px;
  padding: 16px;
}

/* Summary Card Styles */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.summary-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ion-text-color-step-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: var(--ion-text-color);
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: var(--ion-text-color-step-600);
}

.summary-illustration {
  display: flex;
  align-items: center;
  justify-content: center;
}

.summary-icon {
  font-size: 40px;
  opacity: 0.8;
}

/* Specific card colors */
.total-card .summary-icon.total {
  color: var(--ion-color-primary);
}

.active-card .summary-icon.active {
  color: var(--ion-color-success);
}

.inactive-card .summary-icon.inactive {
  color: var(--ion-color-danger);
}

.filtered-card .summary-icon.filtered {
  color: var(--ion-color-warning);
}

/* Collapsible Filter Section */
.filter-section {
  margin: 0 12px 12px 12px;
  background: var(--ion-card-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.filter-header:hover {
  background: #f8f9fa;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ion-text-color);
}

.section-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 12px;
}

.filter-content.expanded {
  max-height: 400px;
  padding: 12px;
}

/* Search Section inside Filter */
.search-section {
  margin-bottom: 16px;
}

.custom-searchbar {
  --background: var(--ion-color-step-50);
  --border-radius: 12px;
  --box-shadow: none;
  --placeholder-color: var(--ion-text-color-step-600);
  --color: var(--ion-text-color);
}

.search-input {
  --background: var(--ion-color-step-50);
  --border-radius: 8px;
  --border-color: var(--ion-border-color);
  --color: var(--ion-text-color);
}

/* Filter Controls */
.filter-controls {
  margin-bottom: 12px;
}

.filter-button {
  --border-radius: 8px;
  --border-color: #e0e0e0;
  --color: #333;
  height: 40px;
  font-size: 14px;
  text-transform: none;
}

.filter-button ion-label {
  margin: 0;
}

/* No Data Item */
.no-data-item {
  --background: var(--ion-item-background);
  --padding-start: 16px;
  --padding-end: 16px;
  margin: 16px;
  border-radius: 8px;
}

/* Product List */
.product-list {
  padding: 8px 16px;
  margin-bottom: 80px; /* Space for floating menu */
}

/* Product Item */
.product-item {
  --background: var(--ion-item-background);
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --min-height: 80px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Product Image */
.product-image {
  width: 60px;
  height: 60px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image ion-thumbnail {
  --size: 60px;
  --border-radius: 8px;
  width: var(--size);
  height: var(--size);
}

.product-placeholder {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 24px;
  color: #999;
}

.product-image ion-skeleton-text {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* Product Details */
.product-details {
  flex: 1;
  margin: 0;
  background: var(--ion-background-color);
}

.product-name {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.product-description {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 8px;
}

.product-meta {
  display: flex;
  align-items: center;
  margin: 4px 0;
  font-size: 14px;
  color: var(--ion-text-color-step-600);
}

.brand-info {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.brand-text {
  font-size: 13px;
  color: var(--ion-color-primary-contrast);
  font-weight: 600;
  background: var(--ion-color-primary-tint);
  padding: 2px 8px;
  border-radius: 12px;
}

.stock-info {
  font-size: 14px;
  display: inline;
  color: var(--ion-text-color-step-600);
}

.product-details-row {
  display: flex;
  gap: 12px;
  margin: 4px 0;
  font-size: 12px;
}

.product-detail {
  color: var(--ion-text-color-step-600);
  font-weight: 500;
}

.product-sku {
  font-size: 12px;
  color: var(--ion-text-color-step-600);
  margin: 4px 0 0 0;
  font-weight: 500;
}

/* Product End Section */
.product-end-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 100px;
}

/* Product Price */
.product-price {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.product-actions .action-button {
  --border-radius: 8px;
  --padding-start: 8px;
  --padding-end: 8px;
  width: 40px;
  height: 40px;
}

.product-actions .action-button ion-icon {
  font-size: 18px;
}

.price {
  font-size: 18px;
  font-weight: 700;
  color: var(--ion-color-success);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-item {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 10px;
    --padding-bottom: 10px;
  }

  .product-image {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .product-image ion-thumbnail {
    --size: 50px;
  }

  .product-name {
    font-size: 15px;
  }

  .product-meta {
    font-size: 13px;
  }

  .brand-text {
    font-size: 12px;
    padding: 1px 6px;
  }

  .product-details-row {
    font-size: 11px;
    gap: 8px;
  }

  .price {
    font-size: 16px;
  }

  .filter-section {
    margin: 0 12px 12px 12px;
  }

  .filter-header {
    padding: 12px;
  }

  .filter-content.expanded {
    padding: 12px;
    max-height: 350px;
  }

  .search-section {
    margin-bottom: 12px;
  }

  .product-list {
    padding: 8px 12px;
  }
}

/* Summary Section */
.summary-section {
  padding: 16px;
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--ion-text-color);
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--ion-text-color-step-600);
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: var(--ion-text-color);
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: var(--ion-text-color-step-600);
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.total {
  color: #6f42c1;
}

.summary-icon.brands {
  color: #007bff;
}

.summary-icon.stock {
  color: #ffc107;
}

.summary-icon.value {
  color: #28a745;
}

.total-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.brands-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

.stock-card {
  background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
}

.value-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

/* Responsive Design for Summary */
@media (max-width: 576px) {
  .summary-section {
    padding: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .summary-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }
}

@media (min-width: 768px) {
  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }
}

@media (min-width: 1024px) {
  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }
}

/* Product Modal Styling */
.product-modal {
  --width: 90%;
  --max-width: 600px;
  --height: 100vh;
  --max-height: 90%;
  --border-radius: 16px;
  background: var(--ion-background-color);
}

.modal-content {
  --background: var(--ion-background-color);
}

.form-section {
  background: var(--ion-card-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.section-icon {
  font-size: 18px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.form-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 16px;
}

.form-item ion-label {
  font-weight: 500;
  color: #333;
}

.form-item ion-input {
  --background: #f5f5f5;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  margin-top: 8px;
}

.form-item ion-toggle {
  --background: #e0e0e0;
  --background-checked: var(--ion-color-primary);
  --handle-background: var(--ion-background-color);
  --handle-background-checked: var(--ion-background-color);
}

.form-actions {
  margin-top: 24px;
  padding: 0 8px;
}

.submit-button {
  --border-radius: 12px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
}

/* Purchase Rate Calculator Modal Styling */
.purchase-calculator-modal {
  --width: 95%;
  --max-width: 800px;
  --height: 100vh;
  --max-height: 90%;
  --border-radius: 16px;
}

.calculator-container {
  padding: 8px 0;
}

.modal-title-icon {
  font-size: 20px;
  margin-right: 8px;
  vertical-align: middle;
}

.calculator-button {
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  height: 36px;
  font-size: 12px;
  margin-top: 8px;
}

.form-section {
  background: var(--ion-card-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.section-icon {
  font-size: 18px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.form-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 12px;
}

.form-item ion-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-item ion-input {
  --background: #f5f5f5;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  margin-top: 4px;
  font-size: 14px;
}

/* Calculation Results Section */
.calculation-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e0e0e0;
}

.calculation-step {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  border-left: 4px solid var(--ion-color-primary);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.calculation-step.final-step {
  border-left-color: var(--ion-color-success);
  background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.step-icon {
  font-size: 16px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.final-step .step-icon {
  color: var(--ion-color-success);
}

.step-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.step-formula {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
}

.step-result {
  font-size: 16px;
  font-weight: 700;
  text-align: right;
}

.calculated-field {
  color: var(--ion-color-primary);
}

.final-result {
  color: var(--ion-color-success);
  font-size: 18px;
  background: rgba(40, 167, 69, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  border: 2px solid rgba(40, 167, 69, 0.2);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-buttons ion-button {
  --border-radius: 8px;
  height: 44px;
  font-weight: 600;
}

/* Responsive Design for Product Modals */
@media (max-width: 768px) {
  .product-modal {
    --width: 95%;
    --max-width: none;
    --height: 100vh;
    --max-height: 95%;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 14px;
  }

  .section-icon {
    font-size: 16px;
  }

  .form-item {
    margin-bottom: 12px;
  }

  .submit-button {
    --padding-top: 14px;
    --padding-bottom: 14px;
    font-size: 14px;
  }

  .calculator-button {
    font-size: 11px;
    height: 32px;
  }
}

/* Responsive Design for Calculator */
@media (max-width: 768px) {
  .purchase-calculator-modal {
    --width: 98%;
    --max-width: none;
    --height: 100vh;
    --max-height: 95%;
  }

  .calculator-container {
    padding: 4px 0;
  }

  .calculation-step {
    padding: 10px;
    margin-bottom: 10px;
  }

  .step-title {
    font-size: 13px;
  }

  .step-formula {
    font-size: 11px;
  }

  .step-result {
    font-size: 14px;
  }

  .final-result {
    font-size: 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .product-end-section {
    gap: 6px;
    min-width: 80px;
  }

  .product-actions .action-button {
    width: 36px;
    height: 36px;
  }

  .product-actions .action-button ion-icon {
    font-size: 16px;
  }
}

.product-header {
  background: linear-gradient(135deg, var(--ion-card-background) 0%, var(--ion-background-color) 100%);
  padding: 20px;
  border-radius: 0 0 20px 20px;
  margin-bottom: 20px;
}

.product-summary {
  background: linear-gradient(135deg, var(--ion-card-background) 0%, var(--ion-background-color) 100%);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}