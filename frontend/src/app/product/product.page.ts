import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, On<PERSON><PERSON>roy } from '@angular/core';
import { ProductService } from '../shared/services/product.service';
import { Platform, MenuController, AlertController, ModalController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { ToastService } from '../shared/services/toast.service';
import { IonicSelectableComponent } from 'ionic-selectable';
import { AuthenticationService } from '../shared/services/authentication.service';
import { ImportExportService } from '../shared/services/import-export.service';
import { RouteService } from '../shared/services/route.service';
import { ImportExportModalComponent } from '../shared/components/import-export-modal/import-export-modal.component';
import { SummaryService, ProductSummary } from '../shared/services/summary.service';
import { TranslationService } from '../shared/services/translation.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-product',
  templateUrl: './product.page.html',
  styleUrls: ['./product.page.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductPage implements OnInit, OnDestroy {
  isModalOpen = false;
  isEditModalOpen = false;
  data: any;
  editData: any;
  displayData: any;
  filterEmpty: boolean;
  calculatedPrMargin: number = null;
  calculatedPrRate: number = null;
  calculatedSellingMargin: number = null;
  calculatedSellingRate: number = null;
  mrp: number = null;
  marginalItem: any = JSON.parse(localStorage.getItem('metadata')).marginalItem;
  isCrateBased: boolean = false;
  crateSize: number = 1;

  // Purchase Rate Calculator Modal properties
  isPurchaseCalculatorOpen: boolean = false;
  calculatorMode: 'add' | 'edit' = 'add';

  // Calculator input fields
  calculatorTotalPurchaseAmount: number = 0;
  calculatorOrderQuantity: number = 1;
  calculatorUnitContains: number = 1;
  calculatorCrateContains: number = 1;
  calculatorGstIncluded: boolean = false;
  calculatorTaxPercent: number = 0;

  // Calculator calculated fields
  calculatorFinalTotalAmount: number = 0;
  calculatorTaxAmount: number = 0;
  calculatorPrRate: number = 0;
  brandData: any;
  count: any;
  page_size = 30;
  page_number = 1;
  search = '';
  searchText: any = '';
  selectedFile: any;
  appLabel: string ='master';
  modelName: string ='product';
  selectedBrand: string = 'all';
  selectedSort: string = '';
  brands: any[] = [];
  displayProducts: any[] = [];
  sortOptions: string[] = ['Name A-Z', 'Name Z-A', 'Price Low-High', 'Price High-Low', 'Stock Low-High', 'Stock High-Low'];
  showFilters: boolean = false;

  // Summary metrics (independent of filters)
  productSummary: ProductSummary | null = null;
  showSummary: boolean = false;
  
  // Performance optimization - OnDestroy subject
  private destroy$ = new Subject<void>();
  
  constructor(
    private api: ProductService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public authService: AuthenticationService,
    private menuController: MenuController,
    private routeService: RouteService,
    private alertController: AlertController,
    private modalController: ModalController,
    private summaryService: SummaryService,
    private cdr: ChangeDetectorRef,
    public translate: TranslationService
  ) { }

  ngOnInit() {

  }

  ngOnDestroy() {
    // Cleanup subscriptions to prevent memory leaks
    this.destroy$.next();
    this.destroy$.complete();
    
    // Clear any pending timeouts
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  ionViewWillEnter() {
    this.getData();
    this.loadProductSummary();
  }

  // Load product summary independently from filtered data
  async loadProductSummary() {
    try {
      this.productSummary = await this.summaryService.getProductSummary();
      if (!this.productSummary) {
        console.warn('Failed to load product summary, using fallback');
        // Fallback to calculating from current data if available
        if (this.data && this.data.length > 0) {
          this.productSummary = {
            total_products: this.data.length,
            active_products: this.data.filter(p => p.active).length,
            inactive_products: this.data.filter(p => !p.active).length,
            generated_at: new Date().toISOString()
          };
        }
      }
    } catch (error) {
      console.error('Error loading product summary:', error);
    }
  }

  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
    if (isOpen) {
      // Reset crate fields when opening add modal
      this.isCrateBased = false;
      this.crateSize = 1;
    }
    this.getBrand()
  }
  setEditOpen(isOpen: boolean) {
    this.isEditModalOpen = isOpen;
    this.editData.brand = { id: this.editData.brand, name: this.editData.brand_name }
    console.log(this.editData);

    this.getBrand()
  }
  
  async addBrand(event: { component: IonicSelectableComponent }) {
    const name = event.component.searchText;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .addBrand(name)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.getBrand();
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async getBrand() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getBrand()
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.brandData = res.data
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async getData() {
    try {
      const result = await this.ionLoaderService.withLoader(async () => {
        return await this.api.getProduct(this.page_number, this.page_size, this.searchText);
      }, 'Loading products...') as any;

      if (result.success) {
        console.log(result);
        this.toast.toastServices(result.message, 'success', 'top');
        this.data = result.data.data;
        this.displayProducts = this.data;
        this.extractBrands();
        this.initializeImageLoading();
        this.count = result.data.count / this.page_size;
        this.filterEmpty = this.data.length <= 0;
        
        // Trigger change detection for OnPush strategy
        this.cdr.markForCheck();
      } else {
        this.toast.toastServices(result.message, 'danger', 'top');
        this.filterEmpty = true;
        this.cdr.markForCheck();
      }
    } catch (err: any) {
      const errorMessage = this.translate.instant('messages.networkError');
      this.toast.toastServices(errorMessage, 'danger', 'top');
      console.error('Error loading products:', err);
      this.filterEmpty = true;
      this.cdr.markForCheck();
    }
  }
  async loadProducts(ev) {
    console.log(ev);
    
    if (this.page_number < this.count) {
      this.page_number++;
      
      try {
        const res: any = await this.api.getProduct(this.page_number, this.page_size, this.searchText);
        
        if (res.success) {
          console.log(res);
          const newProducts = res.data.data;
          
          // Initialize image loading for new products
          newProducts.forEach((product, index) => {
            product.imageLoaded = false;
            setTimeout(() => {
              product.imageLoaded = true;
            }, 500 + (index * 100));
          });
          
          this.data = [...this.data, ...newProducts];
          this.displayProducts = this.data;
          this.extractBrands();
          ev.target.complete();
          
          // Show loading feedback
          if (this.searchText) {
            this.toast.toastServices(`Loaded ${newProducts.length} more products`, 'success', 'bottom');
          }
        } else {
          ev.target.disabled = true;
          this.toast.toastServices('No more products to load', 'warning', 'bottom');
        }
      } catch (err) {
        console.error('Error loading more products:', err);
        ev.target.disabled = true;
        this.toast.toastServices('Error loading more products', 'danger', 'bottom');
      }
    } else {
      ev.target.disabled = true;
      if (this.data.length > 0) {
        this.toast.toastServices('All products loaded', 'primary', 'bottom');
      }
    }
  }
  convertEmptyStringsToNull(formData: any): any {
    Object.keys(formData).forEach(key => {
      if (formData[key] === "") {
        formData[key] = null;
      }
    });
    return formData;
  }
  async addProduct(data) {
    data.brand_id = data.brand.id;
    delete data.brand;

    this.convertEmptyStringsToNull(data);
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .saveProduct(data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            const successMessage = this.translate.instant('messages.saveSuccess');
            this.toast.toastServices(successMessage, 'success', 'top');
            this.isModalOpen = false;
            this.getData()
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          const errorMessage = this.translate.instant('messages.networkError');
          this.toast.toastServices(errorMessage, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });

  }
  edit(data) {
    this.editData = data
    this.mrp = data.mrp;
    this.calculatedPrMargin = data.pr_margin;
    this.calculatedPrRate = data.pr_rate;
    this.calculatedSellingMargin = data.margin;
    this.calculatedSellingRate = data.rate;
    this.isCrateBased = data.is_crate_based || false;
    this.crateSize = data.crate_size || 1;
    this.setEditOpen(true);
  }
  async editProduct(data) {
    data.brand_id = data.brand.id;
    delete data.brand;
    this.convertEmptyStringsToNull(data);
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.editProduct(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.isEditModalOpen = false;
          this.getData();
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      });
    })
  }
  delete(data) {
    const title = this.translate.instant('common.confirm');
    const message = this.translate.instant('messages.deleteConfirm');
    const yesText = this.translate.instant('common.yes');
    const noText = this.translate.instant('common.no');
    
    this.alertService.alertConfirm(title, message, yesText, noText).then((res) => {
      if (res) {
        this.deleteProduct(data)
      }
    })
  }
  async deleteProduct(id) {
    let data = {
      id: id
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.deleteProduct(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      });
    });
  }
  async editProductStatus(status, id, field) {
    let data = field == "active" ? {
      id: id,
      active: status
    } : {
      id: id,
      add: status
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.editProductStatus(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      })
    })

  }

  // Debounce timer for search
  private searchTimeout: any;

  async filterItems(event) {
    this.searchText = event.target.value;

    // Clear existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search for 500ms
    this.searchTimeout = setTimeout(async () => {
      await this.performSearch();
    }, 500);
  }

  async performSearch() {
    try {
      // Reset pagination when searching
      this.page_number = 1;
      
      const result = await this.ionLoaderService.withLoader(async () => {
        return await this.api.getProduct(this.page_number, this.page_size, this.searchText);
      }, 'Searching products...') as any;

      if (result.success) {
        console.log(result);
        this.data = result.data.data;
        this.displayProducts = this.data;
        this.extractBrands();
        this.initializeImageLoading();
        this.count = result.data.count / this.page_size;
        this.filterEmpty = this.data.length <= 0;
        
        if (this.searchText) {
          this.toast.toastServices(`Found ${result.data.count} products`, 'success', 'top');
        }
      } else {
        this.toast.toastServices(result.message, 'danger', 'top');
        this.filterEmpty = true;
      }
    } catch (err: any) {
      this.toast.toastServices('Error searching products', 'danger', 'top');
      console.error('Search error:', err);
      this.filterEmpty = true;
    }
  }

  // Clear search and reload all products
  async clearSearch() {
    this.searchText = '';
    this.page_number = 1;
    await this.getData();
    const message = this.translate.instant('common.clear') + ' ' + this.translate.instant('common.success');
    this.toast.toastServices(message, 'success', 'top');
  }
  updateMrp(mrp: number) {
    this.mrp = mrp;
    console.log(this.mrp);
  }

  onCrateBasedToggle(event: any) {
    // Handle both direct boolean values and event objects
    if (typeof event === 'boolean') {
      this.isCrateBased = event;
    } else if (event && event.detail) {
      this.isCrateBased = event.detail.checked;
    } else {
      this.isCrateBased = event;
    }

    if (!this.isCrateBased) {
      this.crateSize = 1; // Reset to default when not crate-based
    }
  }

  updateCrateSize(size: number) {
    this.crateSize = size;
    if (this.crateSize < 1) {
      this.crateSize = 1; // Ensure minimum value
    }
  }
  calculatePrMargin(prRate: number) {
    if (!this.mrp) {
      return;
    }
    this.calculatedPrMargin = parseFloat((((this.mrp - prRate) / this.mrp) * 100).toFixed(2));
  }
  calculatePrRate(prMargin: number) {
    if (!this.mrp) {
      return;
    }
    this.calculatedPrRate = parseFloat((this.mrp - (this.mrp * prMargin) / 100).toFixed(2));
  }
  calculateSellingMargin(sellingRate: number) {
    if (!this.mrp) {
      return;
    }
    this.calculatedSellingMargin = parseFloat((((this.mrp - sellingRate) / this.mrp) * 100).toFixed(2));
  }
  calculateSellingRate(sellingMargin: number) {
    if (!this.mrp) {
      return;
    }
    this.calculatedSellingRate = parseFloat((this.mrp - (this.mrp * sellingMargin) / 100).toFixed(2));
  }

  // Purchase Rate Calculator Methods
  openPurchaseCalculator(mode: 'add' | 'edit' = 'add') {
    this.calculatorMode = mode;

    // Initialize calculator with existing values if in edit mode
    if (mode === 'edit' && this.editData) {
      this.calculatorCrateContains = this.editData.crate_size || 1;
      this.calculatorUnitContains = this.editData.unit_contains || 1;
      this.calculatorTaxPercent = this.editData.tax_rate || 0;
      this.calculatorTotalPurchaseAmount = 0; // Start fresh for calculation
    } else {
      // Initialize with current form values for add mode
      this.calculatorCrateContains = this.crateSize || 1;
      this.calculatorUnitContains = 1;
      this.calculatorTaxPercent = 0;
      this.calculatorTotalPurchaseAmount = 0;
    }

    // Reset other calculator fields
    this.calculatorOrderQuantity = 1;
    this.calculatorGstIncluded = false;

    // Calculate initial values
    this.calculatePurchaseRate();

    this.isPurchaseCalculatorOpen = true;
  }

  closePurchaseCalculator() {
    this.isPurchaseCalculatorOpen = false;
  }

  calculatePurchaseRate() {
    // Step 1: Determine final total purchase amount
    if (this.calculatorGstIncluded) {
      // GST is already included in the total purchase amount
      this.calculatorFinalTotalAmount = this.calculatorTotalPurchaseAmount;
      this.calculatorTaxAmount = this.calculatorFinalTotalAmount * (this.calculatorTaxPercent / (100 + this.calculatorTaxPercent));
    } else {
      // GST needs to be added to the total purchase amount
      this.calculatorTaxAmount = this.calculatorTotalPurchaseAmount * (this.calculatorTaxPercent / 100);
      this.calculatorFinalTotalAmount = this.calculatorTotalPurchaseAmount + this.calculatorTaxAmount;
    }

    // Step 2: Calculate pr_rate using the required formula
    // pr_rate = total_purchase_amount / (order_quantity × unit_contains × crate_contains)
    const totalUnits = this.calculatorOrderQuantity * this.calculatorUnitContains * this.calculatorCrateContains;
    if (totalUnits > 0) {
      this.calculatorPrRate = this.calculatorFinalTotalAmount / totalUnits;
    } else {
      this.calculatorPrRate = 0;
    }

    // Round to 2 decimal places
    this.calculatorFinalTotalAmount = parseFloat(this.calculatorFinalTotalAmount.toFixed(2));
    this.calculatorTaxAmount = parseFloat(this.calculatorTaxAmount.toFixed(2));
    this.calculatorPrRate = parseFloat(this.calculatorPrRate.toFixed(2));
  }

  onCalculatorFieldChange() {
    this.calculatePurchaseRate();
  }

  applyCalculatedRate() {
    if (this.calculatorMode === 'edit') {
      // Update edit form
      this.calculatedPrRate = this.calculatorPrRate;
      this.editData.pr_rate = this.calculatorPrRate;
      this.editData.crate_size = this.calculatorCrateContains;
      this.editData.unit_contains = this.calculatorUnitContains;
      this.editData.tax_rate = this.calculatorTaxPercent;
      this.crateSize = this.calculatorCrateContains;
    } else {
      // Update add form
      this.calculatedPrRate = this.calculatorPrRate;
      this.crateSize = this.calculatorCrateContains;
    }

    this.closePurchaseCalculator();
  }

  async toggleMenu() {
    await this.menuController.toggle('main-menu');
  }

  goBack() {
    this.routeService.routerFunction('tabs/home');
  }

  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  toggleSummary() {
    this.showSummary = !this.showSummary;
  }

  // Extract unique brands from product data
  extractBrands() {
    if (this.data && this.data.length > 0) {
      const uniqueBrandNames = [...new Set(this.data.map(product => product.brand_name))];
      this.brands = uniqueBrandNames
        .filter(brandName => brandName)
        .map(brandName => ({ name: brandName }));
    }
  }

  // Get stock status color
  getStockStatusColor(product: any): string {
    if (product.stock_quantity <= product.min_stock_threshold) {
      return 'danger';
    } else if (product.stock_quantity <= product.min_stock_threshold * 2) {
      return 'warning';
    }
    return 'success';
  }

  // Get stock status text
  getStockStatusText(product: any): string {
    if (product.stock_quantity <= product.min_stock_threshold) {
      return 'Low Stock';
    } else if (product.stock_quantity <= product.min_stock_threshold * 2) {
      return 'Medium Stock';
    }
    return 'In Stock';
  }

  // Format stock quantity
  getFormattedStock(product: any): string {
    return `${Math.floor(product.stock_quantity)} units`;
  }

  // Initialize image loading state for products
  initializeImageLoading() {
    if (this.data && this.data.length > 0) {
      this.data.forEach((product, index) => {
        // Set initial loading state
        product.imageLoaded = false;

        // Simulate loading delay for skeleton effect
        setTimeout(() => {
          product.imageLoaded = true;
        }, 500 + (index * 100)); // Staggered loading effect
      });
    }
  }

  // Brand filter functionality with alert selection
  async showBrandFilter() {
    const brandOptions: any[] = [
      { name: 'brand', type: 'radio' as const, label: 'All', value: 'all', checked: this.selectedBrand === 'all' },
      ...this.brands.map(brand => ({
        name: 'brand',
        type: 'radio' as const,
        label: brand.name,
        value: brand.name,
        checked: this.selectedBrand === brand.name
      }))
    ];

    const alert = await this.alertController.create({
      header: 'Select Brand',
      inputs: brandOptions,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'OK',
          handler: (brand) => {
            this.filterByBrand(brand);
          }
        }
      ]
    });
    await alert.present();
  }

  // Filter by brand - internal method
  filterByBrand(selectedBrand: string) {
    this.selectedBrand = selectedBrand;
    if (this.selectedBrand == 'all') {
      this.displayProducts = this.data;
      return;
    }
    this.displayProducts = this.data.filter((e) => e.brand_name == this.selectedBrand);
  }

  // Get selected brand label for display
  get selectedBrandLabel(): string {
    if (this.selectedBrand === 'all' || !this.selectedBrand) {
      return 'Brand';
    }
    return this.selectedBrand;
  }

  // Sort functionality
  async showSortOptions() {
    const alert = await this.alertController.create({
      header: 'Sort By',
      inputs: this.sortOptions.map(option => ({
        name: 'sort',
        type: 'radio' as const,
        label: option,
        value: option,
        checked: this.selectedSort === option
      })),
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'OK',
          handler: (sort) => {
            this.selectedSort = sort;
            this.applySorting();
          }
        }
      ]
    });
    await alert.present();
  }

  // Apply sorting to displayProducts
  applySorting() {
    if (!this.displayProducts) return;

    switch (this.selectedSort) {
      case 'Name A-Z':
        this.displayProducts.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'Name Z-A':
        this.displayProducts.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case 'Price Low-High':
        this.displayProducts.sort((a, b) => a.rate - b.rate);
        break;
      case 'Price High-Low':
        this.displayProducts.sort((a, b) => b.rate - a.rate);
        break;
      case 'Stock Low-High':
        this.displayProducts.sort((a, b) => a.stock_quantity - b.stock_quantity);
        break;
      case 'Stock High-Low':
        this.displayProducts.sort((a, b) => b.stock_quantity - a.stock_quantity);
        break;
    }
  }

  // Import/Export Modal Methods
  async openImportExportModal() {
    const modal = await this.modalController.create({
      component: ImportExportModalComponent,
      componentProps: {
        appLabel: 'master',
        modelName: 'product'
      },
      cssClass: 'import-export-modal'
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.imported) {
        // Refresh data after successful import
        this.getData();
      }
    });

    return await modal.present();
  }

  // Summary calculation methods
  getTotalProducts(): number {
    return this.displayProducts ? this.displayProducts.length : 0;
  }

  getTotalBrands(): number {
    if (!this.displayProducts || this.displayProducts.length === 0) return 0;
    const uniqueBrands = new Set(this.displayProducts.map(product => product.brand_name).filter(brand => brand));
    return uniqueBrands.size;
  }

  getLowStockCount(): number {
    if (!this.displayProducts || this.displayProducts.length === 0) return 0;
    return this.displayProducts.filter(product =>
      product.stock_quantity <= (product.min_stock_threshold || 10)
    ).length;
  }

  getTotalInventoryValue(): number {
    if (!this.displayProducts || this.displayProducts.length === 0) return 0;
    return this.displayProducts.reduce((total, product) => {
      const value = (product.rate || 0) * (product.stock_quantity || 0);
      return total + value;
    }, 0);
  }
}
