<app-header [title]="translate.instant('products.title')" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="product-list-content">
  <!-- Collapsible Summary Section - Independent from Filters -->
  <div class="summary-section">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        {{translate.instant('products.title')}} Summary (Global)
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card total-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Total Products</h3>
                <h1>{{productSummary?.total_products || 0}}</h1>
                <p>Items in inventory</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="cube" class="summary-icon total"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card active-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Active Products</h3>
                <h1>{{productSummary?.active_products || 0}}</h1>
                <p>Currently available</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="checkmark-circle" class="summary-icon active"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card inactive-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Inactive Products</h3>
                <h1>{{productSummary?.inactive_products || 0}}</h1>
                <p>Currently disabled</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="close-circle" class="summary-icon inactive"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card filtered-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Filtered Results</h3>
                <h1>{{displayProducts?.length || 0}}</h1>
                <p>Currently showing</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="filter" class="summary-icon filtered"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    </div>
  </div>

  <!-- Collapsible Filter Section -->
  <div class="filter-section">
    <div class="filter-header" (click)="toggleFilters()">
      <h3 class="section-title">
        <ion-icon name="filter-outline" class="section-icon"></ion-icon>
        Search & Filters
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showFilters ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="filter-content" [class.expanded]="showFilters">
      <!-- Search Bar -->
      <div class="search-section">
        <ion-searchbar
          [placeholder]="translate.instant('common.search') + ' ' + translate.instant('products.title')"
          inputmode="text"
          type="text"
          (ionChange)="filterItems($event)"
          [debounce]="450"
          showCancelButton="focus"
          class="custom-searchbar">
        </ion-searchbar>
      </div>

      <!-- Filter Controls -->
      <div class="filter-controls">
        <ion-row>
          <ion-col size="4">
            <ion-button fill="outline" expand="block" class="filter-button" (click)="showBrandFilter()">
              <ion-label>{{selectedBrandLabel}}</ion-label>
              <ion-icon name="chevron-down" slot="end"></ion-icon>
            </ion-button>
          </ion-col>
          <ion-col size="4">
            <ion-button fill="outline" expand="block" class="filter-button" (click)="showSortOptions()">
              <ion-label>{{selectedSort || 'Sort'}}</ion-label>
              <ion-icon name="chevron-down" slot="end"></ion-icon>
            </ion-button>
          </ion-col>
           <!-- Import/Export Buttons -->
          <ion-col size="4">
            <ion-button fill="outline" expand="block" class="filter-button" (click)="openImportExportModal()">
              <ion-label>Import/Export</ion-label>
              <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
            </ion-button>
          </ion-col>
        </ion-row>
      </div>
    </div>
  </div>

  <!-- No Data Message -->
  <ion-item lines="none" *ngIf="filterEmpty" class="no-data-item">
    <ion-text color="danger">
      <h6>{{translate.instant('common.noData')}}</h6>
    </ion-text>
  </ion-item>
  <!-- Product List -->
  <div class="product-list">
    <ion-item
      *ngFor="let product of displayProducts"
      class="product-item"
      lines="none"
      (click)="edit(product)">

      <!-- Product Thumbnail with Skeleton Loading -->
      <div class="product-image" slot="start">
        <ion-thumbnail>
          <ion-skeleton-text animated *ngIf="!product.imageLoaded"></ion-skeleton-text>
          <div class="product-placeholder" *ngIf="product.imageLoaded !== false">
            <ion-icon name="cube-outline" class="placeholder-icon"></ion-icon>
          </div>
        </ion-thumbnail>
      </div>

      <!-- Product Details -->
      <ion-label class="product-details">
        <h2 class="product-name">{{product.name}}</h2>

        <!-- Brand and Stock -->
        <div class="product-meta">
          <div class="brand-info">
            <span class="brand-text">{{product.brand_name}}</span>
          </div>
          <ion-text [color]="getStockStatusColor(product)" class="stock-info">
            • {{getStockStatusText(product)}}: {{getFormattedStock(product)}}
          </ion-text>
        </div>

        <!-- Product Details -->
        <div class="product-details-row">
          <span class="product-detail">HSN: {{product.hsn_code}}</span>
          <span class="product-detail">MRP: {{product.mrp | currency: 'INR':'symbol':'1.0-0'}}</span>
        </div>

        <!-- Short Code -->
        <p class="product-sku">Code: {{product.short_code}}</p>
      </ion-label>

      <!-- Price and Actions -->
      <div class="product-end-section" slot="end">
        <div class="product-price">
          <span class="price">{{product.rate | currency: 'INR':'symbol':'1.0-0'}}</span>
        </div>

        <!-- Actions -->
        <div class="product-actions">
          <ion-button
            fill="clear"
            size="small"
            color="warning"
            class="action-button edit-button"
            (click)="$event.stopPropagation(); edit(product)">
            <ion-icon name="create-outline"></ion-icon>
          </ion-button>
          <ion-button
            fill="clear"
            size="small"
            color="danger"
            class="action-button delete-button"
            (click)="$event.stopPropagation(); delete(product.id)">
            <ion-icon name="trash-outline"></ion-icon>
          </ion-button>
        </div>
      </div>
    </ion-item>
  </div>

  <!-- Infinite Scroll -->
  <ion-infinite-scroll (ionInfinite)="loadProducts($event)">
    <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="Loading more products...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>


  <!-- Add Product Modal -->
  <ion-modal [isOpen]="isModalOpen" class="product-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="primary">
          <ion-title>{{translate.instant('products.add')}}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <form #form="ngForm" (ngSubmit)="addProduct(form.value)">
          <!-- Basic Information Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="information-circle-outline" class="section-icon"></ion-icon>
              Basic Information
            </h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Name
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="name" required="true" placeholder="Enter product name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Short Code
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="short_code" required="true" placeholder="Enter short code"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label for="amount">Brand</ion-label>
              <ionic-selectable itemValueField="id" itemTextField="name" [items]="brandData" ngModel name="brand"
                [canSearch]="true" [canAddItem]="true" (onAddItem)="addBrand($event)" placeholder="Select or add a brand">
              </ionic-selectable>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Sort Order</ion-label>
              <ion-input ngModel type="number" name="sort_order" required="false" placeholder="Enter sort order"></ion-input>
            </ion-item>
          </div>

          <!-- Pricing Information Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="pricetag-outline" class="section-icon"></ion-icon>
              Pricing Information
            </h3>

            <ion-item class="form-item">
              <ion-label position="stacked">MRP
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="number" name="mrp" [ngModel]="mrp" (ngModelChange)="updateMrp($event)"
                [required]="marginalItem" placeholder="0.00"></ion-input>
            </ion-item>
            <ion-row>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">PR Rate
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input ngModel type="number" name="pr_rate" [ngModel]="calculatedPrRate"
                    (ngModelChange)="calculatePrMargin($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="6" class="ion-align-self-end">
                <ion-button
                  fill="outline"
                  size="small"
                  (click)="openPurchaseCalculator('add')"
                  class="calculator-button">
                  <ion-icon name="calculator-outline" slot="start"></ion-icon>
                  Calculate PR Rate
                </ion-button>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">PR Margin
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input ngModel type="number" name="pr_margin" [ngModel]="calculatedPrMargin"
                    (ngModelChange)="calculatePrRate($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">Selling Rate
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input ngModel type="number" name="rate" [ngModel]="calculatedSellingRate"
                    (ngModelChange)="calculateSellingMargin($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">Margin
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input ngModel type="number" name="margin" [ngModel]="calculatedSellingMargin"
                    (ngModelChange)="calculateSellingRate($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">Tax Rate
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input ngModel type="number" name="tax_rate" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
            </ion-row>
          </div>

          <!-- Product Configuration Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="settings-outline" class="section-icon"></ion-icon>
              Product Configuration
            </h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Unit Contents
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="number" name="unit_contains" [required]="marginalItem" placeholder="1"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label>Is Crate Based Product</ion-label>
              <ion-toggle
                ngModel
                name="is_crate_based"
                [ngModel]="isCrateBased"
                (ngModelChange)="onCrateBasedToggle($event)"
                slot="end">
              </ion-toggle>
            </ion-item>

            <ion-item class="form-item" *ngIf="isCrateBased">
              <ion-label position="stacked">Crate Size
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input
                ngModel
                type="number"
                name="crate_size"
                [ngModel]="crateSize"
                (ngModelChange)="updateCrateSize($event)"
                min="1"
                [required]="isCrateBased"
                placeholder="1">
              </ion-input>
            </ion-item>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <ion-button
              type="submit"
              [disabled]="form.invalid"
              expand="block"
              fill="solid"
              shape="round"
              color="primary"
              class="submit-button">
              <ion-icon name="add-outline" slot="start"></ion-icon>
              Add Product
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
  <!-- Edit Product Modal -->
  <ion-modal [isOpen]="isEditModalOpen" class="product-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="warning">
          <ion-title>Edit Product</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setEditOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <form #form="ngForm" (ngSubmit)="editProduct(form.value)">
          <input type="hidden" name="id" [(ngModel)]="editData.id" />

          <!-- Basic Information Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="information-circle-outline" class="section-icon"></ion-icon>
              Basic Information
            </h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Name
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.name" type="text" name="name" required="true" placeholder="Enter product name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Short Code
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.short_code" type="text" name="short_code" required="true" placeholder="Enter short code"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label for="amount">Brand</ion-label>
              <ionic-selectable itemValueField="id" itemTextField="name" [items]="brandData" [(ngModel)]="editData.brand"
                name="brand" [canSearch]="true" [canAddItem]="true" (onAddItem)="addBrand($event)"
                placeholder="Select or add a brand">
              </ionic-selectable>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Sort Order</ion-label>
              <ion-input [(ngModel)]="editData.sort_order" type="number" name="sort_order" required="false" placeholder="Enter sort order">
              </ion-input>
            </ion-item>
          </div>

          <!-- Pricing Information Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="pricetag-outline" class="section-icon"></ion-icon>
              Pricing Information
            </h3>

            <ion-item class="form-item">
              <ion-label position="stacked">MRP
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="mrp" type="number" name="mrp" (ngModelChange)="updateMrp($event)"
                [required]="marginalItem" placeholder="0.00"></ion-input>
            </ion-item>
            <ion-row>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">PR Rate
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input [(ngModel)]="calculatedPrRate" type="number" name="pr_rate"
                    (ngModelChange)="calculatePrMargin($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="6" class="ion-align-self-end">
                <ion-button
                  fill="outline"
                  size="small"
                  (click)="openPurchaseCalculator('edit')"
                  class="calculator-button">
                  <ion-icon name="calculator-outline" slot="start"></ion-icon>
                  Calculate PR Rate
                </ion-button>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">PR Margin
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input [(ngModel)]="calculatedPrMargin" type="number" name="pr_margin"
                    (ngModelChange)="calculatePrRate($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">Selling Rate
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input [(ngModel)]="calculatedSellingRate" type="number" name="rate"
                    (ngModelChange)="calculateSellingMargin($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">Margin
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input [(ngModel)]="calculatedSellingMargin" type="number" name="margin"
                    (ngModelChange)="calculateSellingRate($event)" [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">Tax Rate
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input [(ngModel)]="editData.tax_rate" type="number" name="tax_rate"
                    [required]="marginalItem" placeholder="0.00"></ion-input>
                </ion-item>
              </ion-col>
            </ion-row>
          </div>

          <!-- Product Configuration Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="settings-outline" class="section-icon"></ion-icon>
              Product Configuration
            </h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Unit Contents
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.unit_contains" type="number" name="unit_contains"
                [required]="marginalItem" placeholder="1">
              </ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label>Is Crate Based Product</ion-label>
              <ion-toggle
                [(ngModel)]="isCrateBased"
                name="is_crate_based"
                (ngModelChange)="onCrateBasedToggle($event)"
                slot="end">
              </ion-toggle>
            </ion-item>

            <ion-item class="form-item" *ngIf="isCrateBased">
              <ion-label position="stacked">Crate Size
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input
                [(ngModel)]="crateSize"
                type="number"
                name="crate_size"
                (ngModelChange)="updateCrateSize($event)"
                min="1"
                [required]="isCrateBased"
                placeholder="1">
              </ion-input>
            </ion-item>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <ion-button
              type="submit"
              [disabled]="form.invalid"
              expand="block"
              fill="solid"
              shape="round"
              color="warning"
              class="submit-button">
              <ion-icon name="create-outline" slot="start"></ion-icon>
              Update Product
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Purchase Rate Calculator Modal -->
  <ion-modal [isOpen]="isPurchaseCalculatorOpen" class="purchase-calculator-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="primary">
          <ion-title>
            <ion-icon name="calculator-outline" class="modal-title-icon"></ion-icon>
            Purchase Rate Calculator
          </ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="closePurchaseCalculator()" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <div class="calculator-container">

          <!-- Input Fields Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="create-outline" class="section-icon"></ion-icon>
              Input Fields
            </h3>

            <ion-row>
              <ion-col size="12">
                <ion-item class="form-item">
                  <ion-label position="stacked">Total Purchase Amount
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input
                    type="number"
                    [(ngModel)]="calculatorTotalPurchaseAmount"
                    (ngModelChange)="onCalculatorFieldChange()"
                    placeholder="0.00">
                  </ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">GST Included</ion-label>
                  <ion-checkbox
                    [(ngModel)]="calculatorGstIncluded"
                    (ngModelChange)="onCalculatorFieldChange()">
                  </ion-checkbox>
                </ion-item>
              </ion-col>
              <ion-col size="6">
                <ion-item class="form-item">
                  <ion-label position="stacked">Tax %</ion-label>
                  <ion-input
                    type="number"
                    [(ngModel)]="calculatorTaxPercent"
                    (ngModelChange)="onCalculatorFieldChange()"
                    min="0"
                    max="100"
                    placeholder="0.00">
                  </ion-input>
                </ion-item>
              </ion-col>
            </ion-row>

            <ion-row>
              <ion-col size="4">
                <ion-item class="form-item">
                  <ion-label position="stacked">Order Quantity
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input
                    type="number"
                    [(ngModel)]="calculatorOrderQuantity"
                    (ngModelChange)="onCalculatorFieldChange()"
                    min="1"
                    placeholder="1">
                  </ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="4">
                <ion-item class="form-item">
                  <ion-label position="stacked">Unit Contains
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input
                    type="number"
                    [(ngModel)]="calculatorUnitContains"
                    (ngModelChange)="onCalculatorFieldChange()"
                    min="1"
                    placeholder="1">
                  </ion-input>
                </ion-item>
              </ion-col>
              <ion-col size="4">
                <ion-item class="form-item">
                  <ion-label position="stacked">Crate Contains
                    <ion-text color="danger">*</ion-text>
                  </ion-label>
                  <ion-input
                    type="number"
                    [(ngModel)]="calculatorCrateContains"
                    (ngModelChange)="onCalculatorFieldChange()"
                    min="1"
                    placeholder="1">
                  </ion-input>
                </ion-item>
              </ion-col>
            </ion-row>


          </div>

          <!-- Calculation Results Section -->
          <div class="form-section calculation-section">
            <h3 class="section-title">
              <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
              Calculation Results
            </h3>

            <div class="calculation-step">
              <div class="step-header">
                <ion-icon name="chevron-forward-outline" class="step-icon"></ion-icon>
                <span class="step-title">Step 1: Tax Amount</span>
              </div>
              <div class="step-formula" *ngIf="!calculatorGstIncluded">
                {{ calculatorTotalPurchaseAmount | number:'1.2-2' }} × ({{ calculatorTaxPercent }}% ÷ 100)
              </div>
              <div class="step-formula" *ngIf="calculatorGstIncluded">
                {{ calculatorFinalTotalAmount | number:'1.2-2' }} × ({{ calculatorTaxPercent }}% ÷ (100 + {{ calculatorTaxPercent }}%))
              </div>
              <div class="step-result calculated-field">
                ₹{{ calculatorTaxAmount | number:'1.2-2' }}
              </div>
            </div>

            <div class="calculation-step">
              <div class="step-header">
                <ion-icon name="chevron-forward-outline" class="step-icon"></ion-icon>
                <span class="step-title">Step 2: Final Total Amount</span>
              </div>
              <div class="step-formula" *ngIf="!calculatorGstIncluded">
                {{ calculatorTotalPurchaseAmount | number:'1.2-2' }} + {{ calculatorTaxAmount | number:'1.2-2' }}
              </div>
              <div class="step-formula" *ngIf="calculatorGstIncluded">
                {{ calculatorTotalPurchaseAmount | number:'1.2-2' }} (GST Included)
              </div>
              <div class="step-result calculated-field">
                ₹{{ calculatorFinalTotalAmount | number:'1.2-2' }}
              </div>
            </div>

            <div class="calculation-step final-step">
              <div class="step-header">
                <ion-icon name="checkmark-circle-outline" class="step-icon"></ion-icon>
                <span class="step-title">Final: Purchase Rate (PR Rate)</span>
              </div>
              <div class="step-formula">
                {{ calculatorFinalTotalAmount | number:'1.2-2' }} ÷ ({{ calculatorOrderQuantity }} × {{ calculatorUnitContains }} × {{ calculatorCrateContains }})
              </div>
              <div class="step-result final-result">
                ₹{{ calculatorPrRate | number:'1.2-2' }}
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <ion-button
              fill="clear"
              color="medium"
              (click)="closePurchaseCalculator()"
              expand="block">
              Cancel
            </ion-button>
            <ion-button
              fill="solid"
              color="primary"
              (click)="applyCalculatedRate()"
              expand="block">
              <ion-icon name="checkmark-outline" slot="start"></ion-icon>
              Apply Rate (₹{{ calculatorPrRate | number:'1.2-2' }})
            </ion-button>
          </div>
        </div>
      </ion-content>
    </ng-template>
  </ion-modal>


</ion-content>

<ion-footer>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button (click)="setOpen(true)">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>