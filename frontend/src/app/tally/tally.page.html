<app-header [title]="'Tally Report'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="tally-content">
  <!-- Collapsible Summary Section - Added at Top -->
  <div class="summary-section" *ngIf="data">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        Tally Summary
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card purchase-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Purchase Total</h3>
                <h1>{{data?.purchaseInvoiceTotal | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Total purchases</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="arrow-down-circle" class="summary-icon purchase"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card sales-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Sales Total</h3>
                <h1>{{data?.salesInvoiceTotal | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Total sales</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="arrow-up-circle" class="summary-icon sales"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card expense-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Expense Total</h3>
                <h1>{{data?.expenseTotal | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Total expenses</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="wallet" class="summary-icon expense"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card difference-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Net Difference</h3>
                <h1>{{((+data?.purchaseInvoiceTotal + +data?.expenseTotal) - +data?.salesInvoiceTotal) | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>P&E - Sales</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="calculator" class="summary-icon difference"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row *ngIf="enablePR">
      <ion-col size="12">
        <ion-card class="summary-card pr-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>PR Total</h3>
                <h1>{{(pr_total - +data?.expenseTotal) | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>PR minus expenses</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trending-up" class="summary-icon pr"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    </div>
  </div>

  <!-- Collapsible Filter Section -->
  <div class="filter-section">
    <div class="filter-header" (click)="toggleFilters()">
      <h3 class="section-title">
        <ion-icon name="calendar-outline" class="section-icon"></ion-icon>
        Date Range & Filter
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showFilters ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="filter-content" [class.expanded]="showFilters">
      <ion-card class="filter-card">
        <ion-card-content>
          <ion-row>
            <ion-col size="5">
              <ion-item lines="none" class="date-item">
                <ion-label position="stacked">From Date</ion-label>
                <ion-input
                  [(ngModel)]="from_date"
                  [value]="from_date | date : 'YYYY-MM-dd'"
                  placeholder="Enter From Date"
                  type="date"
                  class="date-input">
                </ion-input>
              </ion-item>
            </ion-col>

            <ion-col size="5">
              <ion-item lines="none" class="date-item">
                <ion-label position="stacked">To Date</ion-label>
                <ion-input
                  [(ngModel)]="to_date"
                  [value]="to_date | date : 'YYYY-MM-dd'"
                  placeholder="Enter To Date"
                  type="date"
                  class="date-input">
                </ion-input>
              </ion-item>
            </ion-col>

            <ion-col size="2">
              <ion-button
                (click)="filterData()"
                fill="solid"
                expand="block"
                class="filter-button">
                <ion-icon name="search" slot="icon-only"></ion-icon>
              </ion-button>
            </ion-col>
          </ion-row>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
  <!-- Segment Section -->
  <div class="segment-section" *ngIf="data">
    <div class="segment-header">
      <h3 class="section-title">
        <ion-icon name="list-outline" class="section-icon"></ion-icon>
        Detailed Reports
      </h3>
      <div class="segment-actions">
        <ion-button
          fill="clear"
          size="small"
          (click)="printCurrentReport()"
          class="action-button print-button">
          <ion-icon name="print-outline" slot="icon-only"></ion-icon>
        </ion-button>
        <ion-button
          fill="clear"
          size="small"
          (click)="openFullScreenModal()"
          class="action-button fullscreen-button">
          <ion-icon name="expand-outline" slot="icon-only"></ion-icon>
        </ion-button>
      </div>
    </div>

    <ion-segment value="product" (ionChange)="segmentChanged($event)" class="custom-segment">
      <ion-segment-button value="product" class="segment-button">
        <ion-icon name="cube-outline"></ion-icon>
        <ion-label>Products</ion-label>
      </ion-segment-button>
      <ion-segment-button value="purchase" class="segment-button">
        <ion-icon name="arrow-down-circle-outline"></ion-icon>
        <ion-label>Purchases</ion-label>
      </ion-segment-button>
      <ion-segment-button value="sales" class="segment-button">
        <ion-icon name="arrow-up-circle-outline"></ion-icon>
        <ion-label>Sales</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>
  <!-- Product Report Table -->
  <div class="table-section" *ngIf="view == 'product' && data">
    <ion-card class="data-card">
      <ion-card-header>
        <ion-card-title class="table-title">
          <ion-icon name="cube-outline" class="table-icon"></ion-icon>
          Product Analysis Report
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr class="table-header">
                <th>Product Name</th>
                <th>Sales {{getFieldName('box')}}</th>
                <th>Sales {{getFieldName('pcs')}}</th>
                <th>Sales Amount</th>
                <th *ngIf="enablePR">PR Amount</th>
                <th>Purchase {{getFieldName('box')}}</th>
                <th>Purchase {{getFieldName('pcs')}}</th>
                <th>Purchase Amount</th>
                <th>Closing {{getFieldName('box')}}</th>
                <th>Closing {{getFieldName('pcs')}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let d of display_data" class="table-row">
                <td class="product-name">{{d.name}}</td>
                <td class="number-cell">{{d.sales_no}}</td>
                <td class="number-cell">{{d.sales_weight}}</td>
                <td class="amount-cell">{{d.sales_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                <td *ngIf="enablePR" class="amount-cell">{{d.pr_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                <td class="number-cell">{{d.purchase_no}}</td>
                <td class="number-cell">{{d.purchase_weight}}</td>
                <td class="amount-cell">{{d.purchase_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                <td class="number-cell">{{d.closing_no}}</td>
                <td class="number-cell">{{d.closing_weight}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
  <!-- Purchase Report Table -->
  <div class="table-section" *ngIf="view == 'purchase' && data">
    <ion-card class="data-card">
      <ion-card-header>
        <ion-card-title class="table-title">
          <ion-icon name="arrow-down-circle-outline" class="table-icon"></ion-icon>
          Purchase Report
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr class="table-header">
                <th>Supplier Name</th>
                <th>Bill Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let p of data.purchaseInvoice" class="table-row">
                <td class="supplier-name">{{p.suplier__name}}</td>
                <td class="amount-cell">{{p.bill_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Sales Report Table -->
  <div class="table-section" *ngIf="view == 'sales' && data">
    <ion-card class="data-card">
      <ion-card-header>
        <ion-card-title class="table-title">
          <ion-icon name="arrow-up-circle-outline" class="table-icon"></ion-icon>
          Sales Report
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr class="table-header">
                <th>Buyer Name</th>
                <th>Bill Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let p of data.salesInvoice" class="table-row">
                <td class="buyer-name">{{p.buyer__name}}</td>
                <td class="amount-cell">{{p.bill_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>

<!-- Full Screen Modal -->
<ion-modal [isOpen]="isFullScreenModalOpen" class="fullscreen-modal">
  <ng-template>
    <ion-header translucent>
      <ion-toolbar color="primary">
        <ion-title>
          <ion-icon [name]="getSegmentIcon(view)" class="modal-title-icon"></ion-icon>
          {{getSegmentTitle(view)}} Report
        </ion-title>
        <ion-buttons slot="end">
          <ion-button
            fill="clear"
            (click)="printCurrentReport()"
            color="light"
            class="modal-action-button">
            <ion-icon name="print-outline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button
            fill="clear"
            (click)="closeFullScreenModal()"
            color="light"
            class="modal-action-button">
            <ion-icon name="contract-outline" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="fullscreen-content">
      <!-- Enhanced Segment Navigation -->
      <div class="fullscreen-segment-section">
        <ion-segment
          [value]="view"
          (ionChange)="segmentChanged($event)"
          class="fullscreen-segment">
          <ion-segment-button value="product" class="fullscreen-segment-button">
            <ion-icon name="cube-outline"></ion-icon>
            <ion-label>Products</ion-label>
          </ion-segment-button>
          <ion-segment-button value="purchase" class="fullscreen-segment-button">
            <ion-icon name="arrow-down-circle-outline"></ion-icon>
            <ion-label>Purchases</ion-label>
          </ion-segment-button>
          <ion-segment-button value="sales" class="fullscreen-segment-button">
            <ion-icon name="arrow-up-circle-outline"></ion-icon>
            <ion-label>Sales</ion-label>
          </ion-segment-button>
        </ion-segment>
      </div>

      <!-- Enhanced Table Content -->
      <div class="fullscreen-table-section">
        <!-- Product Report Table -->
        <div *ngIf="view == 'product' && data" class="fullscreen-table-container">
          <ion-card class="fullscreen-data-card">
            <ion-card-header>
              <ion-card-title class="fullscreen-table-title">
                <ion-icon name="cube-outline" class="table-icon"></ion-icon>
                Product Analysis Report
                <span class="record-count">({{display_data?.length || 0}} products)</span>
              </ion-card-title>
            </ion-card-header>
            <ion-card-content>
              <div class="fullscreen-table-wrapper">
                <table class="fullscreen-data-table">
                  <thead>
                    <tr class="table-header">
                      <th>Product Name</th>
                      <th>Sales {{getFieldName('box')}}</th>
                      <th>Sales {{getFieldName('pcs')}}</th>
                      <th>Sales Amount</th>
                      <th *ngIf="enablePR">PR Amount</th>
                      <th>Purchase {{getFieldName('box')}}</th>
                      <th>Purchase {{getFieldName('pcs')}}</th>
                      <th>Purchase Amount</th>
                      <th>Closing {{getFieldName('box')}}</th>
                      <th>Closing {{getFieldName('pcs')}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let d of display_data" class="table-row">
                      <td class="product-name">{{d.name}}</td>
                      <td class="number-cell">{{d.sales_no}}</td>
                      <td class="number-cell">{{d.sales_weight}}</td>
                      <td class="amount-cell">{{d.sales_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                      <td *ngIf="enablePR" class="amount-cell">{{d.pr_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                      <td class="number-cell">{{d.purchase_no}}</td>
                      <td class="number-cell">{{d.purchase_weight}}</td>
                      <td class="amount-cell">{{d.purchase_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                      <td class="number-cell">{{d.closing_no}}</td>
                      <td class="number-cell">{{d.closing_weight}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </ion-card-content>
          </ion-card>
        </div>

        <!-- Purchase Report Table -->
        <div *ngIf="view == 'purchase' && data" class="fullscreen-table-container">
          <ion-card class="fullscreen-data-card">
            <ion-card-header>
              <ion-card-title class="fullscreen-table-title">
                <ion-icon name="arrow-down-circle-outline" class="table-icon"></ion-icon>
                Purchase Report
                <span class="record-count">({{data.purchaseInvoice?.length || 0}} records)</span>
              </ion-card-title>
            </ion-card-header>
            <ion-card-content>
              <div class="fullscreen-table-wrapper">
                <table class="fullscreen-data-table">
                  <thead>
                    <tr class="table-header">
                      <th>Supplier Name</th>
                      <th>Bill Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let p of data.purchaseInvoice" class="table-row">
                      <td class="supplier-name">{{p.suplier__name}}</td>
                      <td class="amount-cell">{{p.bill_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </ion-card-content>
          </ion-card>
        </div>

        <!-- Sales Report Table -->
        <div *ngIf="view == 'sales' && data" class="fullscreen-table-container">
          <ion-card class="fullscreen-data-card">
            <ion-card-header>
              <ion-card-title class="fullscreen-table-title">
                <ion-icon name="arrow-up-circle-outline" class="table-icon"></ion-icon>
                Sales Report
                <span class="record-count">({{data.salesInvoice?.length || 0}} records)</span>
              </ion-card-title>
            </ion-card-header>
            <ion-card-content>
              <div class="fullscreen-table-wrapper">
                <table class="fullscreen-data-table">
                  <thead>
                    <tr class="table-header">
                      <th>Buyer Name</th>
                      <th>Bill Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let p of data.salesInvoice" class="table-row">
                      <td class="buyer-name">{{p.buyer__name}}</td>
                      <td class="amount-cell">{{p.bill_amount | currency: 'INR':'symbol':'1.0-0'}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </ion-card-content>
          </ion-card>
        </div>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>

<ion-footer>
  <app-floating-menu></app-floating-menu>
</ion-footer>