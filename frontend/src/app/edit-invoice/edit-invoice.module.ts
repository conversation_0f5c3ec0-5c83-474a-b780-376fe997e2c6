import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { IonicModule } from "@ionic/angular";

import { EditInvoicePageRoutingModule } from "./edit-invoice-routing.module";

import { EditInvoicePage } from "./edit-invoice.page";
import { IonicSelectableModule } from "ionic-selectable";
import { SharedModule } from "../shared/modules/shared/shared.module";
import { SelectDropDownModule } from "ngx-select-dropdown";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    EditInvoicePageRoutingModule,
    SharedModule,
    ReactiveFormsModule,
    IonicSelectableModule,
    SelectDropDownModule

  ],
  declarations: [EditInvoicePage],
})
export class EditInvoicePageModule {}
