// Compact Header styling
ion-header {
  .compact-header {
    background: white;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .essential-row {
      margin-bottom: 8px;

      .compact-item {
        --padding-start: 8px;
        --padding-end: 8px;
        --min-height: 40px;
        margin: 0;

        ion-input, ionic-selectable {
          font-size: 14px;
        }
      }
    }

    .action-row {
      margin-bottom: 8px;

      ion-button {
        --height: 36px;
        margin: 0 2px;
        font-size: 12px;

        ion-icon {
          font-size: 18px;
        }
      }
    }

    .more-actions {
      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;

        ion-button {
          --height: 32px;
          --padding-start: 8px;
          --padding-end: 8px;
          font-size: 11px;
          flex: 1;
          min-width: 60px;

          ion-icon {
            font-size: 16px;
          }
        }
      }
    }

    .brand-row {
      ion-segment {
        --background: var(--ion-color-light);
        border-radius: 6px;
        height: 32px;

        ion-segment-button {
          --color: var(--ion-color-medium);
          --color-checked: var(--ion-color-primary);
          font-size: 11px;
          min-height: 28px;
        }
      }
    }
  }

    // Button group styling
    .button-group {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .primary-actions {
        ion-button {
          --border-radius: 12px;
          --height: 48px;
          font-weight: 600;
          font-size: 16px;

          ion-icon {
            font-size: 20px;
          }
        }
      }

      .secondary-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: center;

        ion-button {
          --border-radius: 8px;
          --height: 36px;
          font-size: 12px;
          flex: 1;
          min-width: 120px;

          &[size="small"] {
            --padding-start: 12px;
            --padding-end: 12px;
          }

          ion-icon {
            font-size: 16px;
          }
        }
      }
    }

    // Filter label styling
    .filter-label {
      display: flex;
      align-items: center;
      font-weight: 500;
      color: var(--ion-color-dark);
      margin-bottom: 8px;
      font-size: 14px;

      ion-icon {
        color: var(--ion-color-primary);
      }
    }

    // Segment styling
    ion-segment {
      margin-top: 12px;
      --background: var(--ion-color-light);
      border-radius: 8px;

      ion-segment-button {
        --color: var(--ion-color-medium);
        --color-checked: var(--ion-color-primary);
        --background-checked: var(--ion-color-primary-tint);
        font-size: 12px;
        min-height: 32px;
      }
    }
  }


// Main content styling
ion-content {
  --padding-start: 4px;
  --padding-end: 4px;
  --padding-bottom: 80px; // Space for sticky footer

  .main-content {
    background: var(--content-background);
    border-radius: 8px;
    margin: 4px 0;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    .header-row {
      background: var(--ion-color-light);
      font-weight: 600;
      color: var(--ion-color-dark);
      border-bottom: 2px solid var(--ion-color-primary);

      ion-col {
        padding: 8px 4px;
        text-align: center;
        border-right: 1px solid var(--ion-color-light-shade);

        &:last-child {
          border-right: none;
        }

        ion-label {
          font-size: 11px;
          font-weight: 600;
        }
      }
    }
  }
}

// Remove all selection highlighting and focus effects
* {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-appearance: none !important;
  appearance: none !important;
  outline: none !important;
  -webkit-highlight: none !important;
  -webkit-text-size-adjust: none !important;
  touch-action: manipulation !important;
}

// Remove text selection highlighting
::selection {
  background: transparent !important;
  color: inherit !important;
}

::-moz-selection {
  background: transparent !important;
  color: inherit !important;
}

::-webkit-selection {
  background: transparent !important;
  color: inherit !important;
}

// Remove focus outlines and highlights
*:focus,
*:active,
*:hover {
  outline: none !important;
  box-shadow: none !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  user-select: none !important;
}

// Remove input focus effects
ion-input:focus,
ion-input.ion-focused,
ion-searchbar:focus,
ion-searchbar.ion-focused {
  outline: none !important;
  box-shadow: none !important;
  --highlight-color: transparent !important;
}

// Remove button focus effects
ion-button:focus {
  outline: none !important;
  box-shadow: none !important;
}

// Remove item focus effects
ion-item:focus {
  outline: none !important;
  box-shadow: none !important;
}

// Specifically target invoice items and disable all selection
.invoice-items-container,
.invoice-items-card,
.invoice-item-row,
.item-info,
.item-code,
.item-name,
.item-details,
.detail-item,
.detail-label,
.detail-value {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-highlight: none !important;
  pointer-events: auto !important;
  touch-action: manipulation !important;
}

// Disable context menu on long press
.invoice-items-container * {
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

// Override any Ionic default selections
ion-content,
ion-card,
ion-row,
ion-col,
ion-label,
ion-text {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
}

.total {
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  color: #fff;
  font-weight: 600;
  border-radius: 8px;
  margin: 8px;
  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);

  ion-col {
    padding: 12px 8px;
    text-align: center;

    ion-label {
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.invoice-item {
  border-bottom: 1px solid var(--ion-color-light);
  transition: background-color 0.2s ease;



  ion-col {
    padding: 4px 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-right: 1px solid var(--ion-color-light);
    min-height: 40px;

    &:last-child {
      border-right: none;
    }

    ion-input {
      --padding-start: 4px;
      --padding-end: 4px;
      --background: transparent;
      --border-radius: 4px;
      font-size: 12px;
      text-align: center;

      &:focus-within {
        --background: var(--ion-color-primary-tint);
        --color: var(--ion-color-primary-contrast);
      }
    }

    ion-text {
      font-size: 11px;
      font-weight: 500;
      color: var(--ion-color-dark);
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 3px;
      transition: all 0.2s ease;


    }

    ion-label {
      font-size: 12px;
      font-weight: 500;
      color: var(--ion-color-dark);
    }
  }
}

.bottom-item {
  background: white;
  border-radius: 8px;
  margin: 4px 0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  ion-col {
    padding: 12px 8px;
    border-bottom: 1px solid var(--ion-color-light);
    display: flex;
    align-items: center;
    min-height: 48px;

    &:nth-child(odd) {
      background-color: var(--ion-color-light-tint);
      font-weight: 500;
      color: var(--ion-color-dark);
    }

    &:nth-child(even) {
      justify-content: flex-end;
    }

    ion-input {
      --padding-start: 12px;
      --padding-end: 12px;
      --background: white;
      --border-radius: 8px;
      --border-width: 1px;
      --border-color: var(--ion-color-light-shade);
      font-size: 14px;
      text-align: right;

      &:focus-within {
        --border-color: var(--ion-color-primary);
        --background: var(--ion-color-primary-tint);
      }
    }
  }
}

// Footer styling
ion-footer {
  background: white;
  border-top: 1px solid var(--ion-color-light);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

  // Compact search section
  .search-section {
    padding: 8px;

    ion-searchbar {
      --background: var(--ion-color-light-tint);
      --border-radius: 8px;
      --box-shadow: none;
      --color: var(--ion-color-dark);
      --placeholder-color: var(--ion-color-medium);
      --height: 40px;
      margin-bottom: 8px;

      .searchbar-input {
        font-size: 14px;
      }
    }

    .empty-state {
      text-align: center;
      padding: 12px;

      p {
        margin: 8px 0;
        font-size: 12px;
        color: var(--ion-color-medium);
      }

      ion-button {
        --height: 32px;
        font-size: 11px;
      }
    }
  }

  // Sticky summary footer
  .sticky-summary {
    background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
    color: white;
    padding: 8px;

    .summary-row {
      margin: 0;

      .summary-col {
        padding: 4px;
        text-align: center;

        &.total-col {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        .summary-item {
          .summary-label {
            display: block;
            font-size: 10px;
            font-weight: 500;
            opacity: 0.9;
            margin-bottom: 2px;
          }

          .summary-value {
            display: block;
            font-size: 14px;
            font-weight: 600;
          }
        }

        .total-item {
          .summary-value {
            font-size: 16px;
            font-weight: 700;
          }
        }
      }
    }

    // Rounding controls row
    .rounding-row {
      background: rgba(255, 255, 255, 0.05);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      margin: 0;

      .rounding-col {
        padding: 6px 8px;

        .rounding-details {
          .rounding-breakdown {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .breakdown-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 11px;

              .breakdown-label {
                opacity: 0.9;
                font-weight: 500;
              }

              .breakdown-value {
                font-weight: 600;
              }

              &.rounding-adjustment {
                .breakdown-value {
                  color: rgba(255, 255, 255, 0.95);
                  font-weight: 700;
                }
              }
            }
          }
        }
      }
    }
  }
}

  // Accordion styling
  ion-accordion-group {
    margin: 8px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    ion-accordion {
      ion-item[slot="header"] {
        --background: var(--ion-color-primary);
        --color: white;
        font-weight: 600;
        --padding-start: 16px;
        --padding-end: 16px;

        ion-label {
          font-size: 16px;
          font-weight: 600;
        }
      }

      [slot="content"] {
        --background: var(--content-background);
        padding: 8px;
      }
    }
  }
  
  .header-label{
    color: var(--ion-color-light) !important;
  }

// Payment Modal Styling
.payment-modal-content {
  --padding-start: 16px;
  --padding-end: 16px;

  h3 {
    color: var(--ion-color-primary);
    font-size: 18px;
    font-weight: 600;
    margin: 16px 0 12px 0;

    &:first-child {
      margin-top: 0;
    }
  }

  h4 {
    color: var(--ion-color-dark);
    font-size: 16px;
    font-weight: 500;
    margin: 12px 0 8px 0;
  }

  .balance-section, .additional-fields, .payment-section {
    margin-bottom: 20px;

    .balance-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid var(--ion-color-light);

      &.final {
        border-bottom: none;
        border-top: 2px solid var(--ion-color-primary);
        padding-top: 16px;
        margin-top: 16px;

        .final-amount {
          font-size: 20px;
          font-weight: 700;
          color: var(--ion-color-primary);
        }
      }

      span:first-child {
        font-size: 14px;
        font-weight: 500;
        color: var(--ion-color-medium);
      }

      span:last-child {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
      }
    }
  }

  .field-container {
    margin-bottom: 16px;

    .discount-field {
      h4 {
        margin-bottom: 12px;
      }
    }
  }

  .final-balance {
    background: linear-gradient(135deg, var(--ion-color-primary-tint), var(--ion-color-light));
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
  }

  ion-item {
    --padding-start: 12px;
    --padding-end: 12px;
    --background: var(--ion-color-light-tint);
    border-radius: 8px;
    margin-bottom: 12px;

    ion-label {
      font-size: 12px;
      font-weight: 500;
      color: var(--ion-color-medium);
    }

    ion-input {
      --padding-start: 8px;
      --padding-end: 8px;
      font-size: 14px;
    }
  }
}

/* Product Creation Button Styling */
.create-product-btn {
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  --height: 32px;
  font-size: 11px;

  ion-icon {
    margin-right: 4px;
    font-size: 14px;
  }
}

// Enhanced Invoice Items Container
.invoice-items-container {
  margin: 8px 4px;

  .invoice-items-card {
    --background: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin: 0;

    // Header Section
    .invoice-items-header {
      background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);

      .header-row {
        margin: 0;

        .header-col {
          padding: 12px 8px;
          text-align: center;
          border-right: 1px solid rgba(255, 255, 255, 0.2);

          &:last-child {
            border-right: none;
          }

          .header-label {
            color: white;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }

    // Content Section
    .invoice-items-content {
      background: white;

      .invoice-item-row {
        margin: 0;
        border-bottom: 1px solid var(--ion-color-light);
        transition: background-color 0.2s ease;



        &:last-child {
          border-bottom: none;
        }

        &.has-values {
          background-color: rgba(var(--ion-color-primary-rgb), 0.02);
          border-left: 3px solid var(--ion-color-primary);
        }

        // Column Styling
        ion-col {
          padding: 0;
          min-height: 48px;
          display: flex;
          align-items: center;
          border-right: 1px solid var(--ion-color-light);

          &:last-child {
            border-right: none;
          }
        }

        // Item Column
        .item-col {
          .item-info {
            padding: 10px 14px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.2s ease;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;



            .item-code {
              display: block;
              font-weight: 700;
              font-size: 16px;
              color: var(--ion-color-dark);
              margin-bottom: 4px;
              line-height: 1.2;
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;
            }

            .item-name {
              display: block;
              font-size: 16px;
              color: var(--ion-color-dark);
              opacity: 1;
              margin-bottom: 6px;
              line-height: 1.4;
              font-weight: 700;
              letter-spacing: 0.2px;
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;
            }

            .item-details {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              margin-top: 4px;

              .detail-item {
                display: flex;
                align-items: center;
                font-size: 11px;
                color: var(--ion-color-medium);
                background: var(--ion-color-light-tint);
                padding: 2px 6px;
                border-radius: 4px;

                .detail-label {
                  font-weight: 600;
                  margin-right: 2px;
                }

                .detail-value {
                  font-weight: 500;

                  &.mrp {
                    color: var(--ion-color-warning);
                  }

                  &.rate {
                    color: var(--ion-color-success);
                  }

                  &.margin {
                    color: var(--ion-color-tertiary);
                  }

                  &.tax {
                    color: var(--ion-color-secondary);
                  }
                }
              }
            }
          }
        }

        // Input Columns
        .input-col {
          .input-wrapper {
            width: 100%;
            padding: 4px 8px;

            .invoice-input {
              --background: transparent;
              --padding-start: 8px;
              --padding-end: 8px;
              --padding-top: 8px;
              --padding-bottom: 8px;
              --border-radius: 6px;
              --border-width: 1px;
              --border-style: solid;
              --border-color: transparent;
              --color: var(--ion-color-dark);
              font-size: 16px;
              font-weight: 600;
              transition: all 0.2s ease;



              &.ion-focused {
                --background: white;
                --border-color: var(--ion-color-primary);
                --border-width: 2px;
                box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.1);
              }

              &.rate-input {
                --color: var(--ion-color-success);
                font-weight: 600;
              }

              &.pcs-input, &.box-input {
                --color: var(--ion-color-tertiary);
                font-weight: 600;
                text-align: center;
              }

              &.desc-input {
                --color: var(--ion-color-medium);
                font-style: italic;
              }

              &:disabled {
                --background: var(--ion-color-light);
                --color: var(--ion-color-medium);
                opacity: 0.6;
              }
            }
          }
        }

        // Display Column (for popover weight)
        .display-col {
          .display-wrapper {
            width: 100%;
            padding: 12px;
            cursor: pointer;
            text-align: center;
            transition: background-color 0.2s ease;



            .display-value {
              font-weight: 700;
              font-size: 16px;
              color: var(--ion-color-tertiary);
            }
          }
        }

        // Total Column
        .total-col {
          .total-wrapper {
            width: 100%;
            padding: 12px;
            text-align: right;

            .total-amount {
              font-weight: 800;
              font-size: 16px;
              color: var(--ion-color-primary);
              background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }
          }
        }
      }
    }
  }
}

// Responsive design for invoice items
@media (max-width: 768px) {
  .invoice-items-container {
    margin: 4px 2px;

    .invoice-items-card {
      border-radius: 8px;

      .invoice-items-header {
        .header-row {
          .header-col {
            padding: 8px 4px;

            .header-label {
              font-size: 10px;
            }
          }
        }
      }

      .invoice-items-content {
        .invoice-item-row {
          ion-col {
            min-height: 44px;
          }

          .item-col {
            .item-info {
              padding: 8px 10px;

              .item-code {
                font-size: 14px;
              }

              .item-name {
                font-size: 15px;
                font-weight: 700;
              }

              .item-details {
                .detail-item {
                  font-size: 10px;
                  padding: 1px 4px;
                }
              }
            }
          }

          .input-col {
            .input-wrapper {
              padding: 2px 4px;

              .invoice-input {
                --padding-start: 6px;
                --padding-end: 6px;
                --padding-top: 6px;
                --padding-bottom: 6px;
                font-size: 15px;
              }
            }
          }

          .display-col {
            .display-wrapper {
              padding: 8px;

              .display-value {
                font-size: 13px;
              }
            }
          }

          .total-col {
            .total-wrapper {
              padding: 8px;

              .total-amount {
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .invoice-items-container {
    margin: 2px 1px;

    .invoice-items-card {
      border-radius: 6px;

      .invoice-items-header {
        .header-row {
          .header-col {
            padding: 6px 2px;

            .header-label {
              font-size: 9px;
            }
          }
        }
      }

      .invoice-items-content {
        .invoice-item-row {
          ion-col {
            min-height: 40px;
          }

          .item-col {
            .item-info {
              padding: 6px 8px;

              .item-code {
                font-size: 13px;
              }

              .item-name {
                font-size: 14px;
                font-weight: 700;
              }

              .item-details {
                .detail-item {
                  font-size: 9px;
                  padding: 1px 3px;
                  gap: 4px;
                }
              }
            }
          }

          .input-col {
            .input-wrapper {
              padding: 1px 2px;

              .invoice-input {
                --padding-start: 4px;
                --padding-end: 4px;
                --padding-top: 4px;
                --padding-bottom: 4px;
                font-size: 12px;
              }
            }
          }

          .display-col {
            .display-wrapper {
              padding: 6px;

              .display-value {
                font-size: 12px;
              }
            }
          }

          .total-col {
            .total-wrapper {
              padding: 6px;

              .total-amount {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// Enhanced Visual Feedback and Accessibility
.invoice-items-container {
  .invoice-items-card {
    // Loading state
    &.loading {
      opacity: 0.7;
      pointer-events: none;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
      }
    }

    .invoice-items-content {
      .invoice-item-row {
        // Enhanced focus management
        &:focus-within {
          background-color: rgba(var(--ion-color-primary-rgb), 0.05);
          box-shadow: inset 0 0 0 2px var(--ion-color-primary-tint);
        }

        // Error state
        &.error {
          background-color: rgba(var(--ion-color-danger-rgb), 0.05);
          border-left: 3px solid var(--ion-color-danger);

          .invoice-input {
            --border-color: var(--ion-color-danger);
            --color: var(--ion-color-danger);
          }
        }

        // Success state (when item has valid values)
        &.success {
          background-color: rgba(var(--ion-color-success-rgb), 0.02);
          border-left: 3px solid var(--ion-color-success);
        }

        .input-col {
          .input-wrapper {
            .invoice-input {
              // Enhanced focus states
              &.ion-focused {
                transform: scale(1.02);
                z-index: 5;
                position: relative;
              }

              // Invalid state
              &.ion-invalid {
                --border-color: var(--ion-color-danger);
                --color: var(--ion-color-danger);
                animation: shake 0.3s ease-in-out;
              }

              // Valid state
              &.ion-valid {
                --border-color: var(--ion-color-success);

                &::after {
                  position: absolute;
                  right: 8px;
                  top: 50%;
                  transform: translateY(-50%);
                  color: var(--ion-color-success);
                  font-weight: bold;
                }
              }
            }
          }
        }

        .item-col {
          .item-info {
            // Enhanced click feedback
            &:active {
              transform: scale(0.98);
              background-color: var(--ion-color-primary);
              color: white;

              .item-code, .item-name {
                color: white;
              }
            }
          }
        }

        .total-col {
          .total-wrapper {
            .total-amount {
              // Animated total updates
              transition: all 0.3s ease;

              &.updated {
                animation: pulse 0.5s ease-in-out;
              }
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

// Accessibility improvements
.invoice-items-container {
  .invoice-items-card {
    // High contrast mode support
    @media (prefers-contrast: high) {
      .invoice-items-header {
        background: var(--ion-color-dark);

        .header-label {
          color: var(--ion-color-light);
          font-weight: 700;
        }
      }

      .invoice-items-content {
        .invoice-item-row {
          border-bottom: 2px solid var(--ion-color-dark);

          .invoice-input {
            --border-width: 2px;
            --border-color: var(--ion-color-dark);
          }
        }
      }
    }

    // Reduced motion support
    @media (prefers-reduced-motion: reduce) {
      .invoice-item-row,
      .invoice-input,
      .item-info,
      .total-amount {
        transition: none;
        animation: none;
      }
    }

    // Focus visible for keyboard navigation
    .invoice-item-row {
      .invoice-input {
        &:focus-visible {
          outline: 3px solid var(--ion-color-primary);
          outline-offset: 2px;
        }
      }

      .item-info {
        &:focus-visible {
          outline: 3px solid var(--ion-color-primary);
          outline-offset: 2px;
        }
      }
    }
  }
}

// Payment Details Footer
.payment-details-footer {
  background: white;
  border-top: 2px solid var(--ion-color-primary);
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;

  .payment-content {
    .balance-info {
      margin-bottom: 12px;

      .balance-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        font-size: 14px;

        &.final {
          border-top: 1px solid var(--ion-color-primary);
          padding-top: 8px;
          margin-top: 8px;
          font-weight: 600;

          .final-amount {
            color: var(--ion-color-primary);
            font-size: 16px;
            font-weight: 700;
          }
        }
      }
    }

    .discount-section {
      margin-bottom: 12px;

      .discount-item {
        margin-bottom: 8px;

        .discount-inputs {
          display: flex;
          align-items: center;
          gap: 8px;

          .discount-label {
            font-size: 12px;
            font-weight: 500;
            min-width: 60px;
          }

          .discount-percent, .discount-amount {
            --background: var(--ion-color-light-tint);
            --padding-start: 8px;
            --padding-end: 8px;
            border-radius: 4px;
            font-size: 12px;
            flex: 1;
            max-width: 80px;
          }
        }

        .other-field {
          display: flex;
          align-items: center;
          gap: 8px;

          .field-label {
            font-size: 12px;
            font-weight: 500;
            min-width: 80px;
          }

          .field-input {
            --background: var(--ion-color-light-tint);
            --padding-start: 8px;
            --padding-end: 8px;
            border-radius: 4px;
            font-size: 12px;
            flex: 1;
          }
        }
      }
    }

    .payment-inputs {
      margin-bottom: 12px;

      .payment-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .payment-label {
          font-size: 12px;
          font-weight: 500;
          min-width: 100px;
        }

        .payment-amount {
          --background: var(--ion-color-light-tint);
          --padding-start: 8px;
          --padding-end: 8px;
          border-radius: 4px;
          font-size: 12px;
          flex: 1;
        }

        .payment-mode-btn {
          --background: var(--ion-color-light-tint);
          --color: var(--ion-color-dark);
          --border-color: var(--ion-color-medium);
          --border-radius: 4px;
          --padding-start: 8px;
          --padding-end: 8px;
          font-size: 12px;
          flex: 1;
          text-align: left;
          justify-content: space-between;

          ion-icon {
            margin-left: auto;
          }
        }
      }
    }

    .final-balance-footer {
      border-top: 2px solid var(--ion-color-primary);
      padding-top: 8px;

      .balance-row.final {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;

        .final-amount {
          color: var(--ion-color-primary);
          font-size: 16px;
          font-weight: 700;
        }
      }
    }
  }
}

// Enhanced search and filter section
.filter-section {
  background: white;
  margin: 8px;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  ion-text {
    color: var(--ion-color-medium);
    font-size: 14px;
    text-align: center;

    p {
      margin: 8px 0;
      line-height: 1.4;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  ion-header ion-card {
    margin: 4px;

    ion-button {
      font-size: 11px;
      height: 32px;
      margin: 2px;

      &[size="small"] {
        --padding-start: 8px;
        --padding-end: 8px;
      }
    }
  }

  .invoice-item ion-col {
    min-height: 44px;
    padding: 6px;

    ion-input {
      font-size: 13px;
    }
  }

  .bottom-item ion-col {
    min-height: 44px;
    padding: 10px 6px;

    ion-input {
      font-size: 13px;
    }
  }
}

// Loading and empty states
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--ion-color-medium);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);

  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h3 {
    margin: 16px 0 8px 0;
    color: var(--ion-color-dark);
  }

  p {
    margin: 0 0 20px 0;
    line-height: 1.4;
  }
}

// Rounding section styling
.rounding-section,
.invoice-breakdown-section {
  background: var(--ion-color-light-tint);
  border-radius: 8px;
  margin: 8px;
  padding: 12px;
  border: 1px solid var(--ion-color-light-shade);

  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: "⚙️";
      font-size: 18px;
    }
  }

  .rounding-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .rounding-toggle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid var(--ion-color-light-shade);

      .rounding-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--ion-color-dark);
      }

      .rounding-toggle-btn {
        --handle-width: 20px;
        --handle-height: 20px;
        --track-width: 40px;
        --track-height: 24px;
      }
    }

    .rounding-method {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid var(--ion-color-light-shade);

      .rounding-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--ion-color-dark);
        flex: 1;
      }

      .rounding-select {
        flex: 1;
        max-width: 200px;
        --padding-start: 8px;
        --padding-end: 8px;
      }
    }
  }

  .rounding-breakdown {
    margin-top: 12px;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid var(--ion-color-light-shade);

    ion-item {
      --padding-start: 12px;
      --padding-end: 12px;
      --min-height: 44px;

      ion-label {
        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 600;
          color: var(--ion-color-dark);
        }

        p {
          margin: 2px 0 0 0;
          font-size: 12px;
          color: var(--ion-color-medium);
        }
      }

      ion-note {
        font-size: 14px;
        font-weight: 600;

        &[color="primary"] {
          font-size: 16px;
          font-weight: 700;
        }

        &[color="success"] {
          color: var(--ion-color-success);
        }

        &[color="danger"] {
          color: var(--ion-color-danger);
        }
      }
    }
  }
}

.invoice-form {
  --background: var(--ion-card-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.invoice-modal {
  --background: var(--ion-card-background);
  --border-radius: 16px;
  --width: 95%;
  --max-width: 600px;
  --height: 90%;
  --max-height: 800px;
}
