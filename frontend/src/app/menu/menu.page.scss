/* Force Light Theme for Dashboard - Override any system dark mode */
:host {
  --ion-background-color: #ffffff !important;
  --ion-background-color-rgb: 255, 255, 255 !important;
  --ion-text-color: #000000 !important;
  --ion-text-color-rgb: 0, 0, 0 !important;
  --ion-border-color: #c1c4cd !important;
  --ion-item-background: #ffffff !important;
  --ion-toolbar-background: #ffffff !important;
  --ion-tab-bar-background: #ffffff !important;
  --ion-card-background: #ffffff !important;
  --app-background: #ffffff !important;
  --card-background: #ffffff !important;
  --section-background: #ffffff !important;
  --item-background: #ffffff !important;
  --header-background: #ffffff !important;
  --content-background: #ffffff !important;
  --modal-background: #ffffff !important;
  --form-background: #ffffff !important;
}

ion-content {
  --background: #ffffff !important;
  --color: #000000 !important;
}

ion-header {
  --background: #ffffff !important;
}

ion-toolbar {
  --background: #ffffff !important;
  --color: #000000 !important;
}

ion-card {
  --background: #ffffff !important;
  --color: #000000 !important;
}

ion-item {
  --background: #ffffff !important;
  --color: #000000 !important;
}

/* Dashboard Content */
.dashboard-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  background: #f8f9fa !important;
}

/* Welcome Card */
.welcome-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-card h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.welcome-card p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Section Titles */
.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Business Summary Section */
.business-summary-section {
  margin-bottom: 24px;
}

.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.revenue {
  color: #28a745;
}

.summary-icon.sales {
  color: #007bff;
}

.summary-icon.customers {
  color: #6f42c1;
}

.summary-icon.products {
  color: #fd7e14;
}

.revenue-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.sales-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

.customers-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.products-card {
  background: linear-gradient(135deg, #fff3e0 0%, #fef9f2 100%);
}

/* Statistics Cards */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.stat-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.stat-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.stat-icon {
  font-size: 48px;
  opacity: 0.8;
}

.stat-icon.active {
  color: #28a745;
}

.stat-icon.inactive {
  color: #ffc107;
}

.active-users {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.inactive-users {
  background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
}

/* Analytics Section */
.analytics-section {
  margin-bottom: 24px;
}

.chart-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.chart-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.chart-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 250px;
}

.chart-controls {
  margin-bottom: 16px;
}

.chart-controls ion-segment {
  --background: #f8f9fa;
  border-radius: 8px;
}

.chart-controls ion-segment-button {
  --color: #6c757d;
  --color-checked: var(--ion-color-primary);
  --background-checked: rgba(var(--ion-color-primary-rgb), 0.1);
  --border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* Activities Container */
.activities-container {
  max-height: 300px;
  overflow-y: auto;
}

/* Activity List Styling */
.activity-item {
  --background: transparent;
  --border-radius: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.activity-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(var(--ion-color-primary-rgb), 0.1);
  border-radius: 50%;
  margin-right: 12px;
}

.activity-icon {
  color: var(--ion-color-primary);
  font-size: 20px;
}

.activity-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.activity-description {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #6c757d;
}

.activity-time {
  margin: 0;
  font-size: 11px;
  color: #adb5bd;
}

.activity-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.activity-amount {
  font-size: 12px;
  font-weight: 600;
  color: #28a745;
}

/* No activities message */
.no-activities {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-activities-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 12px;
}

.no-activities p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Quick Access Section */
.quick-access-section {
  margin-bottom: 24px;
}

.quick-access-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.quick-access-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.secondary-action {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
}

.secondary-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.quick-access-card ion-card-content {
  text-align: center;
  padding: 20px;
}

.quick-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.quick-icon {
  font-size: 36px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.quick-icon.primary {
  color: var(--ion-color-primary);
}

.quick-icon.secondary {
  color: var(--ion-color-medium);
}

.quick-access-card:hover .quick-icon {
  transform: scale(1.1);
}

.quick-access-card h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.quick-access-card p {
  margin: 0;
  font-size: 11px;
  color: #6c757d;
  text-align: center;
}

.primary-action {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid rgba(var(--ion-color-primary-rgb), 0.1);
}

.primary-action:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.additional-menu-item {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Brand Performance Section */
.brand-performance-section {
  margin-bottom: 24px;
}

.brand-summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.brand-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.brand-icon {
  font-size: 20px;
  margin-right: 8px;
  color: #ffc107;
}

.brand-list {
  max-height: 400px;
  overflow-y: auto;
}

.brand-item {
  --background: transparent;
  --border-radius: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.brand-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.brand-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(var(--ion-color-primary-rgb), 0.1);
  border-radius: 50%;
  margin-right: 12px;
}

.rank-number {
  font-size: 14px;
  font-weight: 700;
  color: var(--ion-color-primary);
}

.brand-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.brand-stats {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.brand-performance {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.brand-amount {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.brand-growth {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.brand-growth.positive {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.brand-growth.negative {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.brand-growth ion-icon {
  font-size: 14px;
}

/* No brand data message */
.no-brand-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-data-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 12px;
}

.no-brand-data p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* User Management Section */
.user-management-section {
  margin-bottom: 24px;
}

.users-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.users-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.users-icon {
  font-size: 20px;
  margin-right: 8px;
  color: #ffc107;
}

.users-list {
  max-height: 300px;
  overflow-y: auto;
}

.user-item {
  --background: transparent;
  --border-radius: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.user-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(108, 117, 125, 0.1);
  border-radius: 50%;
  margin-right: 12px;
}

.user-icon {
  color: #6c757d;
  font-size: 24px;
}

.user-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.user-status {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  --color: #6c757d;
  --background: transparent;
  --border-radius: 8px;
}

.action-button:hover {
  --color: var(--ion-color-primary);
}

/* No inactive users message */
.no-inactive-users {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.success-icon {
  font-size: 48px;
  color: #28a745;
  margin-bottom: 12px;
}

.no-inactive-users p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Enhanced Buyer Management Styles */
.buyer-management-section {
  margin-bottom: 24px;
}

.buyers-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.no-inactive-buyers-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--ion-color-success);
}

.buyers-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.buyers-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-warning);
}

.buyers-list {
  max-height: 400px;
  overflow-y: auto;
}

.buyer-item {
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 16px;
  border-radius: 12px;
  background: #f8f9fa;
  border-left: 4px solid var(--ion-color-primary);
  transition: all 0.3s ease;
}

.buyer-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.buyer-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--ion-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.buyer-icon {
  font-size: 28px;
  color: white;
}

.buyer-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.buyer-location {
  font-size: 13px;
  color: #6c757d;
  margin: 0 0 6px 0;
  font-weight: 500;
}

.buyer-status {
  font-size: 14px;
  color: #495057;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.buyer-details {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.buyer-details ion-chip {
  font-size: 11px;
  height: 24px;
  --border-radius: 12px;
}

.buyer-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.action-button {
  --color: var(--ion-color-primary);
  --padding-start: 8px;
  --padding-end: 8px;
  width: 36px;
  height: 36px;
  --border-radius: 50%;
}

.action-button:hover {
  --background: var(--ion-color-primary-tint);
}

.no-inactive-buyers h4 {
  color: var(--ion-color-success);
  font-size: 18px;
  font-weight: 600;
  margin: 12px 0 8px 0;
}

.success-icon {
  font-size: 64px;
  color: var(--ion-color-success);
  margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 576px) {
  .dashboard-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .welcome-card h2 {
    font-size: 20px;
  }

  .welcome-card p {
    font-size: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .summary-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stat-info h3 {
    font-size: 12px;
  }

  .stat-info h1 {
    font-size: 24px;
  }

  .stat-info p {
    font-size: 10px;
  }

  .stat-icon {
    font-size: 32px;
  }

  .quick-access-card ion-card-content {
    padding: 12px;
  }

  .quick-icon {
    font-size: 28px;
  }

  .quick-access-card h4 {
    font-size: 12px;
  }

  .quick-access-card p {
    font-size: 10px;
  }

  .chart-container {
    min-height: 200px;
  }

  .chart-title {
    font-size: 16px;
  }

  .chart-icon {
    font-size: 18px;
  }

  .activities-container {
    max-height: 250px;
  }

  .activity-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .activity-icon {
    font-size: 16px;
  }

  .users-list {
    max-height: 250px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .user-icon {
    font-size: 20px;
  }
}

@media (min-width: 768px) {
  .dashboard-content {
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .welcome-card h2 {
    font-size: 28px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }

  .stat-info h1 {
    font-size: 36px;
  }

  .stat-icon {
    font-size: 56px;
  }

  .quick-access-card ion-card-content {
    padding: 24px;
  }

  .quick-icon {
    font-size: 40px;
  }

  .quick-access-card h4 {
    font-size: 15px;
  }

  .quick-access-card p {
    font-size: 12px;
  }

  .chart-container {
    min-height: 300px;
  }

  .chart-title {
    font-size: 20px;
  }

  .chart-icon {
    font-size: 22px;
  }

  .activities-container {
    max-height: 350px;
  }

  .users-list {
    max-height: 350px;
  }
}

@media (min-width: 1024px) {
  .dashboard-content {
    --padding-start: 32px;
    --padding-end: 32px;
  }

  .welcome-card h2 {
    font-size: 32px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }

  .stat-info h1 {
    font-size: 40px;
  }

  .stat-icon {
    font-size: 64px;
  }

  .quick-access-card ion-card-content {
    padding: 28px;
  }

  .quick-icon {
    font-size: 44px;
  }

  .quick-access-card h4 {
    font-size: 16px;
  }

  .quick-access-card p {
    font-size: 13px;
  }

  .chart-container {
    min-height: 350px;
  }

  .chart-title {
    font-size: 22px;
  }

  .chart-icon {
    font-size: 24px;
  }

  .activities-container {
    max-height: 400px;
  }

  .users-list {
    max-height: 400px;
  }
}

.menu-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.menu-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}