<app-header [title]="'Dashboard'"></app-header>

<ion-content class="dashboard-content">
  <!-- Welcome Message Card -->
  <ion-card class="welcome-card">
    <ion-card-content>
      <h2>Welcome back, {{userName}}</h2>
      <p>Here's your business overview for today</p>
    </ion-card-content>
  </ion-card>

  <!-- Business Summary Section -->
  <div class="business-summary-section">
    <h3 class="section-title">
      <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
      Business Overview
    </h3>
    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card revenue-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Revenue ({{financialYearLabel}})</h3>
                <h1>{{businessMetrics.totalRevenue | currency: 'INR':'symbol':'1.2-2'}}</h1>
                <p>Current year total revenue</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trending-up" class="summary-icon revenue"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card sales-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Monthly Sales</h3>
                <h1>{{businessMetrics.monthlySales | currency: 'INR':'symbol':'1.2-2'}}</h1>
                <p>Current month sales</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="cash" class="summary-icon sales"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card customers-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Active Customers</h3>
                <h1>{{businessMetrics.activeCustomers || 0}}</h1>
                <p>Total active customers</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="people-circle" class="summary-icon customers"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card products-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Products</h3>
                <h1>{{businessMetrics.totalProducts || 0}}</h1>
                <p>Total inventory items</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="cube" class="summary-icon products"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>

  <!-- Quick Access Section (Preserving Original Menu Functionality) -->
  <div class="quick-access-section">
    <h3 class="section-title">
      <ion-icon name="flash-outline" class="section-icon"></ion-icon>
      Quick Actions
    </h3>

    <!-- Primary Actions -->
    <ion-row>
      <ion-col size="6" size-md="3">
        <ion-card class="quick-access-card secondary-action" (click)="createNewInvoice()">
          <ion-card-content>
            <div class="quick-card-content">
              <ion-icon name="add-circle" class="quick-icon secondary"></ion-icon>
              <h4>New Invoice</h4>
              <p>Create sales invoice</p>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="quick-access-card secondary-action" (click)="viewInvoices()">
          <ion-card-content>
            <div class="quick-card-content">
              <ion-icon name="receipt-outline" class="quick-icon secondary"></ion-icon>
              <h4>View Invoices</h4>
              <p>Manage sales bills</p>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="quick-access-card secondary-action" (click)="viewReports()">
          <ion-card-content>
            <div class="quick-card-content">
              <ion-icon name="document-text-outline" class="quick-icon secondary"></ion-icon>
              <h4>Reports</h4>
              <p>Business reports</p>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="quick-access-card secondary-action" (click)="viewLedger()">
          <ion-card-content>
            <div class="quick-card-content">
              <ion-icon name="analytics-outline" class="quick-icon secondary"></ion-icon>
              <h4>Ledger</h4>
              <p>Business insights</p>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="quick-access-card secondary-action" (click)="viewChecklists()">
          <ion-card-content>
            <div class="quick-card-content">
              <ion-icon name="clipboard-outline" class="quick-icon secondary"></ion-icon>
              <h4>Checklists</h4>
              <p>Task management</p>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="quick-access-card secondary-action" (click)="viewStoreVisits()">
          <ion-card-content>
            <div class="quick-card-content">
              <ion-icon name="camera-outline" class="quick-icon secondary"></ion-icon>
              <h4>Store Visits</h4>
              <p>Photo capture & visits</p>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

       <!-- Additional Quick Access Items from Original Menu -->
      <ng-container *ngIf="role != 'buyer' && quickAccessItems.length > 0">
        <ng-container *ngFor="let item of quickAccessItems">
          <ion-col size="6" size-md="3" *ngIf="item.active && authService.checkPermission('view',item.slug)">
            <ion-card class="quick-access-card additional-menu-item" (click)="navigateToQuickAccess(item)">
              <ion-card-content>
                <div class="quick-card-content">
                  <ion-icon [name]="item.icon || 'business-outline'" class="quick-icon secondary"></ion-icon>
                  <h4>{{item.title}}</h4>
                  <p>{{item.description || 'Business function'}}</p>
                </div>
              </ion-card-content>
            </ion-card>
          </ion-col>
        </ng-container>
      </ng-container>
    </ion-row>

   
  </div>



  <!-- Buyer Management Section -->
  <div class="buyer-management-section">
    <h3 class="section-title">
      <ion-icon name="people-circle-outline" class="section-icon"></ion-icon>
      Buyer Management
    </h3>

    <!-- Inactive Buyers Card -->
    <ion-card class="buyers-card" *ngIf="inactiveBuyers && inactiveBuyers.length > 0">
      <ion-card-header>
        <ion-card-title class="buyers-title">
          <ion-icon name="alert-circle-outline" class="buyers-icon"></ion-icon>
          Inactive Buyers Based on Purchasing Patterns
        </ion-card-title>
        <ion-card-subtitle>{{inactiveBuyers.length}} buyers requiring attention</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <div class="buyers-list">
          <ion-item *ngFor="let buyer of inactiveBuyers" class="buyer-item" lines="none">
            <!-- Buyer Avatar with Class Icon -->
            <div class="buyer-avatar" slot="start">
              <ion-icon [name]="buyer.buyerIcon || 'person-circle-outline'" class="buyer-icon"></ion-icon>
            </div>

            <!-- Buyer Information -->
            <ion-label>
              <h4 class="buyer-name">{{buyer.name}}</h4>
              <p class="buyer-location">{{buyer.place}} - {{buyer.contact_person || 'No Contact'}}</p>
              <ion-text class="buyer-status" [color]="buyer.statusColor">{{buyer.purchasing_status}}</ion-text>
              <div class="buyer-details">
                <ion-chip [color]="buyer.frequencyColor" size="small">
                  <ion-label>Expected: {{buyer.expected_frequency | titlecase}}</ion-label>
                </ion-chip>
                <ion-chip color="medium" size="small">
                  <ion-label>{{buyer.total_purchases}} purchases</ion-label>
                </ion-chip>
                <ion-chip color="tertiary" size="small">
                  <ion-label>₹{{buyer.total_purchase_amount | number:'1.0-0'}}</ion-label>
                </ion-chip>
                <ion-chip [color]="buyer.priority === 'critical' ? 'danger' : buyer.priority === 'high' ? 'warning' : 'medium'" size="small">
                  <ion-label>{{buyer.lastActive}}</ion-label>
                </ion-chip>
              </div>
            </ion-label>

            <!-- Buyer Actions -->
            <div class="buyer-actions" slot="end">
              <ion-button fill="clear" size="small" class="action-button" (click)="contactBuyer(buyer)">
                <ion-icon name="call-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button fill="clear" size="small" class="action-button" (click)="viewBuyerDetails(buyer)">
                <ion-icon name="information-circle-outline" slot="icon-only"></ion-icon>
              </ion-button>
            </div>
          </ion-item>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- No inactive buyers message -->
    <ion-card class="no-inactive-buyers-card" *ngIf="!inactiveBuyers || inactiveBuyers.length === 0">
      <ion-card-content>
        <div class="no-inactive-buyers">
          <ion-icon name="checkmark-circle-outline" class="success-icon"></ion-icon>
          <h4>All Buyers Are Active</h4>
          <p>All buyers are purchasing according to their expected patterns</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>