/* Suppliers Page Styling */
.suppliers-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}

/* Collapsible Summary Section - Now at Top */
.summary-section {
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.summary-header:hover {
  background: #f8f9fa;
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.summary-content.expanded {
  max-height: 400px;
  padding: 16px;
}

/* Collapsible Search Section */
.search-section {
  margin: 0 16px 16px 16px;
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-header:hover {
  background: #f8f9fa;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.search-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.search-content.expanded {
  max-height: 200px;
  padding: 16px;
}

.custom-searchbar {
  --background: #f5f5f5;
  --border-radius: 12px;
  --box-shadow: none;
  --placeholder-color: #666;
  --color: #333;
}

/* Filter Button */
.filter-button {
  --border-radius: 8px;
  --border-color: var(--ion-color-step-200, #ddd);
  --color: var(--ion-color-medium, #666);
  --background: var(--card-background, white);
  height: 44px;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  transition: all 0.3s ease;
}

.filter-button:hover {
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
  --background: #f0f8ff;
}

/* No Data Item */
.no-data-item {
  --background: white;
  --padding-start: 16px;
  --padding-end: 16px;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Suppliers List */
.suppliers-list {
  padding: 8px 16px;
  margin-bottom: 80px; /* Space for floating menu */
}

/* Supplier Item */
.supplier-item {
  --background: var(--ion-item-background);
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  margin: 0 12px 8px 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
}

.supplier-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Supplier Icon */
.supplier-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.business-icon {
  font-size: 28px;
  color: white;
}

/* Supplier Details */
.supplier-details {
  flex: 1;
  margin: 0;
}

.supplier-name {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.supplier-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.location-info,
.contact-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
}

.meta-icon {
  font-size: 14px;
  color: #999;
}

.location-text,
.contact-text {
  font-weight: 500;
}

.supplier-details-row {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.supplier-detail {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

/* Balance Details */
.balance-details {
  margin-bottom: 8px;
}

.balance-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.balance-label {
  color: #666;
  font-weight: 500;
}

.balance-value {
  font-weight: 700;
  font-size: 15px;
}

.balance-positive {
  color: #4caf50;
}

.balance-zero {
  color: #666;
}

.balance-negative {
  color: #f44336;
}

/* Status Row */
.status-row {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.status-badge {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Supplier Actions */
.supplier-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  min-width: 60px;
}

.action-button {
  --border-radius: 8px;
  --padding-start: 8px;
  --padding-end: 8px;
  width: 40px;
  height: 40px;
}

.edit-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.delete-button {
  --color: #f44336;
  --background: #ffebee;
}

.status-toggle {
  margin-top: 8px;
  --handle-width: 20px;
  --handle-height: 20px;
  --track-width: 40px;
  --track-height: 24px;
}

/* Summary Section */
.summary-section {
  padding: 16px;
  // margin-bottom: 80px; /* Space for floating menu */
}

.summary-card {
  --background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 0;
}

.summary-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-icon {
  font-size: 20px;
  color: #ff9800;
}

/* Section Title */
.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.total {
  color: #6f42c1;
}

.summary-icon.active {
  color: #28a745;
}

.summary-icon.outstanding {
  color: #dc3545;
}

.summary-icon.average {
  color: #007bff;
}

.total-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.active-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.outstanding-card {
  background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
}

.average-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

/* Modal Styling */
.supplier-modal {
  --background: var(--ion-card-background);
  --border-radius: 16px;
  --width: 95%;
  --max-width: 600px;
  --height: 90%;
  --max-height: 800px;
}

.modal-content {
  --background: #f8f9fa;
}

.form-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.form-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 16px;
}

.form-item ion-label {
  font-weight: 500;
  color: #333;
}

.form-item ion-input {
  --background: #f5f5f5;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  margin-top: 8px;
}

.form-actions {
  padding: 16px 0;
}

.submit-button {
  --border-radius: 12px;
  height: 48px;
  font-weight: 600;
  text-transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-section {
    margin: 0 12px 12px 12px;
  }

  .search-header {
    padding: 12px;
  }

  .search-content.expanded {
    padding: 12px;
  }

  .suppliers-list {
    padding: 8px 12px;
  }

  .no-data-item {
    margin: 12px;
  }

  .supplier-item {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 10px;
    --padding-bottom: 10px;
    --min-height: 100px;
  }

  .supplier-icon {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .business-icon {
    font-size: 24px;
  }

  .supplier-name {
    font-size: 16px;
  }

  .supplier-meta {
    font-size: 13px;
    gap: 12px;
  }

  .supplier-details-row {
    font-size: 11px;
    gap: 8px;
  }

  .balance-row {
    font-size: 13px;
  }

  .supplier-actions {
    gap: 6px;
    min-width: 50px;
  }

  .action-button {
    width: 36px;
    height: 36px;
  }

  .summary-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }

  .supplier-modal {
    --width: 95%;
    --max-width: none;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 14px;
  }
}

/* Tablet and Desktop Responsive Design */
@media (min-width: 768px) {
  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }
}

@media (min-width: 1024px) {
  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }
}

/* Brand Management Modal Styling */
.brand-modal {
  --width: 90%;
  --max-width: 600px;
  --height: 100vh;
  --max-height: 80%;
}

.brand-modal-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

.brand-section {
  .section-header {
    margin-bottom: 24px;
    text-align: center;

    h3 {
      color: var(--ion-color-secondary);
      font-weight: 600;
      margin-bottom: 8px;
    }

    p {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }

  .brands-list {
    .brand-item {
      --border-color: #e0e0e0;
      margin: 8px 0;
      border-radius: 8px;
      transition: all 0.3s ease;

      &.selected {
        --background: #f3e5f5;
        --border-color: var(--ion-color-secondary);
        border: 2px solid var(--border-color);
      }

      &:hover {
        --background: #f5f5f5;
      }

      ion-label h3 {
        font-weight: 500;
        color: #333;
      }

      ion-checkbox {
        --color-checked: var(--ion-color-secondary);
      }
    }
  }

  .no-brands {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    .no-brands-icon {
      font-size: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 16px 0 8px 0;
      color: #999;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .selected-summary {
    margin-top: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;

    h4 {
      color: var(--ion-color-secondary);
      margin: 0 0 12px 0;
      font-weight: 600;
    }

    .selected-brands {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      ion-chip {
        --background: var(--ion-color-secondary);
        --color: white;
        font-size: 12px;
      }
    }
  }
}

.brand-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;

  ion-button {
    flex: 1;
    height: 44px;
    font-weight: 600;

    &.cancel-button {
      --border-color: var(--ion-color-secondary);
      --color: var(--ion-color-secondary);
    }

    &.save-button {
      --background: var(--ion-color-secondary);
      --color: white;
    }
  }
}

/* Action Button Styling for Brands */
.supplier-actions {
  .brands-button {
    --color: var(--ion-color-secondary);
    margin-right: 4px;
  }
}

.supplier-form {
  --background: var(--ion-card-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-active {
  color: var(--ion-color-success);
}

.status-inactive {
  color: var(--ion-color-danger);
}