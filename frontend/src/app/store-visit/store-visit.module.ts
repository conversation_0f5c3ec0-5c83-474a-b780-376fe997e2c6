import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';


import { SharedModule } from '../shared/modules/shared/shared.module';
import { StoreVisitPage } from './store-visit.page';
import { StoreVisitPageRoutingModule } from './store-visit-routing.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    StoreVisitPageRoutingModule,
    SharedModule
  ],
  declarations: [StoreVisitPage]
})
export class StoreVisitPageModule {} 