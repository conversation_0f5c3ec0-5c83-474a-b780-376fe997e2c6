// Store Visit Page Styles
.store-visit-content {
  --background: var(--ion-color-light);
}

// Loading Container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}

// Route Selection Screen
.route-selection {
  padding: 16px;
}

.selection-header {
  text-align: center;
  margin-bottom: 24px;
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

.weekday-selection {
  margin-bottom: 24px;
  
  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  ion-segment {
    --background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .weekday-button {
    --color: var(--ion-color-medium);
    --color-checked: var(--ion-color-primary);
    --indicator-color: var(--ion-color-primary);
    
    ion-icon {
      font-size: 16px;
    }
    
    ion-label {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.routes-section {
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
}

.routes-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.route-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  ion-card-content {
    padding: 16px;
  }
}

.route-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.route-icon {
  font-size: 24px;
  color: var(--ion-color-primary);
  flex-shrink: 0;
}

.route-info {
  flex: 1;
  
  h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: var(--ion-color-medium);
  }
}

.arrow-icon {
  font-size: 20px;
  color: var(--ion-color-medium);
  flex-shrink: 0;
}

.no-routes {
  text-align: center;
  padding: 48px 16px;
  
  .no-routes-icon {
    font-size: 64px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

// Store List Screen
.store-list {
  padding: 16px;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  .header-info {
    flex: 1;
    
    h2 {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--ion-color-dark);
    }
    
    p {
      margin: 0;
      color: var(--ion-color-medium);
      font-size: 14px;
    }
  }
}

.progress-section {
  margin-bottom: 16px;
  
  .progress-bar {
    width: 100%;
    height: 8px;
    background: var(--ion-color-light-shade);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--ion-color-success), var(--ion-color-success-shade));
      border-radius: 4px;
      transition: width 0.3s ease;
    }
  }
  
  .progress-text {
    font-size: 12px;
    color: var(--ion-color-medium);
    font-weight: 500;
  }
}

// Summary Section
.summary-section {
  margin-bottom: 16px;
  
  .summary-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
    
    .summary-icon {
      font-size: 18px;
      color: var(--ion-color-primary);
    }
  }
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.summary-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &.total {
    border-left: 4px solid var(--ion-color-primary);
  }
  
  &.pending {
    border-left: 4px solid var(--ion-color-warning);
  }
  
  &.visited {
    border-left: 4px solid var(--ion-color-tertiary);
  }
  
  &.completed {
    border-left: 4px solid var(--ion-color-success);
  }
  
  .summary-content {
    flex: 1;
    
    .summary-number {
      font-size: 20px;
      font-weight: 700;
      color: var(--ion-color-dark);
      line-height: 1;
      margin-bottom: 2px;
    }
    
    .summary-label {
      font-size: 11px;
      color: var(--ion-color-medium);
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  
  .summary-card-icon {
    font-size: 20px;
    color: var(--ion-color-medium);
    flex-shrink: 0;
  }
}

.photo-status-section {
  .photo-status-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--ion-color-dark);
    
    .photo-status-icon {
      font-size: 16px;
      color: var(--ion-color-primary);
    }
  }
}

.photo-status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.photo-status-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border-radius: 6px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &.with-photos {
    border-left: 3px solid var(--ion-color-success);
  }
  
  &.without-photos {
    border-left: 3px solid var(--ion-color-warning);
  }
  
  .photo-status-content {
    flex: 1;
    
    .photo-status-number {
      font-size: 16px;
      font-weight: 700;
      color: var(--ion-color-dark);
      line-height: 1;
      margin-bottom: 2px;
    }
    
    .photo-status-label {
      font-size: 10px;
      color: var(--ion-color-medium);
      font-weight: 500;
    }
  }
  
  .photo-status-card-icon {
    font-size: 16px;
    color: var(--ion-color-medium);
    flex-shrink: 0;
  }
}

.no-photos-alert {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-top: 8px;
  
  .alert-icon {
    font-size: 20px;
    color: var(--ion-color-warning);
    flex-shrink: 0;
    margin-top: 2px;
  }
  
  .alert-content {
    flex: 1;
    
    h5 {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-dark);
    }
    
    p {
      margin: 0;
      font-size: 12px;
      color: var(--ion-color-medium);
      line-height: 1.4;
    }
  }
}

.search-filter-section {
  margin-bottom: 16px;
  
  ion-searchbar {
    margin-bottom: 12px;
    --background: white;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  ion-segment {
    --background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.stores-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.store-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &.completed {
    background: #f0f9f0;
    border-left: 4px solid var(--ion-color-success);
  }
  
  &.visited {
    background: #fff8e1;
    border-left: 4px solid var(--ion-color-warning);
  }
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  ion-card-content {
    padding: 16px;
  }
}

.store-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.store-info {
  flex: 1;
  min-width: 0;
}

.store-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  
  .store-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 8px;
  }
  
  .status-chips {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    flex-shrink: 0;
  }
}

.store-details {
  margin-bottom: 8px;
  
  p {
    margin: 0 0 4px 0;
    font-size: 12px;
    color: var(--ion-color-medium);
    display: flex;
    align-items: center;
    gap: 4px;
    
    ion-icon {
      font-size: 12px;
    }
  }
}

.store-photos {
  ion-chip {
    font-size: 10px;
  }
}

.store-actions {
  flex-shrink: 0;
}

.no-stores {
  text-align: center;
  padding: 48px 16px;
  
  .no-stores-icon {
    font-size: 64px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

// Store Details Screen
.store-details {
  padding: 16px;
}

.details-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  
  .header-info {
    flex: 1;
    
    h2 {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--ion-color-dark);
    }
    
    p {
      margin: 0;
      color: var(--ion-color-medium);
      font-size: 14px;
    }
  }
}

.store-info-card {
  margin: 0 0 16px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  ion-card-content {
    padding: 16px;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  
  ion-icon {
    font-size: 20px;
    color: var(--ion-color-primary);
    flex-shrink: 0;
  }
  
  .info-content {
    flex: 1;
    
    h4 {
      margin: 0 0 2px 0;
      font-size: 12px;
      font-weight: 600;
      color: var(--ion-color-medium);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: var(--ion-color-dark);
    }
  }
}

.photo-capture-section {
  margin-bottom: 16px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
}

.photo-preview {
  position: relative;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .preview-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
  
  ion-button {
    position: absolute;
    top: 8px;
    right: 8px;
    --background: rgba(0, 0, 0, 0.5);
    --color: white;
  }
}

.photo-actions {
  .upload-section {
    .photo-notes {
      margin-bottom: 12px;
      --background: white;
      --border-radius: 8px;
      --padding-start: 12px;
      --padding-end: 12px;
      --padding-top: 12px;
      --padding-bottom: 12px;
    }
    
    .upload-button {
      --border-radius: 8px;
    }
  }
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  ion-button {
    --border-radius: 8px;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .photo-status-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .store-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    
    .store-name {
      margin-right: 0;
    }
  }
  
  .store-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .store-actions {
    width: 100%;
    
    ion-button {
      width: 100%;
    }
  }
}

// Segment Button Customization
ion-segment-button {
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-primary);
  --indicator-color: var(--ion-color-primary);
  
  ion-label {
    font-size: 12px;
    font-weight: 500;
  }
}

// Card Customization
ion-card {
  --background: white;
  margin: 0;
}

// Button Customization
ion-button {
  --border-radius: 8px;
  font-weight: 500;
}

// Chip Customization
ion-chip {
  font-weight: 500;
  
  ion-icon {
    font-size: 12px;
  }
  
  ion-label {
    font-size: 10px;
  }
} 