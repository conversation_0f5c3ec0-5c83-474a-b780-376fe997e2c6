<app-header [title]="'Store Visits'" [showBackButton]="true"></app-header>

<ion-content class="store-visit-content">
  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading store visit data...</p>
  </div>

  <!-- Route Selection Screen -->
  <div class="route-selection" *ngIf="!loading && showRouteSelection">
    <div class="selection-header">
      <h2>Select Route for Store Visits</h2>
      <p>Choose the weekday and route to manage store visits</p>
    </div>

    <!-- Weekday Selection -->
    <div class="weekday-selection">
      <h3>Select Day</h3>
      <ion-segment [(ngModel)]="selectedWeekday" (ionChange)="onWeekdayChange()" scrollable="true">
        <ion-segment-button 
          *ngFor="let day of weekdays" 
          [value]="day.value"
          class="weekday-button">
          <ion-icon [name]="day.icon"></ion-icon>
          <ion-label>{{day.label}}</ion-label>
        </ion-segment-button>
      </ion-segment>
    </div>

    <!-- Available Routes -->
    <div class="routes-section" *ngIf="availableRoutes.length > 0">
      <h3>Available Routes</h3>
      <div class="routes-grid">
        <ion-card 
          *ngFor="let route of availableRoutes; trackBy: trackByRouteId"
          class="route-card"
          (click)="selectRoute(route)">
          <ion-card-content>
            <div class="route-content">
              <ion-icon name="map-outline" class="route-icon"></ion-icon>
              <div class="route-info">
                <h4>{{route.route_name}}</h4>
                <p>Route ID: {{route.route}}</p>
                <ion-chip color="primary" size="small">
                  <ion-label>{{selectedWeekday | titlecase}}</ion-label>
                </ion-chip>
              </div>
              <ion-icon name="chevron-forward" class="arrow-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
    </div>

    <!-- No Routes Available -->
    <div class="no-routes" *ngIf="availableRoutes.length === 0">
      <ion-icon name="map-outline" class="no-routes-icon"></ion-icon>
      <h3>No Routes Available</h3>
      <p>No routes are scheduled for {{selectedWeekday | titlecase}}</p>
    </div>
  </div>

  <!-- Store List Screen -->
  <div class="store-list" *ngIf="!loading && showStoreList && !showStoreDetails">
    <div class="list-header">
      <div class="header-info">
        <h2>Store Visits</h2>
        <p>{{filteredStores.length}} stores • {{getCompletionPercentage()}}% complete</p>
      </div>
      <ion-button fill="clear" (click)="backToRouteSelection()">
        <ion-icon name="arrow-back" slot="icon-only"></ion-icon>
      </ion-button>
    </div>

    <!-- Progress Bar -->
    <div class="progress-section">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="getCompletionPercentage()"></div>
      </div>
      <span class="progress-text">{{getCompletionPercentage()}}% Complete</span>
    </div>

    <!-- Store Visit Summary -->
    <div class="summary-section" *ngIf="summaryStats.totalStores > 0">
      <h3 class="summary-title">
        <ion-icon name="analytics-outline" class="summary-icon"></ion-icon>
        Store Visit Summary
      </h3>
      
      <div class="summary-grid">
        <div class="summary-card total">
          <div class="summary-content">
            <div class="summary-number">{{summaryStats.totalStores}}</div>
            <div class="summary-label">Total Stores</div>
          </div>
          <ion-icon name="storefront-outline" class="summary-card-icon"></ion-icon>
        </div>

        <div class="summary-card pending">
          <div class="summary-content">
            <div class="summary-number">{{summaryStats.storesPending}}</div>
            <div class="summary-label">Pending</div>
          </div>
          <ion-icon name="time-outline" class="summary-card-icon"></ion-icon>
        </div>

        <div class="summary-card visited">
          <div class="summary-content">
            <div class="summary-number">{{summaryStats.storesVisited}}</div>
            <div class="summary-label">Visited</div>
          </div>
          <ion-icon name="checkmark-outline" class="summary-card-icon"></ion-icon>
        </div>

        <div class="summary-card completed">
          <div class="summary-content">
            <div class="summary-number">{{summaryStats.storesCompleted}}</div>
            <div class="summary-label">Completed</div>
          </div>
          <ion-icon name="checkmark-circle-outline" class="summary-card-icon"></ion-icon>
        </div>
      </div>

      <!-- Photo Recording Status -->
      <div class="photo-status-section">
        <h4 class="photo-status-title">
          <ion-icon name="camera-outline" class="photo-status-icon"></ion-icon>
          Photo Recording Status
        </h4>
        
        <div class="photo-status-grid">
          <div class="photo-status-card with-photos">
            <div class="photo-status-content">
              <div class="photo-status-number">{{summaryStats.storesWithPhotos}}</div>
              <div class="photo-status-label">With Photos Today</div>
            </div>
            <ion-icon name="camera" class="photo-status-card-icon"></ion-icon>
          </div>

          <div class="photo-status-card without-photos">
            <div class="photo-status-content">
              <div class="photo-status-number">{{summaryStats.storesWithoutPhotos}}</div>
              <div class="photo-status-label">No Photos Yet</div>
            </div>
            <ion-icon name="camera-outline" class="photo-status-card-icon"></ion-icon>
          </div>
        </div>

        <!-- Alert for stores without photos -->
        <div class="no-photos-alert" *ngIf="summaryStats.storesWithoutPhotos > 0">
          <ion-icon name="alert-circle-outline" class="alert-icon"></ion-icon>
          <div class="alert-content">
            <h5>Photo Recording Required</h5>
            <p>{{summaryStats.storesWithoutPhotos}} store{{summaryStats.storesWithoutPhotos > 1 ? 's' : ''}} still need{{summaryStats.storesWithoutPhotos > 1 ? '' : 's'}} photo{{summaryStats.storesWithoutPhotos > 1 ? 's' : ''}} recorded today</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="search-filter-section">
      <ion-searchbar 
        [(ngModel)]="searchTerm" 
        (ionInput)="onSearchChange()"
        placeholder="Search stores..."
        show-clear-button="focus">
      </ion-searchbar>
      
      <ion-segment [(ngModel)]="filterStatus" (ionChange)="onFilterChange()" scrollable="true">
        <ion-segment-button value="all">
          <ion-label>All</ion-label>
        </ion-segment-button>
        <ion-segment-button value="pending">
          <ion-label>Pending</ion-label>
        </ion-segment-button>
        <ion-segment-button value="visited">
          <ion-label>Visited</ion-label>
        </ion-segment-button>
        <ion-segment-button value="completed">
          <ion-label>Completed</ion-label>
        </ion-segment-button>
      </ion-segment>
    </div>

    <!-- Stores List -->
    <div class="stores-list">
      <ion-card 
        *ngFor="let store of filteredStores; trackBy: trackByStoreId"
        class="store-card"
        [class.completed]="store.visit_status === 'completed'"
        [class.visited]="store.visit_status === 'visited'"
        (click)="selectStore(store)">
        <ion-card-content>
          <div class="store-content">
            <div class="store-info">
              <div class="store-header">
                <h4 class="store-name">{{store.name}}</h4>
                <div class="status-chips">
                  <ion-chip 
                    [color]="getVisitStatusColor(store.visit_status)" 
                    size="small">
                    <ion-icon [name]="getVisitStatusIcon(store.visit_status)"></ion-icon>
                    <ion-label>{{store.visit_status | titlecase}}</ion-label>
                  </ion-chip>
                  <ion-chip 
                    [color]="store.active ? 'success' : 'medium'" 
                    size="small">
                    <ion-icon [name]="store.active ? 'checkmark-circle' : 'close-circle'"></ion-icon>
                    <ion-label>{{store.active ? 'Active' : 'Inactive'}}</ion-label>
                  </ion-chip>
                </div>
              </div>
              
              <div class="store-details">
                <p class="store-place" *ngIf="store.place">
                  <ion-icon name="location-outline"></ion-icon>
                  {{store.place}}
                </p>
                <p class="store-phone" *ngIf="store.phone_no">
                  <ion-icon name="call-outline"></ion-icon>
                  {{store.phone_no}}
                </p>
              </div>

              <div class="store-photos" *ngIf="store.has_today_photos">
                <ion-chip color="success" size="small">
                  <ion-icon name="camera"></ion-icon>
                  <ion-label>{{store.today_photo_count}} photo{{store.today_photo_count > 1 ? 's' : ''}} today</ion-label>
                </ion-chip>
              </div>
            </div>

            <div class="store-actions">
              <ion-button 
                fill="clear" 
                size="small"
                (click)="openShopImageModal(store); $event.stopPropagation()">
                <ion-icon name="camera-outline" slot="icon-only"></ion-icon>
              </ion-button>
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- No Stores Found -->
    <div class="no-stores" *ngIf="filteredStores.length === 0">
      <ion-icon name="storefront-outline" class="no-stores-icon"></ion-icon>
      <h3>No Stores Found</h3>
      <p>No stores match your current search or filter criteria</p>
    </div>
  </div>

  <!-- Store Details Screen -->
  <div class="store-details" *ngIf="!loading && showStoreDetails && selectedStore">
    <div class="details-header">
      <ion-button fill="clear" (click)="backToStoreList()">
        <ion-icon name="arrow-back" slot="icon-only"></ion-icon>
      </ion-button>
      <div class="header-info">
        <h2>{{selectedStore.name}}</h2>
        <p>{{selectedStore.place}}</p>
      </div>
    </div>

    <!-- Store Information -->
    <ion-card class="store-info-card">
      <ion-card-content>
        <div class="info-grid">
          <div class="info-item">
            <ion-icon name="map-outline"></ion-icon>
            <div class="info-content">
              <h4>Route</h4>
              <p>{{selectedStore.route_name}}</p>
            </div>
          </div>
          
          <div class="info-item" *ngIf="selectedStore.phone_no">
            <ion-icon name="call-outline"></ion-icon>
            <div class="info-content">
              <h4>Phone</h4>
              <p>{{selectedStore.phone_no}}</p>
            </div>
          </div>

          <div class="info-item">
            <ion-icon name="camera-outline"></ion-icon>
            <div class="info-content">
              <h4>Photos Today</h4>
              <p>{{selectedStore.today_photo_count}} photo{{selectedStore.today_photo_count > 1 ? 's' : ''}}</p>
            </div>
          </div>

          <div class="info-item">
            <ion-icon [name]="getVisitStatusIcon(selectedStore.visit_status)"></ion-icon>
            <div class="info-content">
              <h4>Status</h4>
              <p>{{selectedStore.visit_status | titlecase}}</p>
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Photo Capture Section -->
    <div class="photo-capture-section">
      <h3>Capture Store Photos</h3>
      
      <!-- Photo Preview -->
      <div class="photo-preview" *ngIf="previewUrl">
        <img [src]="previewUrl" alt="Photo preview" class="preview-image">
        <ion-button fill="clear" size="small" (click)="clearPhotoSelection()">
          <ion-icon name="close" slot="icon-only"></ion-icon>
        </ion-button>
      </div>

      <!-- Photo Actions -->
      <div class="photo-actions">
        <ion-button 
          expand="block" 
          fill="outline"
          (click)="presentImageOptions()"
          *ngIf="!selectedFile">
          <ion-icon name="camera-outline" slot="start"></ion-icon>
          Add Photo
        </ion-button>

        <div class="upload-section" *ngIf="selectedFile">
          <ion-textarea
            [(ngModel)]="photoNotes"
            placeholder="Add notes about this photo (optional)"
            rows="3"
            class="photo-notes">
          </ion-textarea>
          
          <ion-button 
            expand="block" 
            (click)="uploadImage()"
            class="upload-button">
            <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
            Upload Photo
          </ion-button>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <ion-button 
        expand="block" 
        fill="outline"
        color="warning"
        (click)="markStoreAsVisited(selectedStore)"
        *ngIf="selectedStore.visit_status === 'pending'">
        <ion-icon name="checkmark-outline" slot="start"></ion-icon>
        Mark as Visited
      </ion-button>

      <ion-button 
        expand="block" 
        fill="outline"
        (click)="openShopImageModal(selectedStore)">
        <ion-icon name="images-outline" slot="start"></ion-icon>
        View All Photos
      </ion-button>
    </div>
  </div>

  <!-- Hidden File Inputs -->
  <input 
    #fileInput 
    type="file" 
    accept="image/*" 
    (change)="onFileSelected($event)" 
    style="display: none;">
  
  <input 
    #cameraInput 
    type="file" 
    accept="image/*" 
    capture="environment" 
    (change)="onFileSelected($event)" 
    style="display: none;">
</ion-content> 