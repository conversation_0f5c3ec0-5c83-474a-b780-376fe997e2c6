import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './auth/auth.guard';
import { SelectivePreloadService } from './shared/services/selective-preload.service';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'tabs',
    pathMatch: 'full'
  },
  {
    path: 'tabs',
    loadChildren: () => import('./tabs/tabs.module').then(m => m.TabsPageModule),
    canActivate: [AuthGuard],
    data: { permission: 'view_reports' }
  },
  {
    path: 'login',
    loadChildren: () => import('./login/login.module').then( m => m.LoginPageModule)
  },
  {
    path: 'buyers',
    loadChildren: () => import('./buyers/buyers.module').then( m => m.BuyersPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'suppliers',
    loadChildren: () => import('./suppliers/suppliers.module').then( m => m.SuppliersPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'purchase-bill',
    loadChildren: () => import('./purchase-bill/purchase-bill.module').then( m => m.PurchaseBillPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'line-accounts',
    loadChildren: () => import('./line-accounts/line-accounts.module').then( m => m.LineAccountsPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'tally',
    loadChildren: () => import('./tally/tally.module').then( m => m.TallyPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'purchase-payment',
    loadChildren: () => import('./purchase-payment/purchase-payment.module').then( m => m.PurchasePaymentPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'sales-payment',
    loadChildren: () => import('./sales-payment/sales-payment.module').then( m => m.SalesPaymentPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'create-invoice',
    loadChildren: () => import('./create-invoice/create-invoice.module').then( m => m.CreateInvoicePageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'edit-invoice',
    loadChildren: () => import('./edit-invoice/edit-invoice.module').then( m => m.EditInvoicePageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'sales-order',
    loadChildren: () => import('./sales-order/sales-order.module').then( m => m.SalesOrderPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'ledger',
    loadChildren: () => import('./ledger/ledger.module').then( m => m.LedgerPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'expense',
    loadChildren: () => import('./expense/expense.module').then( m => m.ExpensePageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' }
  },
  {
    path: 'retailer-class',
    loadChildren: () => import('./retailer-class/retailer-class.module').then( m => m.RetailerClassPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' } 
  },
  {
    path: 'retailer-class-mgmt',
    loadChildren: () => import('./retailer-class-mgmt/retailer-class-mgmt.module').then( m => m.RetailerClassMgmtPageModule), canActivate: [AuthGuard],data: { permission: 'view_reports' } 
  },
  {
    path: 'closing-stocks',
    loadChildren: () => import('./closing-stocks/closing-stocks.module').then( m => m.ClosingStocksPageModule)
  },
  {
    path: 'analytics',
    loadChildren: () => import('./analytics/analytics.module').then( m => m.AnalyticsPageModule)
  },
  {
    path: 'report',
    loadChildren: () => import('./report/report.module').then( m => m.ReportPageModule),
    canActivate: [AuthGuard],
    data: { permission: 'view_reports' }
  },
  {
    path: 'purchase-order',
    loadChildren: () => import('./purchase-order/purchase-order.module').then( m => m.PurchaseOrderPageModule),
    canActivate: [AuthGuard],
    data: { permission: 'view_reports' }
  },
  {
    path: 'create-purchase-order',
    loadChildren: () => import('./create-purchase-order/create-purchase-order.module').then( m => m.CreatePurchaseOrderPageModule)
  },
  {
    path: 'checklist',
    loadChildren: () => import('./checklist/checklist.module').then( m => m.ChecklistPageModule)
  },
  {
    path: 'route-management',
    loadChildren: () => import('./route-management/route-management.module').then( m => m.RouteManagementPageModule)
  },
  {
    path: 'store-visit',
    loadChildren: () => import('./store-visit/store-visit.module').then( m => m.StoreVisitPageModule),
    canActivate: [AuthGuard],
    data: { permission: 'view_reports' }
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { 
      preloadingStrategy: SelectivePreloadService,
      // Additional performance optimizations
      enableTracing: false, // Only enable for debugging
      onSameUrlNavigation: 'ignore'
    })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {}
