/* Expense Page Styling */
.expense-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

/* Section Styling */
.summary-section,
.filter-section,
.expense-list-section,
.no-data-section {
  margin-bottom: 24px;
}

/* Collapsible Summary Section */
.summary-section {
  background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.summary-header:hover {
  background: #f8f9fa;
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.summary-content.expanded {
  max-height: 400px;
  padding: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.total {
  color: #6f42c1;
}

.summary-icon.amount {
  color: #dc3545;
}

.total-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
}

.amount-card {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

/* Filter Card */
.filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
}

.date-item {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 8px;
}

.date-input {
  --background: #f8f9fa;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
}

.filter-button {
  --border-radius: 12px;
  height: 44px;
  margin-top: 20px;
}

.add-button {
  --border-radius: 12px;
  height: 44px;
  margin-top: 16px;
  --background: var(--ion-color-success);
}

.print-button {
  --border-radius: 12px;
  height: 44px;
  margin-top: 16px;
  --color: var(--ion-color-primary);
  --border-color: var(--ion-color-primary);
}

/* Expense Cards */
.expense-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.expense-card {
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --background: var(--item-background);
  transition: all 0.3s ease;
  cursor: pointer;
}

.expense-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.expense-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.expense-info {
  flex: 1;
}

.category-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #e3f2fd;
  padding: 6px 12px;
  border-radius: 20px;
  margin-bottom: 8px;
  width: fit-content;
}

.category-icon {
  font-size: 14px;
  color: var(--ion-color-primary);
}

.category-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--ion-color-primary);
  text-transform: uppercase;
}

.subcategory {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.expense-amount {
  text-align: right;
}

.amount {
  font-size: 20px;
  font-weight: 700;
  color: #dc3545;
}

.expense-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.expense-notes,
.expense-date {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notes-icon,
.date-icon {
  font-size: 14px;
  color: #6c757d;
}

.notes-text,
.date-text {
  font-size: 13px;
  color: #6c757d;
}

.notes-text {
  font-style: italic;
}

.expense-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.action-button {
  --border-radius: 8px;
  width: 36px;
  height: 36px;
}

.view-button {
  --color: var(--ion-color-primary);
  --background: #e3f2fd;
}

.edit-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.delete-button {
  --color: #f44336;
  --background: #ffebee;
}

/* No Data Section */
.no-data-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-data-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-data-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #6c757d;
}

.no-data-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #adb5bd;
}

.add-first-button {
  --border-radius: 12px;
  --background: var(--ion-color-success);
}

/* Responsive Design */
@media (max-width: 576px) {
  .expense-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .summary-card-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }

  .expense-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .expense-amount {
    text-align: left;
  }

  .amount {
    font-size: 18px;
  }

  .category-badge {
    margin-bottom: 4px;
  }

  .subcategory {
    font-size: 14px;
  }

  .expense-actions {
    justify-content: center;
  }

  .filter-button,
  .add-button,
  .print-button {
    height: 40px;
    font-size: 12px;
  }

  .no-data-icon {
    font-size: 48px;
  }

  .no-data-content h3 {
    font-size: 16px;
  }

  .no-data-content p {
    font-size: 12px;
  }
}

@media (min-width: 768px) {
  .expense-content {
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }

  .expense-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .amount {
    font-size: 22px;
  }

  .subcategory {
    font-size: 18px;
  }
}

@media (min-width: 1024px) {
  .expense-content {
    --padding-start: 32px;
    --padding-end: 32px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }

  .expense-cards {
    grid-template-columns: repeat(3, 1fr);
  }

  .amount {
    font-size: 24px;
  }

  .subcategory {
    font-size: 20px;
  }
}

/* Expense Modal Styling */
.expense-modal {
  --width: 90%;
  --max-width: 600px;
  --height: 100vh;
  --max-height: 90%;
  --border-radius: 16px;
}

.modal-content {
  --background: #f8f9fa;
}

.form-section {
  background: var(--form-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.section-icon {
  font-size: 18px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.form-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 16px;
}

.form-item ion-label {
  font-weight: 500;
  color: #333;
}

.form-item ion-input,
.form-item ion-textarea {
  --background: #f5f5f5;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  margin-top: 8px;
}

/* Image Upload Styling */
.image-preview {
  position: relative;
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  display: block;
}

.remove-preview-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  --background: rgba(255, 255, 255, 0.9);
  --color: #dc3545;
  --border-radius: 50%;
  width: 32px;
  height: 32px;
  margin: 0;
}

.upload-controls {
  margin-top: 12px;
}

.select-image-btn {
  --border-radius: 8px;
  height: 44px;
  --background: #f5f5f5;
  --color: var(--ion-color-primary);
  --border-color: var(--ion-color-primary);
}

.select-image-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.file-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f5f5f5;
  margin-top: 8px;
}

.form-actions {
  margin-top: 24px;
  padding: 0 8px;
}

.submit-button {
  --border-radius: 12px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
}

/* Responsive Design for Expense Modal */
@media (max-width: 768px) {
  .expense-modal {
    --width: 95%;
    --max-width: none;
    --height: 100vh;
    --max-height: 95%;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 14px;
  }

  .section-icon {
    font-size: 16px;
  }

  .form-item {
    margin-bottom: 12px;
  }

  .submit-button {
    --padding-top: 14px;
    --padding-bottom: 14px;
    font-size: 14px;
  }
}

.expense-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.status-pending {
  color: var(--ion-color-warning);
}

.status-inactive {
  color: var(--ion-color-danger);
}