import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ExpenseService } from '../shared/services/expense.service';
import { IonicSelectableComponent } from 'ionic-selectable';
import * as moment from 'moment';
import { Platform, ActionSheetController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { RouteService } from '../shared/services/route.service';
import { ToastService } from '../shared/services/toast.service';
import { environment } from 'src/environments/environment';
import { PrintServiceService } from '../shared/services/print-service.service';
import { UtilService } from '../shared/services/util.service';

@Component({
  selector: 'app-expense',
  templateUrl: './expense.page.html',
  styleUrls: ['./expense.page.scss'],
})
export class ExpensePage implements OnInit {
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('cameraInput', { static: false }) cameraInput!: ElementRef<HTMLInputElement>;

  categories = [];
  subcategories = [];
  expenseForm: FormGroup;

  isAddModalOpen = false;
  data: any;
  expense_amount_total: any;
  from_date: any = moment().subtract(30, 'days').format("YYYY-MM-DD");
  to_date: any = moment().format("YYYY-MM-DD");
  isEditMode: boolean;
  isEditModalOpen: boolean;
  expenseData: any;
  isViewModalOpen: boolean;
  viewData: any;
  apiUrlClean: any = environment.apiUrlClean;
  showSummary: boolean = false;

  // Camera/File selection properties
  selectedFile: File | null = null;
  previewUrl: string | null = null;
  captureMode: 'camera' | 'gallery' = 'camera';

  modeOfPaymentConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a Mode of payment',
    customComparator: () => 0, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  modeOfPaymentSelectedOption : any;
  modeOfPaymentOptions :  any = JSON.parse(localStorage.getItem('metadata')).modeOfPayment;
  
  constructor(
    private fb: FormBuilder,
    private expenseService: ExpenseService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public routerService: RouteService,
    private printService: PrintServiceService,
    private util: UtilService,
    private actionSheetController: ActionSheetController,
  ) {
    this.expenseForm = this.fb.group({
      category: ['', Validators.required],
      subcategory: ['', Validators.required],
      amount: ['', [Validators.required, Validators.min(0)]],
      notes: [''],
      expense_file: [null]
    });
    this.expenseForm.get('subcategory')?.disable();
  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.getExpenseData();
  }
  onModeOfPaymentOptionChange(selected: any) {
    console.log('Selected Option:', selected);
    this.modeOfPaymentSelectedOption = selected.value;
  }
  async getExpenseData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.expenseService.getExpense().subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.data = res.data
          this.expense_amount_total = this.data.reduce((acc, ecc) => acc + ecc.amount, 0);
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      });
    });
  }

  async filterList() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.expenseService.getFilterExpense(this.from_date, this.to_date).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.data = res.data
          this.expense_amount_total = this.data.reduce((acc, ecc) => acc + ecc.amount, 0);
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  async getCategoryData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.expenseService.getCategories().subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.categories = res.data;
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      });
    });
  }

  async onSubmit() {
    if (this.expenseForm.valid) {
      await this.ionLoaderService.startLoader().then(async () => {
        const formData = new FormData();

        console.log(formData);
        if (this.isEditMode) {
          // Update existing expense
          for (const key in this.expenseForm.value) {
            if (key !== 'expense_file') {
              formData.append(key, this.expenseForm.value[key]);
            }
          }
          formData.append('id', this.expenseData.id);
          formData.append('category', this.expenseData?.category);
          formData.append('subcategory', this.expenseData?.subcategory);
          formData.append('mode_of_payment', this.modeOfPaymentSelectedOption?.slug);
          await this.expenseService.updateExpense(formData).subscribe(async (res: any) => {
            console.log('Expense added:', res);
            if (res.success) {
              this.toast.toastServices(res.message, 'success', 'top');
              this.expenseForm.reset();
              this.closeModal();
              this.getExpenseData();
            }
            else {
              this.toast.toastServices(res.message, 'danger', 'top')
            }
            this.ionLoaderService.dismissLoader();

          });

        } else {
          for (const key in this.expenseForm.value) {
            if (this.expenseForm.value[key]) {
              formData.append(key, this.expenseForm.value[key]);
            }
          }
          formData.append('category', this.expenseForm.get('category').value?.id);
          formData.append('subcategory', this.expenseForm.get('subcategory').value?.id);
          formData.append('mode_of_payment', this.modeOfPaymentSelectedOption?.slug);
          await this.expenseService.addExpense(formData).subscribe(async (res: any) => {
            console.log('Expense added:', res);
            if (res.success) {
              this.toast.toastServices(res.message, 'success', 'top');
              this.expenseForm.reset();
              this.closeModal();
              this.getExpenseData();
            }
            else {
              this.toast.toastServices(res.message, 'danger', 'top')
            }
            this.ionLoaderService.dismissLoader();

          });
        }
      });
    }

  }

  async delete(id) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.expenseService
        .deleteExpense(id)
        .subscribe(async (res: any) => {
          if (res.success) {
            this.toast.toastServices(res.message, 'success', 'top');
            this.getExpenseData();
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        });
    });

  }

  async addCategory(event: { component: IonicSelectableComponent }) {
    const name = event.component.searchText;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.expenseService.addCategory(name).subscribe(res => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.categories.push(res.data);
          event.component.items = [...this.categories];
          event.component.searchText = '';
          event.component.close();
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      });
    });
  }

  async addSubcategory(event: { component: IonicSelectableComponent }) {
    const name = event.component.searchText;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.expenseService.addSubCategory(this.expenseForm.get('category').value?.id, name).subscribe(res => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.subcategories.push(res.data);
          event.component.items = [...this.subcategories];
          event.component.searchText = '';
          event.component.close();
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  async onSelectCategory(event: {
    component: IonicSelectableComponent,
    item: any,
    isSelected: boolean
  }) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.expenseService.getSubCategories(event.item.id).subscribe(res => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.subcategories = res.data;
          this.expenseForm.get('subcategory')?.enable()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      });
    });

  }

  async presentImageOptions() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Add Receipt Image',
      cssClass: 'image-options-action-sheet',
      buttons: [
        {
          text: 'Take Photo',
          icon: 'camera-outline',
          handler: () => {
            this.openCamera();
          }
        },
        {
          text: 'Choose from Gallery',
          icon: 'images-outline',
          handler: () => {
            this.openGallery();
          }
        },
        {
          text: 'Cancel',
          icon: 'close-outline',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }

  openCamera() {
    this.captureMode = 'camera';
    if (this.cameraInput) {
      this.cameraInput.nativeElement.click();
    }
  }

  openGallery() {
    this.captureMode = 'gallery';
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  handleFileSelection(file: File) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      this.toast.toastServices('Please select a valid image file', 'danger', 'top');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      this.toast.toastServices('Image size should not exceed 10MB', 'danger', 'top');
      return;
    }

    this.selectedFile = file;
    this.expenseForm.patchValue({
      expense_file: file
    });
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      this.previewUrl = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    this.toast.toastServices('Image selected successfully', 'success', 'top');
  }

  clearImageSelection() {
    this.selectedFile = null;
    this.previewUrl = null;
    this.expenseForm.patchValue({
      expense_file: null
    });
    
    // Reset file inputs
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    if (this.cameraInput) {
      this.cameraInput.nativeElement.value = '';
    }
  }

  onFileChange(event: any) {
    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      this.expenseForm.patchValue({
        expense_file: file
      });
    }
  }
  getModeOfPayment(data){
    return this.modeOfPaymentOptions.find(item=>item.slug ==  data).name
  }
  openModal(isEditMode: boolean = false, expenseData: any = null) {
    this.getCategoryData();
    this.isEditMode = isEditMode;
    if (isEditMode && expenseData) {
      this.expenseData = expenseData;
      this.modeOfPaymentSelectedOption = this.modeOfPaymentOptions.find(item=>item.slug ==  expenseData.mode_of_payment)
      this.expenseForm.patchValue(expenseData);
      this.expenseForm.get('category')?.disable();
      this.expenseForm.get('subcategory')?.disable();
    } else {
      this.expenseForm.reset();
      this.expenseForm.get('category')?.enable();
      this.expenseForm.get('subcategory')?.enable();
    }
    this.isAddModalOpen = !isEditMode;
    this.isEditModalOpen = isEditMode;
  }
  closeModal() {
    this.isAddModalOpen = false;
    this.isEditModalOpen = false;
  }
  view(expense) {
    this.viewData = expense;
    this.setViewOpen(true);
  }
  setViewOpen(isOpen: boolean) {
    this.isViewModalOpen = isOpen;
  }
  openImg() {
    window.open(this.apiUrlClean + this.viewData.ledger_file)
  }
  toggleSummary() {
    this.showSummary = !this.showSummary;
  }

  printStatement(){
    const items = this.data.map((d, index) => {
      return `
        <tr>
          <td class="name-cell">${moment(d.created_at).format("DD/MM/YYYY")}</td>
          <td class="name-cell">${d.category_name}</td>
          <td class="name-cell">${d.subcategory_name}</td>
          <td class="amount-cell">₹${d.amount.toFixed(2)}</td>
          <td class="remarks-col">${d.notes || '-'}</td>
        </tr>
      `;
    }).join("");

    const totals = this.data.reduce((acc, transaction) => acc += transaction.amount, 0);
    const dateRange = `${this.from_date} to ${this.to_date}`;

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Category</th>
            <th>Sub Category</th>
            <th>Amount</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          ${items}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td></td>
            <td></td>
            <td><strong>₹${totals.toFixed(2)}</strong></td>
            <td></td>
          </tr>
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      'Expense Report',
      dateRange,
      tableContent,
      `Total Expenses: ₹${totals.toFixed(2)} | Records: ${this.data.length}`
    );

    this.printService.printEnhancedReport(htmlContent, `Expense Report - ${dateRange}`);
  }

}
