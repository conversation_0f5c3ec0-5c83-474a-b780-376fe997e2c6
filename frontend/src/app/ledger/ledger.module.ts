import { SharedModule } from './../shared/modules/shared/shared.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { LedgerPageRoutingModule } from './ledger-routing.module';

import { LedgerPage } from './ledger.page';
import { IonicSelectableModule } from 'ionic-selectable';
import { SelectDropDownModule } from 'ngx-select-dropdown';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    LedgerPageRoutingModule,
    SharedModule,
    ReactiveFormsModule,
    IonicSelectableModule,
    SelectDropDownModule
  ],
  declarations: [LedgerPage]
})
export class LedgerPageModule {}
