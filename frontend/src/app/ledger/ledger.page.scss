/* Ledger Page Styling */
.ledger-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

/* Image Upload Styling */
.image-preview {
  position: relative;
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  display: block;
}

.remove-preview-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  --background: rgba(255, 255, 255, 0.9);
  --color: #dc3545;
  --border-radius: 50%;
  width: 32px;
  height: 32px;
  margin: 0;
}

.upload-controls {
  margin-top: 12px;
}

.select-image-btn {
  --border-radius: 8px;
  height: 44px;
  --background: #f8f9fa;
  --color: var(--ion-color-primary);
  --border-color: var(--ion-color-primary);
}

/* Section Styling */
.summary-section,
.segment-section,
.search-section,
.entities-section,
.ledger-detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.outstanding {
  color: #dc3545;
}

.summary-icon.advance {
  color: #28a745;
}

.outstanding-card {
  background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
}

.advance-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

/* Segment Section */
.custom-segment {
  --background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.segment-button {
  --indicator-color: var(--ion-color-primary);
  --color: #6c757d;
  --color-checked: var(--ion-color-primary);
}

.segment-button ion-icon {
  margin-bottom: 4px;
}

/* Collapsible Search Section */
.search-section {
  margin-bottom: 12px;
  background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-header:hover {
  background: #f8f9fa;
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.search-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.search-content.expanded {
  max-height: 150px;
  padding: 16px;
}

.custom-searchbar {
  --background: #f5f5f5;
  --border-radius: 12px;
  --box-shadow: none;
  --placeholder-color: #666;
  --color: #333;
}

/* Entities Section */
.entities-card,
.no-data-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
}



.entity-item {
  --background: transparent;
  --border-radius: 12px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.entity-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.entity-avatar {
  margin-right: 12px;
}

.avatar-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.entity-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.entity-type {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.entity-balance {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.balance-amount {
  font-size: 14px;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.chevron-icon {
  font-size: 16px;
  color: #adb5bd;
}

/* No Data Section */
.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-data-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 12px;
}

.no-data-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #6c757d;
}

.no-data-content p {
  margin: 0;
  font-size: 14px;
  color: #adb5bd;
}

/* Selected Entity Card */
.selected-entity-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
  margin-bottom: 16px;
}

.entity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.entity-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.entity-badge {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
}

.entity-details h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.entity-details p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.entity-balance {
  text-align: right;
}

.balance-label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.balance-amount {
  font-size: 18px;
  font-weight: 700;
  color: var(--ion-color-primary);
}

/* Filter Card */
.filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
  margin-bottom: 16px;
}

.date-item {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 8px;
}

.date-input {
  --background: #f8f9fa;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
}

.filter-button {
  --border-radius: 12px;
  height: 44px;
  margin-top: 20px;
}

/* Transactions Card */
.transactions-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
}

.transactions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transactions-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.transactions-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.print-button {
  --border-radius: 12px;
  --padding-start: 12px;
  --padding-end: 12px;
  height: 36px;
}

/* Transactions Container */
.transactions-container {
  padding: 0;
}

.transaction-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Transaction Card Styling */
.transaction-card {
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  border-left: 4px solid transparent;
  overflow: hidden;
}

.transaction-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Transaction Type Specific Styling */
.credit-transaction {
  border-left-color: #28a745;
  background: linear-gradient(135deg, #f8fff8 0%, #ffffff 100%);
}

.debit-transaction {
  border-left-color: #dc3545;
  background: linear-gradient(135deg, #fff8f8 0%, #ffffff 100%);
}

.credit-transaction:hover {
  background: linear-gradient(135deg, #f0fff0 0%, #fafffe 100%);
}

.debit-transaction:hover {
  background: linear-gradient(135deg, #fff0f0 0%, #fffafa 100%);
}

/* Transaction Content Layout */
.transaction-content {
  padding: 0;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

/* Transaction Type Badge */
.transaction-type-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.credit-badge {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
  color: #28a745;
  border: 1px solid #c3e6cb;
}

.debit-badge {
  background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
  color: #dc3545;
  border: 1px solid #f5c6cb;
}

.type-icon {
  font-size: 16px;
}

.type-label {
  font-weight: 700;
}

/* Transaction Amount Display */
.transaction-amount-display {
  display: flex;
  align-items: center;
  gap: 4px;
}

.amount-sign {
  font-size: 18px;
  font-weight: 700;
  width: 20px;
  text-align: center;
}

.amount-sign.positive {
  color: #28a745;
}

.amount-sign.negative {
  color: #dc3545;
}

.amount-value {
  font-size: 18px;
  font-weight: 700;
}

.amount-value.credit-amount {
  color: #28a745;
}

.amount-value.debit-amount {
  color: #dc3545;
}

/* Transaction Body */
.transaction-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.date-section {
  display: flex;
  align-items: center;
}

.date-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-icon {
  font-size: 16px;
  color: #6c757d;
  min-width: 16px;
}

.date-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.date {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.time {
  font-size: 12px;
  color: #6c757d;
}

/* Transaction Meta Information */
.transaction-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.balance-info,
.payment-mode-info,
.discount-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.balance-label,
.discount-label {
  color: #6c757d;
  font-weight: 500;
}

.balance-amount {
  color: var(--ion-color-primary);
  font-weight: 600;
}

.payment-mode {
  color: #495057;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.discount-amount {
  color: #ffc107;
  font-weight: 600;
}

/* Remarks Section */
.remarks-section {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #e9ecef;
}

.remarks {
  font-size: 12px;
  color: #495057;
  font-style: italic;
  line-height: 1.4;
  flex: 1;
}

/* No Transactions */
.no-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16px;
  border: 2px dashed #dee2e6;
  margin: 20px 0;
}

.no-transactions-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-transactions h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
}

.no-transactions p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 576px) {
  .ledger-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .summary-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }

  .entity-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .entity-balance {
    text-align: left;
  }

  .transactions-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .filter-button {
    height: 40px;
    font-size: 12px;
  }

  /* Mobile-specific transaction card adjustments */
  .transaction-card {
    border-radius: 12px;
    margin: 0 4px;
  }

  .transaction-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .transaction-amount-display {
    align-self: flex-end;
  }

  .amount-value {
    font-size: 16px;
  }

  .amount-sign {
    font-size: 16px;
  }

  .transaction-type-badge {
    padding: 6px 10px;
    font-size: 11px;
  }

  .type-icon {
    font-size: 14px;
  }

  .transaction-meta {
    gap: 6px;
  }

  .balance-info,
  .payment-mode-info,
  .discount-info {
    font-size: 12px;
  }

  .detail-icon {
    font-size: 14px;
  }

  .date {
    font-size: 13px;
  }

  .time {
    font-size: 11px;
  }

  .remarks-section {
    padding: 6px;
  }

  .remarks {
    font-size: 11px;
  }

  .no-transactions {
    padding: 40px 16px;
  }

  .no-transactions-icon {
    font-size: 48px;
  }

  .no-transactions h3 {
    font-size: 16px;
  }

  .no-transactions p {
    font-size: 13px;
  }
}

@media (min-width: 768px) {
  .ledger-content {
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }

  .entity-badge {
    width: 56px;
    height: 56px;
    font-size: 18px;
  }

  .balance-amount {
    font-size: 20px;
  }

  /* Tablet-specific transaction card improvements */
  .transaction-cards {
    gap: 16px;
  }

  .transaction-card {
    border-radius: 16px;
  }

  .amount-value {
    font-size: 20px;
  }

  .amount-sign {
    font-size: 20px;
  }

  .transaction-type-badge {
    padding: 10px 14px;
    font-size: 13px;
  }

  .type-icon {
    font-size: 18px;
  }

  .detail-icon {
    font-size: 18px;
  }

  .date {
    font-size: 15px;
  }

  .time {
    font-size: 13px;
  }

  .balance-info,
  .payment-mode-info,
  .discount-info {
    font-size: 14px;
  }

  .remarks {
    font-size: 13px;
  }

  .no-transactions {
    padding: 80px 40px;
  }

  .no-transactions-icon {
    font-size: 72px;
  }

  .no-transactions h3 {
    font-size: 20px;
  }

  .no-transactions p {
    font-size: 15px;
  }
}

@media (min-width: 1024px) {
  .ledger-content {
    --padding-start: 32px;
    --padding-end: 32px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }

  .entity-badge {
    width: 64px;
    height: 64px;
    font-size: 20px;
  }

  .balance-amount {
    font-size: 22px;
  }

  /* Desktop-specific transaction card enhancements */
  .transaction-cards {
    gap: 20px;
  }

  .transaction-card {
    border-radius: 18px;
  }

  .transaction-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
  }

  .amount-value {
    font-size: 22px;
  }

  .amount-sign {
    font-size: 22px;
  }

  .transaction-type-badge {
    padding: 12px 16px;
    font-size: 14px;
  }

  .type-icon {
    font-size: 20px;
  }

  .detail-icon {
    font-size: 20px;
  }

  .date {
    font-size: 16px;
  }

  .time {
    font-size: 14px;
  }

  .balance-info,
  .payment-mode-info,
  .discount-info {
    font-size: 15px;
  }

  .remarks {
    font-size: 14px;
  }

  .no-transactions {
    padding: 100px 60px;
  }

  .no-transactions-icon {
    font-size: 80px;
  }

  .no-transactions h3 {
    font-size: 22px;
  }

  .no-transactions p {
    font-size: 16px;
  }
}

/* Modal Styling */
.modal-content {
  --background: #f8f9fa;
}

.modal-transaction-header {
  background: var(--modal-background);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.modal-type-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal-type-icon {
  font-size: 20px;
}

.modal-type-label {
  font-weight: 700;
}

.modal-amount-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-amount-sign {
  font-size: 24px;
  font-weight: 700;
  width: 30px;
  text-align: center;
}

.modal-amount-value {
  font-size: 24px;
  font-weight: 700;
}

.modal-details-section {
  background: var(--modal-background);
  border-radius: 16px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.modal-detail-item {
  --padding-start: 20px;
  --padding-end: 20px;
  --min-height: 60px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-detail-item:last-child {
  border-bottom: none;
}

.modal-detail-icon {
  font-size: 20px;
  color: var(--ion-color-primary);
  margin-right: 16px;
}

.modal-detail-item ion-label h3 {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 4px 0;
}

.modal-detail-item ion-label p {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.discount-text {
  color: #ffc107 !important;
}

.balance-text {
  color: var(--ion-color-primary) !important;
}

.modal-remarks-section {
  background: var(--modal-background);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.remarks-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
}

.remarks-icon {
  font-size: 18px;
  color: var(--ion-color-primary);
}

.remarks-content {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  border-left: 4px solid var(--ion-color-primary);
}

.remarks-content p {
  margin: 0;
  font-size: 14px;
  color: #495057;
  line-height: 1.5;
  font-style: italic;
}

.modal-attachment-section {
  background: var(--modal-background);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.attachment-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 16px 0;
}

.attachment-icon {
  font-size: 18px;
  color: var(--ion-color-primary);
}

.attachment-preview {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.attachment-preview:hover {
  transform: scale(1.02);
}

.attachment-image {
  width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: cover;
  display: block;
}

.attachment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.attachment-preview:hover .attachment-overlay {
  opacity: 1;
}

.view-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.attachment-overlay span {
  font-size: 14px;
  font-weight: 500;
}

.ledger-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.ledger-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}
  