.checklist-content {
  --background: #f5f5f5;
}

.summary-section {
  padding: 16px;

  .summary-card {
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .summary-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .summary-info {
        h3 {
          margin: 0 0 8px 0;
          font-size: 14px;
          color: var(--ion-color-medium);
          font-weight: 500;
        }

        h1 {
          margin: 0;
          font-size: 28px;
          font-weight: bold;
          color: var(--ion-color-dark);
        }
      }

      .summary-icon {
        font-size: 32px;
        opacity: 0.7;
      }
    }

    &.total-card .summary-icon {
      color: var(--ion-color-primary);
    }

    &.pending-card .summary-icon {
      color: var(--ion-color-warning);
    }

    &.progress-card .summary-icon {
      color: var(--ion-color-secondary);
    }

    &.completed-card .summary-icon {
      color: var(--ion-color-success);
    }
  }
}

.filters-card {
  margin: 0 16px 16px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.checklists-section {
  padding: 0 16px 16px 16px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: var(--ion-color-dark);
      font-weight: 600;
    }
  }

  .empty-state {
    .empty-content {
      text-align: center;
      padding: 32px 16px;

      .empty-icon {
        font-size: 64px;
        color: var(--ion-color-medium);
        margin-bottom: 16px;
      }

      h3 {
        color: var(--ion-color-dark);
        margin-bottom: 8px;
      }

      p {
        color: var(--ion-color-medium);
        margin-bottom: 24px;
      }
    }
  }

  .checklist-card {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .checklist-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .checklist-title-section {
        flex: 1;
        margin-right: 16px;

        ion-card-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--ion-color-dark);
          margin-bottom: 4px;
        }

        ion-card-subtitle {
          color: var(--ion-color-medium);
          font-size: 14px;
        }
      }

      .checklist-badges {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }

    .checklist-details {
      margin-bottom: 16px;

      .detail-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        ion-icon {
          margin-right: 8px;
          color: var(--ion-color-medium);
          font-size: 16px;
        }

        span {
          font-size: 14px;
          color: var(--ion-color-dark);
        }
      }
    }

    .checklist-actions {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      ion-button {
        --padding-start: 8px;
        --padding-end: 8px;
        font-size: 12px;
      }
    }
  }
}

.modal-actions {
  margin-top: 24px;

  ion-button {
    margin: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .summary-section {
    padding: 12px;

    .summary-card .summary-content {
      .summary-info h1 {
        font-size: 24px;
      }

      .summary-icon {
        font-size: 28px;
      }
    }
  }

  .checklists-section {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .checklist-card {
      .checklist-header {
        flex-direction: column;
        gap: 12px;

        .checklist-badges {
          flex-direction: row;
          align-self: flex-start;
        }
      }

      .checklist-actions {
        justify-content: center;
      }
    }
  }
}