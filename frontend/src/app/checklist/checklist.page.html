<app-header [title]="'Checklists'"></app-header>

<ion-content class="checklist-content">
  <!-- Summary Cards -->
  <div class="summary-section">
    <ion-row>
      <ion-col size="6" size-md="3">
        <ion-card class="summary-card total-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total</h3>
                <h1>{{summary.total}}</h1>
              </div>
              <ion-icon name="list-outline" class="summary-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="summary-card pending-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Pending</h3>
                <h1>{{summary.pending}}</h1>
              </div>
              <ion-icon name="time-outline" class="summary-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="summary-card progress-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>In Progress</h3>
                <h1>{{summary.in_progress}}</h1>
              </div>
              <ion-icon name="play-circle-outline" class="summary-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6" size-md="3">
        <ion-card class="summary-card completed-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Completed</h3>
                <h1>{{summary.completed}}</h1>
              </div>
              <ion-icon name="checkmark-circle-outline" class="summary-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>

  <!-- Filters Section -->
  <ion-card class="filters-card">
    <ion-card-header>
      <ion-card-title>Filters</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-row>
        <ion-col size="12" size-md="4">
          <ion-select
            [(ngModel)]="statusFilter"
            (ionChange)="onFilterChange()"
            placeholder="Filter by Status"
            interface="popover">
            <ion-select-option value="all">All Status</ion-select-option>
            <ion-select-option value="pending">Pending</ion-select-option>
            <ion-select-option value="in_progress">In Progress</ion-select-option>
            <ion-select-option value="completed">Completed</ion-select-option>
            <ion-select-option value="overdue">Overdue</ion-select-option>
          </ion-select>
        </ion-col>

        <ion-col size="12" size-md="4">
          <ion-select
            [(ngModel)]="routeFilter"
            (ionChange)="onFilterChange()"
            placeholder="Filter by Route"
            interface="popover">
            <ion-select-option [value]="null">All Routes</ion-select-option>
            <ion-select-option *ngFor="let route of routes" [value]="route.id">
              {{route.name}}
            </ion-select-option>
          </ion-select>
        </ion-col>

        <ion-col size="12" size-md="4">
          <ion-select
            [(ngModel)]="buyerFilter"
            (ionChange)="onFilterChange()"
            placeholder="Filter by Buyer"
            interface="popover">
            <ion-select-option [value]="null">All Buyers</ion-select-option>
            <ion-select-option *ngFor="let buyer of buyers" [value]="buyer.id">
              {{buyer.name}}
            </ion-select-option>
          </ion-select>
        </ion-col>
      </ion-row>
    </ion-card-content>
  </ion-card>

  <!-- Checklists List -->
  <div class="checklists-section">
    <div class="section-header">
      <h3>Checklists ({{filteredChecklists.length}})</h3>
      <ion-button fill="clear" (click)="openCreateModal()">
        <ion-icon name="add" slot="start"></ion-icon>
        New Checklist
      </ion-button>
    </div>

    <div *ngIf="filteredChecklists.length === 0" class="empty-state">
      <ion-card>
        <ion-card-content>
          <div class="empty-content">
            <ion-icon name="clipboard-outline" class="empty-icon"></ion-icon>
            <h3>No Checklists Found</h3>
            <p>Create your first checklist to get started</p>
            <ion-button (click)="openCreateModal()">
              <ion-icon name="add" slot="start"></ion-icon>
              Create Checklist
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <ion-card *ngFor="let checklist of filteredChecklists" class="checklist-card">
      <ion-card-header>
        <div class="checklist-header">
          <div class="checklist-title-section">
            <ion-card-title>{{checklist.title}}</ion-card-title>
            <ion-card-subtitle *ngIf="checklist.description">{{checklist.description}}</ion-card-subtitle>
          </div>
          <div class="checklist-badges">
            <ion-chip [color]="getStatusColor(checklist.status)" size="small">
              <ion-label>{{checklist.status | titlecase}}</ion-label>
            </ion-chip>
            <ion-chip [color]="getPriorityColor(checklist.priority)" size="small">
              <ion-label>{{checklist.priority | titlecase}}</ion-label>
            </ion-chip>
          </div>
        </div>
      </ion-card-header>

      <ion-card-content>
        <div class="checklist-details">
          <div class="detail-row" *ngIf="checklist.route_name">
            <ion-icon name="location-outline"></ion-icon>
            <span>Route: {{checklist.route_name}}</span>
          </div>

          <div class="detail-row" *ngIf="checklist.buyer_name">
            <ion-icon name="person-outline"></ion-icon>
            <span>Buyer: {{checklist.buyer_name}}</span>
          </div>

          <div class="detail-row" *ngIf="checklist.due_date">
            <ion-icon name="calendar-outline"></ion-icon>
            <span>Due: {{formatDate(checklist.due_date)}}</span>
          </div>

          <div class="detail-row" *ngIf="checklist.completion_percentage !== undefined">
            <ion-icon name="pie-chart-outline"></ion-icon>
            <span>Progress: {{checklist.completion_percentage}}%</span>
          </div>
        </div>

        <div class="checklist-actions">
          <ion-button
            fill="clear"
            size="small"
            (click)="openDetailModal(checklist)">
            <ion-icon name="eye-outline" slot="start"></ion-icon>
            View Details
          </ion-button>

          <ion-button
            *ngIf="checklist.status === 'pending'"
            fill="clear"
            size="small"
            color="primary"
            (click)="startChecklist(checklist)">
            <ion-icon name="play-outline" slot="start"></ion-icon>
            Start
          </ion-button>

          <ion-button
            *ngIf="checklist.status === 'in_progress'"
            fill="clear"
            size="small"
            color="success"
            (click)="submitChecklist(checklist)">
            <ion-icon name="checkmark-outline" slot="start"></ion-icon>
            Submit
          </ion-button>

          <ion-button
            fill="clear"
            size="small"
            color="danger"
            (click)="deleteChecklist(checklist)">
            <ion-icon name="trash-outline" slot="start"></ion-icon>
            Delete
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>

<!-- Create Checklist Modal -->
<ion-modal [isOpen]="isCreateModalOpen" (didDismiss)="closeCreateModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Create New Checklist</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeCreateModal()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <ion-card>
        <ion-card-content>
          <ion-item>
            <ion-label position="stacked">Title *</ion-label>
            <ion-input
              [(ngModel)]="newChecklist.title"
              placeholder="Enter checklist title"
              required>
            </ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Description</ion-label>
            <ion-textarea
              [(ngModel)]="newChecklist.description"
              placeholder="Enter checklist description"
              rows="3">
            </ion-textarea>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Priority</ion-label>
            <ion-select [(ngModel)]="newChecklist.priority" interface="popover">
              <ion-select-option value="low">Low</ion-select-option>
              <ion-select-option value="medium">Medium</ion-select-option>
              <ion-select-option value="high">High</ion-select-option>
            </ion-select>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Route (Optional)</ion-label>
            <ion-select [(ngModel)]="newChecklist.route" interface="popover">
              <ion-select-option [value]="null">No Route</ion-select-option>
              <ion-select-option *ngFor="let route of routes" [value]="route.id">
                {{route.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Buyer (Optional)</ion-label>
            <ion-select [(ngModel)]="newChecklist.buyer" interface="popover">
              <ion-select-option [value]="null">No Buyer</ion-select-option>
              <ion-select-option *ngFor="let buyer of buyers" [value]="buyer.id">
                {{buyer.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">Due Date (Optional)</ion-label>
            <ion-datetime
              [(ngModel)]="newChecklist.due_date"
              presentation="date-time"
              placeholder="Select due date">
            </ion-datetime>
          </ion-item>

          <div class="modal-actions">
            <ion-button expand="block" (click)="createChecklist()">
              <ion-icon name="add" slot="start"></ion-icon>
              Create Checklist
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </ion-content>
  </ng-template>
</ion-modal>
