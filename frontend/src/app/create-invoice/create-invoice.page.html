<!-- <app-header [title]="'Create Invoice'"></app-header> -->

<ion-header>
  <app-back-button [url]="getReturnUrl()" [size]="'large'"></app-back-button>
  <!-- Top -->

  <!-- Compact Header -->
  <div class="compact-header">
    <!-- Essential Info Row -->
    <ion-row class="essential-row">
      <ion-col size="6">
        <ion-item class="compact-item">
          <ion-input [(ngModel)]="date" [value]="date | date : 'YYYY-MM-dd'" placeholder="Date" type="date">
          </ion-input>
        </ion-item>
      </ion-col>
      <ion-col size="6">
        <ion-item class="compact-item" *ngIf="buyer_data.length">
          <ionic-selectable name="agent" [shouldFocusSearchbar]="true" [(ngModel)]="selectedBuyer" placeholder="Select Buyer"
            [items]="buyer_data" itemValueField="id" itemTextField="name" [canSearch]="true"
            (ngModelChange)="setBuyer($event)">
          </ionic-selectable>
        </ion-item>
        <ion-item class="compact-item" *ngIf="supplier_data.length">
          <ionic-selectable name="agent" [shouldFocusSearchbar]="true" [(ngModel)]="selectedSupplier"
            placeholder="Select Supplier" [items]="supplier_data" itemValueField="id" itemTextField="name" [canSearch]="true"
            (ngModelChange)="setSupplier($event)">
          </ionic-selectable>
        </ion-item>
      </ion-col>
    </ion-row>

    <!-- Action Buttons Row -->
    <ion-row class="action-row">
      <ion-col size="3">
        <ion-button color="primary" (click)="save()" size="small" expand="block">
          <ion-icon name="save-outline"></ion-icon>
        </ion-button>
      </ion-col>
      <ion-col size="3">
        <ion-button (click)="togglePaymentDetails()" size="small" fill="outline" expand="block">
          <ion-icon name="card-outline"></ion-icon>
        </ion-button>
      </ion-col>
      <ion-col size="3">
        <ion-button (click)="toggleMoreActions()" size="small" fill="clear" expand="block">
          <ion-icon name="ellipsis-horizontal"></ion-icon>
        </ion-button>
      </ion-col>
      <ion-col size="3">
        <!-- Product Mode Toggle -->
        <ion-button 
          (click)="toggleProductMode()" 
          size="small" 
          [fill]="isBrandBasedMode ? 'solid' : 'outline'"
          [color]="isBrandBasedMode ? 'secondary' : 'medium'"
          expand="block">
          <ion-icon [name]="isBrandBasedMode ? 'pricetag' : 'apps'"></ion-icon>
        </ion-button>
      </ion-col>
    </ion-row>

    <!-- More Actions (Collapsible) -->
    <ion-row class="more-actions" *ngIf="showMoreActions">
      <ion-col size="12">
        <div class="action-buttons">
          <ion-button (click)="changeRateRequest()" size="small" fill="outline">
            <ion-icon *ngIf="lock" name="lock-closed-outline"></ion-icon>
            <ion-icon *ngIf="!lock" name="lock-open-outline"></ion-icon>
          </ion-button>
          <ion-button (click)="addRemarks()" size="small" fill="outline">
            <ion-icon name="chatbox-outline"></ion-icon>
          </ion-button>
          <ion-button *ngIf="billingHeaderData?.length" (click)="addbillingHeaderData()" size="small" fill="outline">
            <ion-icon name="add-circle-outline"></ion-icon>
          </ion-button>
          <ion-button (click)="assignSO()" size="small" fill="outline">
            <ion-icon name="document-text-outline"></ion-icon>
          </ion-button>
          <ion-button (click)="openProductForm()" size="small" fill="outline" color="success">
            <ion-icon name="add-outline"></ion-icon>
          </ion-button>
          <!-- Image upload button removed - moved to sales bill page -->
        </div>
      </ion-col>
    </ion-row>


    <!-- Brand Filter (Always Visible) -->
    <ion-row class="brand-row">
      <ion-col size="12">
        <ion-segment [value]="selectedBrand" (ionChange)="filterByBrand($event)" [scrollable]="true" mode="ios">
          <ion-segment-button *ngFor="let b of brand" [value]="b.name">
            <ion-label>{{b.name}}</ion-label>
          </ion-segment-button>
          <ion-segment-button value="all">
            <ion-label>All</ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-col>
    </ion-row>
  </div>
</ion-header>

<ion-content>

  <ion-item lines="none" *ngIf="filterEmpty">
    <ion-text color="danger">
      <h6>No data available for searched item.</h6>
    </ion-text>
  </ion-item>

  <!-- Start : Invoice item section main -->
  <div class="invoice-items-container">
    <ion-card class="invoice-items-card">
      <!-- Header Row -->
      <div class="invoice-items-header">
        <ion-grid class="ion-no-padding">
          <ion-row class="header-row">
            <ng-container *ngFor="let setting of billing_field_settings">
              <ion-col [size]="setting.size" class="header-col">
                <ion-label class="header-label">{{setting.displayName}}</ion-label>
              </ion-col>
            </ng-container>
          </ion-row>
        </ion-grid>
      </div>

      <!-- Items Content -->
      <div class="invoice-items-content">
        <ion-grid class="ion-no-padding">
          <ng-container *ngFor="let pd of displayProducts; let i=index;">
            <ion-row class="invoice-item-row" [class.has-values]="(pd.weight > 0 || pd.no > 0)">
              <ng-container *ngFor="let field of billing_field_settings">
                <ng-container [ngSwitch]="field.slug">
                  <!-- Item Column -->
                  <ion-col *ngSwitchCase="'item'" [size]="field.size" class="item-col">
                    <div class="item-info" (click)="handleClick($event,pd.id)">
                      <ion-text class="item-code">{{pd.short_code}}</ion-text>
                      <ion-text class="item-name">{{pd.name}}</ion-text>
                      <div class="item-details">
                        <div class="detail-item" *ngIf="pd.mrp">
                          <span class="detail-label">MRP:</span>
                          <span class="detail-value mrp">₹{{pd.mrp}}</span>
                        </div>
                        <div class="detail-item" *ngIf="pd.rate">
                          <span class="detail-label">Rate:</span>
                          <span class="detail-value rate">₹{{pd.rate}}</span>
                        </div>
                        <div class="detail-item" *ngIf="pd.margin">
                          <span class="detail-label">Margin:</span>
                          <span class="detail-value margin">{{pd.margin}}%</span>
                        </div>
                        <div class="detail-item" *ngIf="pd.tax_rate">
                          <span class="detail-label">Tax:</span>
                          <span class="detail-value tax">{{pd.tax_rate}}%</span>
                        </div>
                      </div>
                    </div>
                  </ion-col>

                  <!-- Rate Column -->
                  <ion-col *ngSwitchCase="'rate'" [size]="field.size" class="input-col">
                    <div class="input-wrapper">
                      <ion-input
                        class="invoice-input rate-input"
                        [disabled]="!billing_settings.enableRateChangeField"
                        type="number"
                        [min]="0"
                        [step]="1"
                        [placeholder]="field.displayName"
                        [(ngModel)]="pd.rate"
                        (ngModelChange)="modifyRate($event, pd)"
                        (keyup.enter)="changeFocusOnCrate(i)"
                        (keypress)="numberOnlyValidation($event)">
                      </ion-input>
                    </div>
                  </ion-col>

                  <!-- Pieces Column -->
                  <ng-container *ngSwitchCase="'pcs'">
                    <ion-col *ngIf="billing_settings.enablePopoverWeight" [size]="field.size" class="display-col">
                      <div class="display-wrapper" (click)="presentWeightPopover($event,pd.id)">
                        <ion-text class="display-value">{{pd?.weight?pd.weight:'0'}}</ion-text>
                      </div>
                    </ion-col>
                    <ion-col [size]="field.size" class="input-col" *ngIf="!billing_settings.enablePopoverWeight">
                      <div class="input-wrapper">
                        <ion-input
                          class="invoice-input pcs-input"
                          type="number"
                          [min]="0"
                          [step]="1"
                          [placeholder]="field.displayName"
                          [(ngModel)]="pd.weight"
                          (ngModelChange)="modifyWeight($event, pd)"
                          (keyup.enter)="changeFocusOnCrate(i)"
                          (keypress)="numberOnlyValidation($event)">
                        </ion-input>
                      </div>
                    </ion-col>
                  </ng-container>

                  <!-- Box Column -->
                  <ion-col *ngSwitchCase="'box'" [size]="field.size" class="input-col">
                    <div class="input-wrapper">
                      <ion-input
                        class="invoice-input box-input"
                        #inputCrates
                        type="number"
                        [min]="0"
                        [step]="1"
                        [placeholder]="field.displayName"
                        [(ngModel)]="pd.no"
                        (ngModelChange)="modifyNo($event, pd)"
                        (keyup.enter)="changeFocusOnCrate(i)"
                        (keypress)="numberOnlyValidation($event)">
                      </ion-input>
                    </div>
                  </ion-col>

                  <!-- Total Column -->
                  <ion-col *ngSwitchCase="'total'" [size]="field.size" class="total-col">
                    <div class="total-wrapper">
                      <ion-text class="total-amount">{{pd?.total ? pd.total.toFixed(2) : '0.00'}}</ion-text>
                    </div>
                  </ion-col>

                  <!-- Description Column -->
                  <ion-col *ngSwitchCase="'desc'" [size]="field.size" class="input-col">
                    <div class="input-wrapper">
                      <ion-input
                        class="invoice-input desc-input"
                        type="text"
                        [(ngModel)]="pd.desc"
                        (ngModelChange)="modifyDesc($event, pd)"
                        [placeholder]="field.displayName">
                      </ion-input>
                    </div>
                  </ion-col>
                </ng-container>
              </ng-container>
            </ion-row>
          </ng-container>
        </ion-grid>
      </div>
    </ion-card>
  </div>
  <!-- Ends : Invoice item section main  -->
</ion-content>

<ion-popover #popover [isOpen]="isProductDetailsOpen" (didDismiss)="isProductDetailsOpen = false">
  <ng-template>
    <ion-row class="ion-no-margin ion-no-padding" *ngIf="billing_settings.enableMarginalView">
      <ion-col size="12">
        <ion-item>
          <ion-label position="stacked">MRP
            <ion-text color="danger">*</ion-text>
          </ion-label>
          <ion-input [disabled]="lock" [(ngModel)]="product_data[selectedIndex].mrp"
            (ngModelChange)="updateProductData('mrp')" type="number" name="mrp" required="true"></ion-input>
        </ion-item>
      </ion-col>
      <ion-col size="6">
        <ion-item>
          <ion-label position="stacked">Rate
            <ion-text color="danger">*</ion-text>
          </ion-label>
          <ion-input [disabled]="lock" [(ngModel)]="product_data[selectedIndex].rate"
            (ngModelChange)="updateProductData('rate')" type="number" name="rate" required="true"></ion-input>
        </ion-item>
      </ion-col>
      <ion-col size="6">
        <ion-item>
          <ion-label position="stacked">Margin
            <ion-text color="danger">*</ion-text>
          </ion-label>
          <ion-input [disabled]="lock" [(ngModel)]="product_data[selectedIndex].margin"
            (ngModelChange)="updateProductData('margin')" type="number" name="margin" required="true"></ion-input>
        </ion-item>
      </ion-col>
    </ion-row>
    <ion-item *ngIf="billing_settings.enableRemarksItemView">
      <ion-label position="stacked">Remarks
      </ion-label>
      <ion-textarea [(ngModel)]="product_data[selectedIndex].remarks" name="remarks" required="false"></ion-textarea>
    </ion-item>
  </ng-template>
</ion-popover>

<ion-popover #popover [isOpen]="changeRateRequestFlag" (didDismiss)="changeRateRequestFlag = false">
  <ng-template>
    <ion-text *ngIf="message" color="danger">
      <p>{{message}}</p>
    </ion-text>
    <form (ngSubmit)="changeRateRequestFunction(passwordForm.value)" #passwordForm="ngForm">
      <ion-input autofocus="true" type="password" ngModel name="password" placeholder="Password" required="true">
      </ion-input>
      <ion-button style="float: right;" type="submit" [disabled]="passwordForm.invalid" fill="solid" shape="round">
        OK
      </ion-button>
    </form>

  </ng-template>
</ion-popover>

<ion-popover #popover [isOpen]="isWeightOpen" (didDismiss)="isWeightOpen = false">
  <ng-template>
    <form (ngSubmit)="weightButton(weightForm.value)" #weightForm="ngForm">
      <ion-input autofocus="true" #weightInput type="tel" *ngIf="selectedIndex != null"
        [(ngModel)]="product_data[selectedIndex].weights" name="weights" placeholder="Weights" required="true">
      </ion-input>
      <ion-button style="float: right;" type="submit" [disabled]="weightForm.invalid" fill="solid" shape="round">
        OK
      </ion-button>
    </form>

  </ng-template>
</ion-popover>


<ion-footer>

  <!-- Search Section -->
  <div class="search-section">
    <ion-searchbar
      placeholder="Search products..."
      (ionChange)="onSearchChange($event)"
      [debounce]="250"
      showCancelButton="focus">
    </ion-searchbar>

    <!-- Create New Product Section -->
    <div class="empty-state" *ngIf="filterEmpty">
      <p>Product not found</p>
      <ion-button size="small" fill="outline" color="primary" (click)="openProductForm()">
        <ion-icon name="add" slot="start"></ion-icon>
        Create
      </ion-button>
    </div>
  </div>
  <!-- Sticky Invoice Summary Footer -->
  <div class="sticky-summary">
    <ion-row class="summary-row">
      <ion-col size="3" class="summary-col">
        <div class="summary-item">
          <span class="summary-label">Items</span>
          <span class="summary-value">{{totalNo}}</span>
        </div>
      </ion-col>
      <ion-col size="3" class="summary-col">
        <div class="summary-item">
          <span class="summary-label">Weight</span>
          <span class="summary-value">{{totalWeight}}</span>
        </div>
      </ion-col>
      <ion-col size="6" class="summary-col">
        <div class="summary-item">
          <span class="summary-label">Subtotal</span>
          <span class="summary-value">₹{{invoiceSubtotal.toFixed(2)}}</span>
        </div>
      </ion-col>
    </ion-row>

    <!-- Tax Row -->
    <ion-row class="summary-row" *ngIf="invoiceTaxAmount > 0">
      <ion-col size="6" class="summary-col">
        <div class="summary-item">
          <span class="summary-label">Tax Total</span>
          <span class="summary-value">₹{{invoiceTaxAmount.toFixed(2)}}</span>
        </div>
      </ion-col>
      <ion-col size="6" class="summary-col">
        <div class="summary-item">
          <span class="summary-label">Gross Total</span>
          <span class="summary-value">₹{{invoiceGrossTotal.toFixed(2)}}</span>
        </div>
      </ion-col>
    </ion-row>

    <!-- Rounding Row -->
    <!-- <ion-row class="summary-row" *ngIf="roundingConfig.enabled && roundingAdjustment !== 0">
      <ion-col size="6" class="summary-col">
        <div class="summary-item">
          <span class="summary-label">Rounding Off</span>
          <span class="summary-value">{{formatRoundingAdjustment()}}</span>
        </div>
      </ion-col>
      <ion-col size="6" class="summary-col total-col">
        <div class="summary-item total-item">
          <span class="summary-label">Net Amount</span>
          <span class="summary-value">₹{{invoiceNetAmount.toFixed(2)}}</span>
        </div>
      </ion-col>
    </ion-row> -->

    <!-- Final Total Row (when no rounding) -->
    <!-- <ion-row class="summary-row" *ngIf="!roundingConfig.enabled || roundingAdjustment === 0">
      <ion-col size="6" class="summary-col total-col">
        <div class="summary-item total-item">
          <span class="summary-label">Net Amount</span>
          <span class="summary-value">₹{{invoiceNetAmount.toFixed(2)}}</span>
        </div>
      </ion-col>
    </ion-row> -->
  </div>

  <!-- Payment Details Section (Alternative to Modal) -->
  <div class="payment-details-footer" *ngIf="showPaymentDetails">
    <div class="payment-content">
      <!-- Balance Information -->
      <div class="balance-info">
        <div class="balance-row">
          <span>Opening Balance:</span>
          <span>₹{{agentLastSalesInvoiceRecord && agentLastSalesInvoiceRecord.id ?
            agentLastSalesInvoiceRecord.current_balance.toFixed(2) : '0.00'}}</span>
        </div>
        <div class="balance-row">
          <span>Bill Amount:</span>
          <span>₹{{totalAmount.toFixed(2)}}</span>
        </div>
      </div>

      <!-- Discount Fields -->
      <div class="discount-section" *ngIf="billingAdditionalFields.length">
        <div *ngFor="let field of billingAdditionalFields" class="discount-item">
          <ng-container [ngSwitch]="field.fieldType">
            <ng-container *ngSwitchCase="'discount'">
              <div class="discount-inputs">
                <span class="discount-label">{{ field.displayName }}:</span>
                <ion-input
                  type="number"
                  [min]="0"
                  [step]="0.1"
                  placeholder="%"
                  [(ngModel)]="field.percentage"
                  (ngModelChange)="handleDiscountChange(field, 'percentage')"
                  class="discount-percent">
                </ion-input>
                <ion-input
                  type="number"
                  [min]="0"
                  [step]="0.01"
                  placeholder="Amount"
                  [(ngModel)]="field.value"
                  (ngModelChange)="handleDiscountChange(field, 'value')"
                  class="discount-amount">
                </ion-input>
              </div>
            </ng-container>
            <ng-container *ngSwitchDefault>
              <div class="other-field">
                <span class="field-label">{{ field.displayName }}:</span>
                <ion-input
                  [type]="field.fieldType"
                  [min]="0"
                  [step]="0.01"
                  [placeholder]="field.placeholder || '0.00'"
                  [(ngModel)]="field.value"
                  (ngModelChange)="handleDynamicFieldChange(field.slug, $event)"
                  class="field-input">
                </ion-input>
              </div>
            </ng-container>
          </ng-container>
        </div>
      </div>

      <!-- Rounding Section -->
      <div class="rounding-section">
        <div class="rounding-controls">
          <div class="rounding-toggle">
            <span class="rounding-label">Round to Nearest Rupee:</span>
            <ion-toggle
              [(ngModel)]="roundingConfig.enabled"
              (ngModelChange)="onRoundingToggle()"
              class="rounding-toggle-btn">
            </ion-toggle>
          </div>
        </div>
      </div>

      <!-- Payment Section -->
      <div class="payment-inputs">
        <div class="payment-row">
          <span class="payment-label">{{purchase ? 'Paid': 'Received'}} Amount:</span>
          <ion-input
            type="number"
            [min]="0"
            [step]="0.01"
            placeholder="Enter amount"
            [(ngModel)]="received_amount"
            (ngModelChange)="modifyReceivedAmount($event)"
            class="payment-amount">
          </ion-input>
        </div>

        <div class="payment-row" *ngIf="received_amount">
          <span class="payment-label">Payment Mode:</span>
          <ion-button
            fill="outline"
            size="small"
            (click)="selectPaymentMode()"
            class="payment-mode-btn">
            {{getSelectedPaymentModeName() || 'Select Payment Mode'}}
            <ion-icon name="chevron-down" slot="end"></ion-icon>
          </ion-button>
        </div>
      </div>

      <!-- Final Balance -->
      <div class="final-balance-footer">
        <div class="balance-row final">
          <span>Current Balance:</span>
          <span class="final-amount">₹{{closing_balance.toFixed(2)}}</span>
        </div>
      </div>
    </div>
  </div>
</ion-footer>

<!-- Payment Modal -->
<ion-modal [isOpen]="isPaymentModalOpen" (didDismiss)="closePaymentModal()">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Payment & Balance Details</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closePaymentModal()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="payment-modal-content">
      <!-- Balance Information -->
      <div class="balance-section">
        <h3>Balance Information</h3>
        <div class="balance-item">
          <span>Opening Balance</span>
          <span>₹{{agentLastSalesInvoiceRecord && agentLastSalesInvoiceRecord.id ?
            agentLastSalesInvoiceRecord.current_balance.toFixed(2) : '0.00'}}</span>
        </div>
        <div class="balance-item">
          <span>Bill Amount</span>
          <span>₹{{totalAmount.toFixed(2)}}</span>
        </div>
      </div>

      <!-- Additional Fields -->
      <div class="additional-fields" *ngIf="billingAdditionalFields.length">
        <h3>Additional Charges</h3>
        <div *ngFor="let field of billingAdditionalFields" class="field-container">
          <ng-container [ngSwitch]="field.fieldType">
            <!-- Discount Field -->
            <ng-container *ngSwitchCase="'discount'">
              <div class="discount-field">
                <h4>{{ field.displayName }}</h4>
                <ion-row>
                  <ion-col size="6">
                    <ion-item>
                      <ion-label position="stacked">Percentage</ion-label>
                      <ion-input
                        type="number"
                        [min]="0"
                        [step]="0.1"
                        placeholder="%"
                        [(ngModel)]="field.percentage"
                        (ngModelChange)="handleDiscountChange(field, 'percentage')"
                        (keypress)="numberOnlyValidation($event)">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                  <ion-col size="6">
                    <ion-item>
                      <ion-label position="stacked">Amount</ion-label>
                      <ion-input
                        type="number"
                        [min]="0"
                        [step]="0.01"
                        placeholder="₹"
                        [(ngModel)]="field.value"
                        (ngModelChange)="handleDiscountChange(field, 'value')"
                        (keypress)="numberOnlyValidation($event)">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                </ion-row>
              </div>
            </ng-container>

            <!-- Other Fields -->
            <ng-container *ngSwitchDefault>
              <ion-item>
                <ion-label position="stacked">{{ field.displayName }}</ion-label>
                <ion-input
                  [type]="field.fieldType"
                  [min]="0"
                  [step]="0.01"
                  [placeholder]="field.placeholder || '0.00'"
                  [(ngModel)]="field.value"
                  (ngModelChange)="handleDynamicFieldChange(field.slug, $event)"
                  (keypress)="numberOnlyValidation($event)">
                </ion-input>
              </ion-item>
            </ng-container>
          </ng-container>
        </div>
      </div>

      <!-- Invoice Breakdown Section -->
      <div class="invoice-breakdown-section">
        <h3>Invoice Breakdown</h3>

        <ion-item>
          <ion-label>
            <h3>Subtotal</h3>
            <p>Items total</p>
          </ion-label>
          <ion-note slot="end">₹{{invoiceSubtotal.toFixed(2)}}</ion-note>
        </ion-item>

        <ion-item *ngIf="invoiceTaxAmount > 0">
          <ion-label>
            <h3>Tax Total</h3>
            <p>Calculated from subtotal</p>
          </ion-label>
          <ion-note slot="end">₹{{invoiceTaxAmount.toFixed(2)}}</ion-note>
        </ion-item>

        <ion-item>
          <ion-label>
            <h3>Gross Total</h3>
            <p>Subtotal + Tax</p>
          </ion-label>
          <ion-note slot="end">₹{{invoiceGrossTotal.toFixed(2)}}</ion-note>
        </ion-item>

        <!-- Rounding Toggle -->
        <ion-item>
          <ion-label>Round to Nearest Rupee</ion-label>
          <ion-toggle
            [(ngModel)]="roundingConfig.enabled"
            (ngModelChange)="onRoundingToggle()"
            slot="end">
          </ion-toggle>
        </ion-item>

        <ion-item *ngIf="roundingConfig.enabled && roundingAdjustment !== 0">
          <ion-label>
            <h3>Rounding Off</h3>
            <p>Adjustment to nearest rupee</p>
          </ion-label>
          <ion-note slot="end" [color]="roundingAdjustment > 0 ? 'success' : 'danger'">
            {{formatRoundingAdjustment()}}
          </ion-note>
        </ion-item>

        <ion-item>
          <ion-label>
            <h3>Net Amount</h3>
            <p>Final amount</p>
          </ion-label>
          <ion-note slot="end" color="primary">₹{{invoiceNetAmount.toFixed(2)}}</ion-note>
        </ion-item>
      </div>

      <!-- Payment Section -->
      <div class="payment-section">
        <h3>Payment Details</h3>
        <ion-item>
          <ion-label position="stacked">
            {{purchase ? 'Paid': 'Received'}} Amount
          </ion-label>
          <ion-input
            type="number"
            [min]="0"
            [step]="0.01"
            placeholder="Enter amount"
            [(ngModel)]="received_amount"
            (ngModelChange)="modifyReceivedAmount($event)">
          </ion-input>
        </ion-item>

        <ion-item *ngIf="received_amount">
          <ion-label position="stacked">Mode of Payment</ion-label>
          <ion-button
            fill="outline"
            expand="block"
            (click)="selectPaymentMode()">
            {{getSelectedPaymentModeName() || 'Select Payment Mode'}}
            <ion-icon name="chevron-down" slot="end"></ion-icon>
          </ion-button>
        </ion-item>
      </div>

      <!-- Staff Information Section -->
      <div class="staff-section">
        <h3>Staff Information</h3>
        <ion-item>
          <ion-label position="stacked">Billed by</ion-label>
          <ion-input
            type="text"
            placeholder="Enter name of person who billed"
            [(ngModel)]="billedBy">
          </ion-input>
        </ion-item>

        <ion-item>
          <ion-label position="stacked">Delivery by</ion-label>
          <ion-input
            type="text"
            placeholder="Enter name of person who delivered"
            [(ngModel)]="deliveryBy">
          </ion-input>
        </ion-item>

        <ion-item>
          <ion-label position="stacked">Collected by</ion-label>
          <ion-input
            type="text"
            placeholder="Enter name of person who collected payment"
            [(ngModel)]="collectedBy">
          </ion-input>
        </ion-item>
      </div>

      <!-- Final Balance -->
      <div class="final-balance">
        <div class="balance-item final">
          <span>Current Balance</span>
          <span class="final-amount">₹{{closing_balance.toFixed(2)}}</span>
        </div>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>

<!-- Product Creation Modal -->
<app-product-form
  [isOpen]="isProductFormOpen"
  mode="create"
  (modalClosed)="closeProductForm()"
  (productCreated)="onProductCreated($event)">
</app-product-form>