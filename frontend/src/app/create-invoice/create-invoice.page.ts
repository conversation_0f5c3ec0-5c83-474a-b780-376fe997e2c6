import { BrowserModule } from "@angular/platform-browser";
import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  ViewChildren,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy
} from "@angular/core";
import { AlertController, IonInput, Platform } from "@ionic/angular";
import { HttpClient } from "@angular/common/http";
import { ActivatedRoute } from "@angular/router";
import { AlertService } from "../shared/services/alert.service";
import { IonLoaderService } from "../shared/services/ion-loader.service";
import { RouteService } from "../shared/services/route.service";
import { SalesInvoiceService } from "../shared/services/sales-invoice.service";
import { ToastService } from "../shared/services/toast.service";
import * as moment from "moment";
import { PurchaseInvoiceService } from "../shared/services/purchase-invoice.service";
import { ReportsService } from "../shared/services/report.service";
import { RoundingService, RoundingConfig } from "../shared/services/rounding.service";
import { TranslationService } from "../shared/services/translation.service";
import { BuyerService } from "../shared/services/buyer.service";

@Component({
  selector: "app-create-invoice",
  templateUrl: "./create-invoice.page.html",
  styleUrls: ["./create-invoice.page.scss"],
})
export class CreateInvoicePage implements OnInit {
  @ViewChildren("inputCrates") inputCrates: QueryList<ElementRef>;
  @ViewChildren("weightInput") weightInput;
  @ViewChild("popover") popover;
  data: any;
  date: any;
  // date = moment().format("YYYY-MM-DD");
  selectedBuyer = null;
  selectedSupplier = null;
  product_data: any;
  buyer_data = [];
  supplier_data = [];
  isRateOpen = false;
  isWeightOpen = false;
  isNoOpen = false;
  isProductDetailsOpen = false;
  selectedIndex: any;
  totalAmount: Number = 0;
  totalNo: Number = 0;
  totalWeight: Number = 0;
  received_amount: Number = 0;
  current_balance: Number = 0;
  closing_balance: Number = 0;
  agentLastSalesInvoiceRecord: any;
  filterEmpty: boolean = false;
  displayProducts: any;
  changeRateRequestFlag: boolean;
  message: string;

  // Product creation modal properties
  isProductFormOpen: boolean = false;
  lastClickTime: number = 0;
  lock: boolean = true;
  billing_settings: any = JSON.parse(localStorage.getItem('metadata')).billing_settings
  billing_field_settings: any = JSON.parse(localStorage.getItem('metadata')).billing_field_settings.filter(item => item.active).sort((a, b) => a.sort_order - b.sort_order);
  invoice_remarks: string;
  billingAdditionalFields: any = JSON.parse(localStorage.getItem('metadata')).billingAdditionalFields.filter(item => item.active).sort((a, b) => a.sort_order - b.sort_order);
  billingHeaderData: any = JSON.parse(localStorage.getItem('metadata')).billingHeaderData || [];
  billingAdditionalFieldsTotal: Number = 0;
  state = '';
  modeOfPayment: string = '';
  modeOfPayments: any = JSON.parse(localStorage.getItem('metadata')).modeOfPayment;
  dropdownConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a Payment mode',
    customComparator: () => { }, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };

  // Staff information fields
  billedBy: string = '';
  deliveryBy: string = '';
  collectedBy: string = '';
  selectedOption = '';
  options = this.modeOfPayments;
  brand: any;
  selectedBrand: string;
  salesPerson: any;
  selectedSalesPerson: any;
  isPaymentModalOpen = false;
  showMoreActions = false;
  showPaymentDetails = false;
  purchase: boolean = false;

  // Product viewing mode toggle
  isBrandBasedMode: boolean = true; // true = brand-based products, false = all products
  allProductsData: any; // Store all products for toggle functionality
  brandBasedProductsData: any; // Store brand-filtered products

  // Rounding properties
  roundingConfig: RoundingConfig = {
    enabled: true
  };

  // Invoice calculation breakdown
  invoiceSubtotal: number = 0;
  invoiceTaxAmount: number = 0;
  invoiceGrossTotal: number = 0;
  roundingAdjustment: number = 0;
  invoiceNetAmount: number = 0;

  constructor(
    private api: SalesInvoiceService,
    private report_api: ReportsService,
    private purchase_api: PurchaseInvoiceService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public routerService: RouteService,
    public alertController: AlertController,
    private roundingService: RoundingService,
    public translate: TranslationService,
    private http: HttpClient,
    private route: ActivatedRoute,
    private buyerService: BuyerService
  ) {
    this.date = moment().format("YYYY-MM-DD");
    this.state = localStorage.getItem('state');


  }

  ngOnInit() {
    // Initialize component
  }

  ionViewWillEnter() {
    // Check for URL parameters first
    this.handleUrlParameters().then(() => {
      // Then proceed with normal data loading only if no buyer was auto-selected
      if (!this.selectedBuyer) {
        switch (this.state) {
          case 'purchase':
            this.getPurchaseData();
            break;
          default:
            this.getData();
            break;
        }
      }
    });
  }

  async handleUrlParameters() {
    try {
      // Get buyer_id from URL parameters
      const buyerId = this.route.snapshot.queryParamMap.get('buyer_id');
      
      console.log('CreateInvoice: Checking URL parameters, buyer_id:', buyerId);
      
      if (buyerId) {
        console.log('CreateInvoice: Found buyer_id in URL:', buyerId);
        
        // Load buyer data and auto-select
        await this.ionLoaderService.startLoader('Loading buyer data...');
        
        try {
          const response: any = await this.buyerService.getBuyer();
          
          if (response.success) {
            const buyer = response.data.find((b: any) => b.id == buyerId);
            
            if (buyer) {
              console.log('CreateInvoice: Auto-selecting buyer:', buyer);
              // Also load buyer_data for the dropdown
              this.buyer_data = response.data;
              await this.setBuyer(buyer);
            } else {
              console.error('CreateInvoice: Buyer not found with ID:', buyerId);
              this.toast.toastServices('Buyer not found', 'danger', 'top');
            }
          } else {
            console.error('CreateInvoice: Failed to load buyers:', response.message);
            this.toast.toastServices('Failed to load buyer data', 'danger', 'top');
          }
        } catch (error) {
          console.error('CreateInvoice: Error loading buyer data:', error);
          this.toast.toastServices('Error loading buyer data', 'danger', 'top');
        } finally {
          this.ionLoaderService.dismissLoader();
        }
      } else {
        console.log('CreateInvoice: No buyer_id found in URL parameters');
      }
    } catch (error) {
      console.error('CreateInvoice: Error handling URL parameters:', error);
    }
  }
  filterByBrand(ev) {
    console.log(ev);
    this.selectedBrand = ev.detail.value
    if (this.selectedBrand == 'all') {
      this.displayProducts = this.product_data;
      return;
    };
    this.displayProducts = this.product_data.filter((e) => e.brand_name == this.selectedBrand)
  }
  async setBuyer(ev) {
    console.log(ev);
    this.selectedBuyer = ev;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getBuyerSalesInvoice(ev.id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.agentLastSalesInvoiceRecord = res.data;
            if (this.agentLastSalesInvoiceRecord.id) {
              this.current_balance = res.data.current_balance;
              this.modifyTotal();
            }
            
            // Load only brand-filtered products when buyer is selected
            await this.loadProductsByBuyer(ev.id);
            
            // Reset to brand-based mode by default
            this.isBrandBasedMode = true;
            this.selectedBrand = 'all';
            
            console.log(this.displayProducts);
            this.updateMargin()
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  async loadProductsByBuyer(buyerId: number) {
    try {
      // Import the BuyerBrandService dynamically to avoid circular dependencies
      const { BuyerBrandService } = await import('../shared/services/buyer-brand.service');
      const buyerBrandService = new BuyerBrandService(this.http);
      
      const response: any = await buyerBrandService.getProductsByBuyer(buyerId).toPromise();
      
      if (response.success) {
        if (response.data && response.data.products && response.data.products.length > 0) {
          // Store brand-filtered products
          this.brandBasedProductsData = response.data.products;
          this.brand = response.data.brands;
          console.log('Products filtered by buyer brands:', this.brandBasedProductsData);
          console.log('Brands:', this.brand);
          
          // Set initial product data based on current mode
          this.updateProductDataBasedOnMode();
        } else {
          // Fallback to all products if no brands are associated
          console.log('No brands associated with buyer, loading all products');
          await this.loadAllProducts();
        }
      } else {
        console.log('Error loading buyer products, loading all products');
        await this.loadAllProducts();
      }
    } catch (error) {
      console.error('Error loading products by buyer brands:', error);
      await this.loadAllProducts();
    }
  }

  async loadAllProducts() {
    try {
      if (!this.selectedBuyer || !this.selectedBuyer.id) {
        this.toast.toastServices('Please select a buyer first', 'danger', 'top');
        return;
      }
      // Use the service method for API call
      const response: any = await this.buyerService.getAllProductsAndBrandsForBuyer(this.selectedBuyer.id).toPromise();
      if (response.success && response.data) {
        this.allProductsData = response.data.products;
        this.brand = response.data.brands;
        console.log('Loaded all products:', this.allProductsData);
        console.log('Brands:', this.brand);
        // Set initial product data based on current mode
        this.updateProductDataBasedOnMode();
      } else {
        console.error('Failed to load all products');
        this.toast.toastServices('Failed to load products', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error loading all products:', error);
      this.toast.toastServices('Error loading products', 'danger', 'top');
    }
  }

  // Toggle between brand-based and all products modes
  async toggleProductMode() {
    this.isBrandBasedMode = !this.isBrandBasedMode;
    
    if (this.isBrandBasedMode) {
      // Switching to brand-based mode - load brand-filtered products
      if (this.selectedBuyer && this.selectedBuyer.id) {
        await this.loadProductsByBuyer(this.selectedBuyer.id);
      }
    } else {
      // Switching to all products mode - load all products
      if (this.selectedBuyer && this.selectedBuyer.id) {
        await this.loadAllProducts();
      }
    }
    
    // Reset brand filter to 'all' when switching modes
    this.selectedBrand = 'all';
    
    console.log(`Switched to ${this.isBrandBasedMode ? 'brand-based' : 'all'} products mode`);
  }

  // Update product data based on current mode
  updateProductDataBasedOnMode() {
    if (this.isBrandBasedMode) {
      // Use brand-filtered products
      this.product_data = this.brandBasedProductsData || this.allProductsData || [];
    } else {
      // Use all products
      this.product_data = this.allProductsData || this.brandBasedProductsData || [];
    }
    
    // Update display products
    this.displayProducts = this.product_data;
    
    // Update margin calculations
    this.updateMargin();
  }

  async setSupplier(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.purchase_api
        .getInvoiceForSelectedSupplier(data.id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.agentLastSalesInvoiceRecord = res.data;
            if (this.agentLastSalesInvoiceRecord.id) {
              this.current_balance = res.data.current_balance;
              this.modifyTotal();
            }
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async addbillingHeaderData() {

    const options = this.billingHeaderData.map(item => ({
      name: item.slug,
      type: 'text',
      label: item.name,
      placeholder: "enter " + item.name,
    }));
    const alert = await this.alertController.create({
      header: 'Enter Billing Header Data',
      inputs: options,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Ok',
          handler: (mode) => {
            console.log(mode);
            this.billingHeaderData = this.billingHeaderData.map(item => ({
              ...item,
              value: mode[item.slug] || '', // Set the value from the input fields
            }));
          },
        },
      ],
    });

    await alert.present();
  }

  handleClick(e: Event, i) {
    const currentTime = new Date().getTime();
    const elapsedTime = currentTime - this.lastClickTime;
    if (elapsedTime < 300) { // Adjust this value as per your requirement for detecting double click
      this.presentProductDetailsPopover(e, i);
    } else {
      // Perform single click action here
    }
    this.lastClickTime = currentTime;
  }

  presentProductDetailsPopover(e: Event, i) {
    this.popover.event = e;
    this.isProductDetailsOpen = true;
    this.selectedIndex = this.product_data.findIndex((e) => e.id == i);
  }
  changeRateRequest() {
    this.changeRateRequestFlag = true;
  }
  changeRateRequestFunction(value) {
    if (value.password == "Abinesh*1998") {
      this.changeRateRequestFlag = false;
      this.lock = false;
      this.message = null;
      return true;
    } else {
      this.message = "Password not matching"
      return false;
    }

  }
  presentWeightPopover(e: Event, i) {
    this.popover.event = e;
    this.isWeightOpen = true;
    this.selectedIndex = this.product_data.findIndex((e) => e.id == i);
    // this.weightInput["QueryList"].setFocus();
    console.log(this.weightInput);

    // this.weightInput.setFocus();
  }
  modifyRate(data, pd) {
    pd.rate = data;
    pd.total = pd.weight ? pd?.weight * pd?.rate : 0;
    this.getTotal();
  }
  modifyWeight(data, pd) {
    pd.weight = data
    pd.total = (pd.no ? ((pd?.no * pd?.unit_contains) * pd?.rate) : 0) + (pd.weight ? pd?.weight * pd?.rate : 0);
    this.getTotal();
  }
  modifyNo(data, pd) {
    pd.no = data;
    pd.total = (pd.no ? ((pd?.no * pd?.unit_contains) * pd?.rate) : 0) + (pd?.weight ? pd?.weight * pd?.rate : 0);
    this.getTotal();
  }
  modifyDesc(data, pd) {
    pd.desc = data;
  }

  modifyReceivedAmount(amount: number) {
    this.received_amount = amount || 0;
    this.modifyTotal();
  }

  modifyTotal() {
    // Use net amount (after rounding) for balance calculations
    const effectiveTotal = this.invoiceNetAmount;

    switch (this.state) {
      case 'purchase':
        this.closing_balance = +this.current_balance + effectiveTotal - (+this.received_amount + +this.billingAdditionalFieldsTotal)
        break;
      default:
        this.closing_balance = +this.current_balance + +this.billingAdditionalFieldsTotal + effectiveTotal - +this.received_amount;
        break;
    }
  }
  weightButton(data) {
    console.log(this.selectedIndex);
    let weights = data.weights.split("+");
    let pickedData = this.product_data[this.selectedIndex];
    pickedData.no = this.product_data[this.selectedIndex].add ? weights.length : 0;
    pickedData.weights = data.weights;
    console.log(weights);
    let temp = 0;
    weights.forEach((element) => {
      console.log(element);
      temp += Number(element);
      // if (condition) {
      //   pickedData.weight += Number(element);
      // }
      // console.log(pickedData.weight);
    });
    console.log(pickedData.no);

    pickedData.weight = temp;
    pickedData.total = pickedData.rate
      ? pickedData?.weight * pickedData?.rate
      : 0;
    this.getTotal();
    this.isWeightOpen = false;
    this.selectedIndex = null;
  }
  getTotal() {
    (this.totalWeight = 0), (this.totalNo = 0), (this.totalAmount = 0);
    this.product_data.forEach((element) => {
      if (element.weight) {
        this.totalWeight += element.weight;
      }
      if (element.no) {
        this.totalNo += element.no;
      }
      if (element.total) {
        this.totalAmount += element.total;
      }
    });
    this.calculateInvoiceTotals();
    this.modifyTotal();
  }
  numberOnlyValidation(event: any) {
    const pattern = /[0-9]/;
    let inputChar = String.fromCharCode(event.charCode);

    if (!pattern.test(inputChar)) {
      // invalid character, prevent input
      event.preventDefault();
    }
  }
  changeFocusOnCrate(i) {
    let idxNext = i + 1; // Get index by class name
    if (idxNext) {
      let idx = idxNext == this.product_data.length ? 0 : idxNext;
      this.inputCrates.toArray()[idx]["el"].setFocus();
    }
  }
  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .createSalesInvoice()
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");

            this.buyer_data = res.buyer_data;
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  onOptionChange(selected: any) {
    console.log('Selected Option:', selected);
    this.modeOfPayment = selected.value.slug;
  }
  
  async getPurchaseData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.purchase_api
        .createPurchaseInvoice()
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");

            this.product_data = res.product_data.map(product => ({
              ...product,
              rate: product.pr_rate, // Assigning pr_rate to rate
              margin: product.pr_margin // Assigning pr_rate to rate
            }));
            this.brand = res.brand
            this.displayProducts = this.product_data;
            this.supplier_data = res.supplier_data;
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  assignSO() {
    let salesperson: any;
    this.report_api.getSalesPerson().subscribe(async (res: any) => {
      salesperson = res.data
      console.log(res);
      if (res.data.length < 1) {
        this.toast.toastServices("No sales person to be selected", "danger", "bottom")
      }
      this.salesPerson = res.data
      const options = salesperson.map(item => ({
        name: item.first_name,
        type: 'radio',
        label: item.first_name,
        value: item.id,
        checked: false
      }));
      const alert = await this.alertController.create({
        header: 'Select Sales Person',
        inputs: options,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
            cssClass: 'secondary',
            handler: () => {
              console.log('Confirm Cancel');
            },
          },
          {
            text: 'Ok',
            handler: (data) => {
              console.log(data);
              this.selectedSalesPerson = data
            },
          },
        ],
      });

      await alert.present();

    });
  }
  onSearchChange(ev) {
    console.log(ev);

    const val = ev.detail.value;
    if (val.length >= 1) {
      this.displayProducts = this.product_data.filter((item: any) => {
        return item.name.toLowerCase().indexOf(val.toLowerCase()) > -1;
      });
      this.displayProducts.length <= 0 ? (this.filterEmpty = true) : (this.filterEmpty = false);
    } else {
      this.displayProducts = this.product_data;
    }

  }

  // Product creation methods
  openProductForm() {
    this.isProductFormOpen = true;
  }

  // Image upload functionality removed - moved to sales bill page

  closeProductForm() {
    this.isProductFormOpen = false;
  }

  async onProductCreated(newProduct: any) {
    // Refresh the product data to include the newly created product
    if (this.selectedBuyer) {
      await this.setBuyer(this.selectedBuyer);
    } else if (this.selectedSupplier) {
      await this.setSupplier(this.selectedSupplier);
    } else {
      // If no buyer/supplier selected, refresh the general product data
      await this.getPurchaseData();
    }

    this.toast.toastServices('Product created successfully and is now available for selection', 'success', 'top');
  }

  // New methods for improved UI
  openPaymentModal() {
    this.isPaymentModalOpen = true;
  }

  closePaymentModal() {
    this.isPaymentModalOpen = false;
  }

  toggleMoreActions() {
    this.showMoreActions = !this.showMoreActions;
  }

  togglePaymentDetails() {
    this.showPaymentDetails = !this.showPaymentDetails;
  }

  async selectPaymentMode() {
    const options = this.modeOfPayments.map(mode => ({
      name: mode.slug,
      type: 'radio',
      label: mode.name,
      value: mode.slug,
      checked: this.modeOfPayment === mode.slug
    }));

    const alert = await this.alertController.create({
      header: 'Select Payment Mode',
      inputs: options,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'OK',
          handler: (selectedMode) => {
            if (selectedMode) {
              this.modeOfPayment = selectedMode;
            }
          }
        }
      ]
    });

    await alert.present();
  }

  getSelectedPaymentModeName(): string {
    if (!this.modeOfPayment) return '';
    const selectedMode = this.modeOfPayments.find(mode => mode.slug === this.modeOfPayment);
    return selectedMode ? selectedMode.name : '';
  }


  async saveInvoice(status) {
    if (!this.selectedBuyer) {
      this.toast.toastServices("Please select the buyer", "danger", "bottom");
      return;
    }
    if (this.received_amount && !this.modeOfPayment) {
      this.toast.toastServices("Please select the mode of payment", "danger", "top");
      return
    }

    // Validate invoice calculations before saving
    if (!this.validateInvoiceCalculations()) {
      this.toast.toastServices("Invoice calculation error. Please check the invoice totals.", "danger", "bottom");
      return;
    }
    let invoice = {
      buyer_id: this.selectedBuyer.id,
      previous_balance: this.current_balance,
      bill_amount: this.getEffectiveTotal(),
      received_amount: this.received_amount,
      current_balance: this.closing_balance,
      date: moment(this.date).format("YYYY-MM-DD"),
      remarks: this.invoice_remarks,
      headerdata: this.billingHeaderData,
      metadata: this.billingAdditionalFields,
      sales_person_id: this.selectedSalesPerson,
      // Rounding fields
      rounding_enabled: this.roundingConfig.enabled,
      rounding_adjustment: this.roundingAdjustment,
      gross_total: this.invoiceGrossTotal,
      // Staff information fields
      billed_by: this.billedBy,
      delivery_by: this.deliveryBy,
      collected_by: this.collectedBy,
      // Invoice images functionality removed - moved to sales bill page
    };
    let invoice_item = [];
    this.product_data.forEach((element) => {
      if (element.weight > 0 || element.no > 0) {
        invoice_item.push({
          product_id: element.id,
          mrp: element.mrp,
          rate: element.rate,
          weight: element.weight ? element.weight : 0,
          weights: element.weights ? element.weights : 0,
          no: element.no ? element.no : 0,
          line_total: element.total,
          remarks: element.remarks
        });
      }
    });
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .saveSalesInvoice({ invoice: invoice, invoice_item: invoice_item, mode_of_payment: this.modeOfPayment, }, status)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.routerService.navigateBackAndRefresh(this.getReturnUrl());
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async savePurchaseInvoice() {
    if (!this.selectedSupplier) {
      this.toast.toastServices("Please select the supplier", "danger", "bottom");
      return;
    }
    if (this.received_amount && !this.modeOfPayment) {
      this.toast.toastServices("Please select the mode of payment", "danger", "top");
      return
    }
    let invoice = {
      suplier_id: this.selectedSupplier.id,
      previous_balance: this.current_balance,
      bill_amount: +this.totalAmount + +this.billingAdditionalFieldsTotal,
      received_amount: this.received_amount,
      current_balance: this.closing_balance,
      date: moment(this.date).format("YYYY-MM-DD"),
      remarks: this.invoice_remarks,
      headerdata: this.billingHeaderData,
      metadata: this.billingAdditionalFields
    };
    let invoice_item = [];
    this.product_data.forEach((element) => {
      if (element.weight > 0 || element.no > 0) {
        invoice_item.push({
          product_id: element.id,
          rate: element.rate,
          weight: element.weight,
          weights: element.weights,
          no: element.no,
          line_total: element.total,
          remarks: element.remarks
        });
      }
    });
    await this.ionLoaderService.startLoader().then(async () => {
      await this.purchase_api
        .savePurchaseInvoice({ mode_of_payment: this.modeOfPayment, invoice: invoice, invoice_item: invoice_item })
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.routerService.navigateBackAndRefresh(this.getReturnUrl());
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }


  updateMargin() {
    this.product_data.forEach(element => {
      element.rate = element.mrp - (element.mrp * element.final_margin) / 100;
      element.margin = element.final_margin;
    });
  }
  updateProductData(modifiedField: string) {
    let mrp = this.product_data[this.selectedIndex].mrp;
    let rate = this.product_data[this.selectedIndex].rate;
    let margin = this.product_data[this.selectedIndex].margin;
    switch (modifiedField) {
      case 'mrp':
        this.product_data[this.selectedIndex].rate = mrp - (mrp * margin) / 100;
        break;
      case 'rate':
        this.product_data[this.selectedIndex].margin = ((mrp - rate) / mrp) * 100;
        break;
      case 'margin':
        this.product_data[this.selectedIndex].rate = mrp - (mrp * margin) / 100;
        break;
      // case 'remarks':
      //   this.product_data[this.selectedIndex].remarks = mrp - (mrp * margin) / 100;
      //   break;
      default:
        break;
    }
  }
  parseStyle(style: any): { [key: string]: string } {
    if (!style || typeof style !== 'object') {
      return {};
    }
    return Object.keys(style).reduce((acc, key) => {
      acc[key] = style[key];
      return acc;
    }, {});
  }
  async addRemarks() {

    const alert = await this.alertController.create({
      header: 'Enter Remarks',
      inputs: [
        {
          name: 'remarks',
          type: 'textarea',
          placeholder: 'Enter your remarks here...',
          value: this.invoice_remarks,
          attributes: {
            rows: 5, // Optional: Defines the visible height of the textarea
            maxlength: 500, // Optional: Limits the input characters
          },
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          handler: () => {
            console.log('User canceled');
          },
        },
        {
          text: 'Submit',
          handler: (data) => {
            console.log('Remarks:', data.remarks);
            this.invoice_remarks = data.remarks; // Optional: Pass the remarks to another method
          },
        },
      ],
    });

    await alert.present();

  }
  handleDiscountChange(field: any, changedField: string) {
    if (field.fieldType === 'discount') {
      if (changedField === 'percentage') {
        // Calculate amount based on percentage
        field.value = (+this.totalAmount * field.percentage) / 100;
      } else if (changedField === 'value') {
        // Calculate percentage based on amount
        field.percentage = (field.value / +this.totalAmount) * 100;
      }
      this.handleCalculations();
    }
  }
  handleCalculations() {
    this.billingAdditionalFieldsTotal = 0;
    this.billingAdditionalFields.filter(item => item.value).forEach(f => {
      if (f.operation === 'add') {
        this.billingAdditionalFieldsTotal = +this.billingAdditionalFieldsTotal + +f.value || 0;
      } else if (f.operation === 'minus') {
        this.billingAdditionalFieldsTotal = +this.billingAdditionalFieldsTotal - +f.value || 0;
      }
    });
    this.calculateInvoiceTotals();
    this.modifyTotal();
  }

  handleDynamicFieldChange(fieldSlug: string, value: any): void {
    const field = this.billingAdditionalFields.find(f => f.slug === fieldSlug);

    if (field) {
      // Parse the value to a number to ensure calculations work correctly
      const numericValue = parseFloat(value) || 0;

      field.value = numericValue;

      // Recalculate the total amount based on all active fields
      this.handleCalculations();
    }
  }

  // Invoice calculation methods with proper order
  calculateInvoiceTotals() {
    // Step 1: Calculate subtotal from original item amounts
    this.invoiceSubtotal = +this.totalAmount;

    // Step 2: Calculate tax amount (this should be calculated from original subtotal)
    // For now, we'll extract tax from billingAdditionalFieldsTotal
    // In a real implementation, this should be calculated properly from tax rates
    this.invoiceTaxAmount = +this.billingAdditionalFieldsTotal;

    // Step 3: Calculate gross total (subtotal + tax)
    this.invoiceGrossTotal = this.invoiceSubtotal + this.invoiceTaxAmount;

    // Step 4: Apply rounding to gross total and calculate net amount
    const invoiceTotals = this.roundingService.calculateInvoiceTotals(
      this.invoiceSubtotal,
      this.invoiceTaxAmount,
      this.roundingConfig
    );

    this.roundingAdjustment = invoiceTotals.roundingAdjustment;
    this.invoiceNetAmount = invoiceTotals.netAmount;
  }

  onRoundingToggle() {
    this.calculateInvoiceTotals();
    this.modifyTotal();
  }

  formatRoundingAdjustment(): string {
    return this.roundingService.formatRoundingAdjustment(this.roundingAdjustment);
  }

  getEffectiveTotal(): number {
    return this.invoiceNetAmount;
  }

  // Validation method to ensure invoice calculations are correct
  validateInvoiceCalculations(): boolean {
    const expectedGrossTotal = this.invoiceSubtotal + this.invoiceTaxAmount;

    // Validate gross total calculation
    if (Math.abs(this.invoiceGrossTotal - expectedGrossTotal) > 0.01) {
      console.warn('Gross total calculation validation failed:', {
        expected: expectedGrossTotal,
        actual: this.invoiceGrossTotal
      });
      return false;
    }

    // Validate rounding calculation if enabled
    if (this.roundingConfig.enabled) {
      const roundingResult = this.roundingService.applyRoundingToNearest(this.invoiceGrossTotal);
      const expectedNetAmount = roundingResult.roundedAmount;
      const expectedAdjustment = roundingResult.adjustment;

      if (Math.abs(this.invoiceNetAmount - expectedNetAmount) > 0.01 ||
          Math.abs(this.roundingAdjustment - expectedAdjustment) > 0.01) {
        console.warn('Rounding calculation validation failed:', {
          expected: { netAmount: expectedNetAmount, adjustment: expectedAdjustment },
          actual: { netAmount: this.invoiceNetAmount, adjustment: this.roundingAdjustment }
        });
        return false;
      }
    }

    return true;
  }

  save() {
    switch (this.state) {
      case 'purchase':
        this.savePurchaseInvoice();
        break;
      case 'sales-order':
        this.saveInvoice('order');
        break;
      default:
        this.saveInvoice('');
        break;
    }
  }
  getReturnUrl() {
    switch (this.state) {
      case 'purchase':
        return 'purchase-bill';
      case 'sales-order':
        return 'sales-order';
      default:
        return 'tabs/sales-bill';
    }
  }
  ngOnDestroy(): void {
    localStorage.setItem('state', '');
  }
}
