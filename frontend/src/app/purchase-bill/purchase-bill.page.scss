/* Purchase Bill Page Styling */
.purchase-bill-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}

/* Collapsible Summary Section - Now at Top */
.summary-section {
  background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.summary-header:hover {
  background: #f8f9fa;
}

.summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.summary-content.expanded {
  max-height: 500px;
  padding: 16px;
}

/* Collapsible Filter Section */
.filter-section {
  margin: 0 16px 16px 16px;
  background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.filter-header:hover {
  background: #f8f9fa;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.filter-content.expanded {
  max-height: 300px;
  padding: 16px;
}

/* Filter Controls Container */
.filter-controls {
  margin-bottom: 12px;
}

.options-row {
  margin-top: 12px;
}

.date-filter-item {
  --background: var(--item-background);
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 12px;
  --inner-padding-end: 12px;
  --border-radius: 12px;
  --border-color: #e0e0e0;
  --border-style: solid;
  --border-width: 1px;
  margin: 0;
  flex: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.date-filter-item ion-label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-bottom: 4px;
}

.date-input {
  --background: transparent;
  --color: #333;
  --padding-start: 0;
  --padding-end: 0;
  font-size: 14px;
  font-weight: 500;
}

.filter-button {
  --border-radius: 12px;
  --background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  --color: white;
  --box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
  height: 48px;
  min-width: 100px;
  font-size: 14px;
  font-weight: 600;
  text-transform: none;
  margin: 0;
  transition: all 0.3s ease;
}

.filter-button:hover {
  --box-shadow: 0 6px 16px rgba(255, 152, 0, 0.4);
  transform: translateY(-1px);
}

.filter-button ion-icon {
  margin-right: 6px;
}

/* No Data Item */
.no-data-item {
  --background: var(--item-background);
  --padding-start: 16px;
  --padding-end: 16px;
  margin: 16px;
  border-radius: 8px;
}

/* Bill List */
.bill-list {
  padding: 8px 16px;
  margin-bottom: 80px; /* Space for floating menu */
}

/* Bill Item */
.bill-item {
  --background: var(--item-background);
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  --min-height: 120px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bill-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Bill Icon */
.bill-icon {
  width: 60px;
  height: 60px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff3e0;
  border-radius: 12px;
}

.document-icon {
  font-size: 28px;
  color: #ff9800;
}

/* Bill Details */
.bill-details {
  flex: 1;
  margin: 0;
}

.supplier-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.bill-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 6px 0 12px 0;
  font-size: 13px;
}

.bill-id {
  color: #ff9800;
  font-weight: 600;
  background: #fff3e0;
  padding: 3px 8px;
  border-radius: 12px;
}

.bill-date {
  color: #666;
  font-weight: 500;
}

.amount-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.amount-label {
  color: #666;
  font-weight: 500;
}

.amount-value {
  font-weight: 600;
  color: #333;
}

.bill-amount {
  color: #ff9800;
}

.paid-amount {
  color: #4caf50;
}

.balance-positive {
  color: #f44336;
}

.balance-zero {
  color: #666;
}

/* Bill Actions */
.bill-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 50px;
}

.action-button {
  --border-radius: 8px;
  --padding-start: 8px;
  --padding-end: 8px;
  // width: 40px;
  height: 40px;
}

.edit-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.delete-button {
  --color: #f44336;
  --background: #ffebee;
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.bills {
  color: #ff9800;
}

.summary-icon.amount {
  color: #ff9800;
}

.summary-icon.paid {
  color: #28a745;
}

.summary-icon.outstanding {
  color: #dc3545;
}

.bills-card {
  background: linear-gradient(135deg, #fff3e0 0%, #fef8f0 100%);
}

.amount-card {
  background: linear-gradient(135deg, #fff3e0 0%, #fef8f0 100%);
}

.paid-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.outstanding-card {
  background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-section {
    margin: 0 12px 12px 12px;
  }

  .filter-header {
    padding: 12px;
  }

  .filter-content.expanded {
    padding: 12px;
  }

  .bill-list {
    padding: 8px 12px;
  }

  .no-data-item {
    margin: 12px;
  }

  .bill-item {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    --min-height: 100px;
  }

  .bill-icon {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .document-icon {
    font-size: 24px;
  }

  .supplier-name {
    font-size: 16px;
  }

  .bill-meta {
    font-size: 12px;
    gap: 8px;
  }

  .amount-row {
    font-size: 13px;
  }

  .bill-actions {
    gap: 6px;
    min-width: 45px;
  }

  .action-button {
    // width: 36px;
    height: 36px;
  }

  .summary-card-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }

  .summary-section {
    padding: 12px;
  }
}

.bill-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.status-pending {
  color: var(--ion-color-warning);
}

.status-inactive {
  color: var(--ion-color-danger);
}