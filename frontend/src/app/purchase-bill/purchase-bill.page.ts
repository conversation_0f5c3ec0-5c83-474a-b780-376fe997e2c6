import { Component, OnInit } from '@angular/core';
import { NavController, MenuController } from '@ionic/angular';
import * as moment from 'moment';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { PurchaseInvoiceService } from '../shared/services/purchase-invoice.service';
import { RouteService } from '../shared/services/route.service';
import { ToastService } from '../shared/services/toast.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-purchase-bill',
  templateUrl: './purchase-bill.page.html',
  styleUrls: ['./purchase-bill.page.scss'],
})
export class PurchaseBillPage implements OnInit {
  suplier_data: any;
  isModalOpen = false;
  isEditModalOpen = false;
  previous_balance: any;
  supplierData: any;
  date: any;
  // date = moment().format("YYYY-MM-DD");
  lineAccountData: any;
  editData: any;
  selectedSupplier: any;
  previous_balance_total: any;
  bill_amount_total: any;
  receivable_amount_total: any;
  received_amount_total: any;
  current_balance_total: any;
  showFilters: boolean = false;
  showSummary: boolean = false;

  constructor(
    private api: PurchaseInvoiceService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public alertService: AlertService,
    public nav: NavController,
    public routerService: RouteService,
    private menuController: MenuController,
    private location: Location
  ) {
    this.date = moment().format("YYYY-MM-DD");
  }

  ngOnInit() {}
  ionViewWillEnter() {
    this.getData(this.date);
  }
  filterData() {
    this.getData(this.date);
  }

  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  toggleSummary() {
    this.showSummary = !this.showSummary;
  }

  async getData(date) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getPurchaseInvoice(date)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.lineAccountData = res.data;
            this.getTotal();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async getTotal() {
    this.previous_balance_total=0;
    this.bill_amount_total=0;
    this.receivable_amount_total=0;
    this.received_amount_total=0;
    this.current_balance_total = 0;
    this.lineAccountData.forEach((element) => {
      this.previous_balance_total += element.previous_balance;
      this.bill_amount_total += element.bill_amount;
      this.receivable_amount_total += element.receivable_amount;
      this.received_amount_total += element.received_amount;
      this.current_balance_total += element.current_balance;
    });
    
  }

  createPurchaseInvoice() {
    localStorage.setItem('state',"purchase");
    this.nav.navigateForward(`/create-invoice`);
  }
  editPurchaseInvoice(id: number) {
    localStorage.setItem('state',"purchase");
    this.nav.navigateForward(`/edit-invoice?id=${id}`);
  }
  delete(data) {
    this.alertService
      .alertConfirm(
        "Alert",
        "Are you sure you want to delete the Purchase Invoice !!!",
        "yes",
        "no"
      )
      .then((res) => {
        if (res) {
          this.deletePurchaseInvoice(data);
        }
      });
  }
  async deletePurchaseInvoice(id) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .deletePurchaseInvoice(id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.getData(this.date);
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, "danger", "top");
          this.ionLoaderService.dismissLoader();
        });
    });
  }

  // Navigation methods
  toggleMenu() {
    this.menuController.toggle();
  }

  goBack() {
    this.location.back();
  }
}
