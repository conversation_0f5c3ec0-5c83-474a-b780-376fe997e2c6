import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { PurchaseBillPageRoutingModule } from './purchase-bill-routing.module';

import { PurchaseBillPage } from './purchase-bill.page';
import { SharedModule } from '../shared/modules/shared/shared.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    PurchaseBillPageRoutingModule,
    SharedModule
  ],
  declarations: [PurchaseBillPage]
})
export class PurchaseBillPageModule {}
