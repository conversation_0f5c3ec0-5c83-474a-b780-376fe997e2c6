<app-header [title]="'Purchase Bills'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="purchase-bill-content">
  <!-- Collapsible Summary Section - Moved to Top -->
  <div class="summary-section" *ngIf="lineAccountData && lineAccountData.length > 0">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        Daily Summary
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card bills-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Total Bills</h3>
                <h1>{{lineAccountData.length}}</h1>
                <p>Purchase bills processed</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="document-text" class="summary-icon bills"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card amount-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Bill Amount</h3>
                <h1>{{bill_amount_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Total purchase amount</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="card" class="summary-icon amount"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card paid-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Paid</h3>
                <h1>{{received_amount_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Amount paid</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="checkmark-done" class="summary-icon paid"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card outstanding-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Outstanding</h3>
                <h1>{{current_balance_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Pending payment</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="hourglass" class="summary-icon outstanding"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    </div>
  </div>

  <!-- Collapsible Filter Section -->
  <div class="filter-section">
    <div class="filter-header" (click)="toggleFilters()">
      <h3 class="section-title">
        <ion-icon name="filter-outline" class="section-icon"></ion-icon>
        Filter & Options
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showFilters ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="filter-content" [class.expanded]="showFilters">
      <!-- Filter Controls -->
      <div class="filter-controls">
        <ion-row>
          <ion-col size="8">
            <ion-item class="date-filter-item" lines="none">
              <ion-label position="stacked">Select Date</ion-label>
              <ion-input
                [(ngModel)]="date"
                [value]="date | date : 'YYYY-MM-dd'"
                placeholder="Enter Date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>

          <ion-col size="4">
            <ion-button
              fill="solid"
              expand="block"
              class="filter-button"
              (click)="filterData()">
              <ion-icon name="search" slot="start"></ion-icon>
              Filter
            </ion-button>
          </ion-col>
        </ion-row>
      </div>

      <!-- Options Row -->
      <div class="options-row">
        <ion-row>
          <ion-col size="12">
            <ion-button
              fill="outline"
              expand="block"
              class="action-button"
              (click)="createPurchaseInvoice()">
              <ion-icon name="add-circle-outline" slot="start"></ion-icon>
              New Purchase Bill
            </ion-button>
          </ion-col>
        </ion-row>
      </div>
    </div>
  </div>
  <!-- No Data Message -->
  <ion-item lines="none" *ngIf="!lineAccountData || lineAccountData.length === 0" class="no-data-item">
    <ion-text color="medium">
      <h6>No purchase bills found for the selected date.</h6>
    </ion-text>
  </ion-item>

  <!-- Purchase Bill List -->
  <div class="bill-list">
    <ion-item
      *ngFor="let bill of lineAccountData"
      class="bill-item"
      lines="none"
      (click)="editPurchaseInvoice(bill.id)">

      <!-- Bill Icon -->
      <div class="bill-icon" slot="start">
        <ion-icon name="document-text-outline" class="document-icon"></ion-icon>
      </div>

      <!-- Bill Details -->
      <ion-label class="bill-details">
        <h2 class="supplier-name">{{bill.name}}</h2>

        <!-- Bill ID and Date -->
        <div class="bill-meta">
          <span class="bill-id">PUR-{{bill.id}}</span>
          <span class="bill-date">{{bill.date | date:'dd/MM/yyyy'}}</span>
        </div>

        <!-- Amount Details -->
        <div class="amount-details">
          <div class="amount-row">
            <span class="amount-label">Bill Amount:</span>
            <span class="amount-value bill-amount">{{bill.bill_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
          </div>
          <div class="amount-row">
            <span class="amount-label">Paid:</span>
            <span class="amount-value paid-amount">{{bill.received_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
          </div>
          <div class="amount-row">
            <span class="amount-label">Balance:</span>
            <span class="amount-value" [ngClass]="{'balance-positive': bill.current_balance > 0, 'balance-zero': bill.current_balance === 0}">
              {{bill.current_balance | currency: 'INR':'symbol':'1.0-0'}}
            </span>
          </div>
        </div>
      </ion-label>

      <!-- Actions -->
      <div class="bill-actions" slot="end">
        <ion-button
          fill="clear"
          size="small"
          class="action-button edit-button"
          (click)="editPurchaseInvoice(bill.id); $event.stopPropagation()">
          <ion-icon name="create-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button delete-button"
          (click)="delete(bill.id); $event.stopPropagation()">
          <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
        </ion-button>
      </div>
    </ion-item>
  </div>
</ion-content>

<ion-footer>
  <app-floating-menu></app-floating-menu>
</ion-footer>