import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SalesPaymentPageRoutingModule } from './sales-payment-routing.module';

import { SalesPaymentPage } from './sales-payment.page';
import { SharedModule } from '../shared/modules/shared/shared.module';
import { IonicSelectableModule } from 'ionic-selectable';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SalesPaymentPageRoutingModule,
    SharedModule,
    IonicSelectableModule,

  ],
  declarations: [SalesPaymentPage]
})
export class SalesPaymentPageModule {}
