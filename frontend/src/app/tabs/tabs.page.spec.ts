import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { RouterTestingModule } from '@angular/router/testing';

import { TabsPage } from './tabs.page';

describe('TabsPage', () => {
  let component: TabsPage;
  let fixture: ComponentFixture<TabsPage>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ TabsPage ],
      imports: [IonicModule.forRoot(), RouterTestingModule]
    }).compileComponents();

    fixture = TestBed.createComponent(TabsPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have 5 tab buttons', () => {
    const compiled = fixture.nativeElement;
    const tabButtons = compiled.querySelectorAll('ion-tab-button');
    expect(tabButtons.length).toBe(5);
  });

  it('should have correct tab labels', () => {
    const compiled = fixture.nativeElement;
    const tabLabels = compiled.querySelectorAll('ion-label');
    expect(tabLabels[0].textContent).toContain('Home');
    expect(tabLabels[1].textContent).toContain('Product');
    expect(tabLabels[2].textContent).toContain('Sales Bill');
    expect(tabLabels[3].textContent).toContain('Report');
    expect(tabLabels[4].textContent).toContain('Settings');
  });
});
