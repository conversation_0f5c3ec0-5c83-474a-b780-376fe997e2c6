/* Custom Tab Bar Styling */
.custom-tab-bar {
  --background: var(--ion-color-light, #f4f5f8);
  --border: 1px solid var(--ion-color-medium, #92949c);
  height: 65px;
  padding: 8px 0;
}

/* Custom Tab Button Styling */
.custom-tab-button {
  --color: var(--ion-color-medium, #92949c);
  --color-selected: var(--ion-color-primary, #3880ff);
  --background: transparent;
  --background-focused: var(--ion-color-light-shade, #e8e9ed);
  --background-focused-opacity: 0.12;
  
  /* Smooth transitions */
  transition: all 300ms ease-in-out;
  border-radius: 8px;
  margin: 0 4px;
  position: relative;
  overflow: hidden;
}

/* Hover effects for desktop */
@media (hover: hover) {
  .custom-tab-button:hover {
    --background: var(--ion-color-light-shade, #e8e9ed);
    --color: var(--ion-color-primary-shade, #3171e0);
    transform: translateY(-2px);
  }
}

/* Active/Selected tab styling */
.custom-tab-button.tab-selected {
  --color: var(--ion-color-primary, #3880ff);
  --background: var(--ion-color-primary-tint, #4c8dff);
  --background-focused: var(--ion-color-primary-shade, #3171e0);
  font-weight: 600;
}

/* Active tab bottom border indicator */
.custom-tab-button.tab-selected::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: var(--ion-color-primary, #3880ff);
  border-radius: 2px 2px 0 0;
}

/* Tab icon styling */
.custom-tab-button ion-icon {
  font-size: 22px;
  margin-bottom: 2px;
  transition: transform 300ms ease-in-out;
}

.custom-tab-button.tab-selected ion-icon {
  transform: scale(1.1);
}

/* Tab label styling */
.custom-tab-button ion-label {
  font-size: 12px;
  font-weight: 500;
  margin-top: 2px;
  transition: font-weight 300ms ease-in-out;
}

.custom-tab-button.tab-selected ion-label {
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 320px) {
  .custom-tab-button ion-label {
    font-size: 10px;
  }
  
  .custom-tab-button ion-icon {
    font-size: 20px;
  }
}

@media (min-width: 768px) {
  .custom-tab-bar {
    height: 70px;
    padding: 10px 0;
  }
  
  .custom-tab-button {
    margin: 0 8px;
  }
  
  .custom-tab-button ion-icon {
    font-size: 24px;
  }
  
  .custom-tab-button ion-label {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  .custom-tab-bar {
    height: 75px;
    padding: 12px 0;
  }
  
  .custom-tab-button {
    margin: 0 12px;
    border-radius: 12px;
  }
  
  .custom-tab-button ion-icon {
    font-size: 26px;
  }
  
  .custom-tab-button ion-label {
    font-size: 15px;
  }
}

/* Accessibility improvements */
.custom-tab-button:focus {
  outline: 2px solid var(--ion-color-primary, #3880ff);
  outline-offset: 2px;
}

/* Animation for tab switching */
@keyframes tabSwitch {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.custom-tab-button.tab-selected {
  animation: tabSwitch 300ms ease-out;
}
