<app-header [title]="'Purchase Orders'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="purchase-order-content">
  <!-- View Toggle Tabs -->
  <div class="view-toggle-section">
    <ion-segment [(ngModel)]="currentView" (ionChange)="switchView(currentView)">
      <ion-segment-button value="orders">
        <ion-label>Purchase Orders</ion-label>
      </ion-segment-button>
      <ion-segment-button value="drafts">
        <ion-label>Drafts</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <!-- Summary Section -->
  <div class="summary-section" *ngIf="currentView === 'orders' && purchaseOrders.length > 0">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        Purchase Order Summary
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">
      <ion-row>
        <ion-col size="6">
          <ion-card class="summary-card total-card">
            <ion-card-content>
              <div class="summary-card-content">
                <div class="summary-info">
                  <h3>Total POs</h3>
                  <h1>{{summary.total_pos}}</h1>
                  <p>Purchase orders</p>
                </div>
                <div class="summary-illustration">
                  <ion-icon name="document-text" class="summary-icon total"></ion-icon>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>
        <ion-col size="6">
          <ion-card class="summary-card approved-card">
            <ion-card-content>
              <div class="summary-card-content">
                <div class="summary-info">
                  <h3>Approved</h3>
                  <h1>{{summary.approved_count}}</h1>
                  <p>Ready for processing</p>
                </div>
                <div class="summary-illustration">
                  <ion-icon name="checkmark-circle" class="summary-icon approved"></ion-icon>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col size="6">
          <ion-card class="summary-card pending-card">
            <ion-card-content>
              <div class="summary-card-content">
                <div class="summary-info">
                  <h3>Pending</h3>
                  <h1>{{summary.pending_approval_count}}</h1>
                  <p>Awaiting approval</p>
                </div>
                <div class="summary-illustration">
                  <ion-icon name="time" class="summary-icon pending"></ion-icon>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>
        <ion-col size="6">
          <ion-card class="summary-card draft-card">
            <ion-card-content>
              <div class="summary-card-content">
                <div class="summary-info">
                  <h3>Drafts</h3>
                  <h1>{{summary.draft_count}}</h1>
                  <p>In preparation</p>
                </div>
                <div class="summary-illustration">
                  <ion-icon name="create" class="summary-icon draft"></ion-icon>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section" *ngIf="currentView === 'orders'">
    <div class="filters-header" (click)="toggleFilters()">
      <h3 class="section-title">
        <ion-icon name="filter-outline" class="section-icon"></ion-icon>
        Filters
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showFilters ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="filters-content" [class.expanded]="showFilters">
      <ion-row>
        <ion-col size="12" size-md="4">
          <ion-item>
            <ion-label position="stacked">Status</ion-label>
            <ion-select [(ngModel)]="selectedStatus" placeholder="All Status">
              <ion-select-option *ngFor="let status of statusOptions" [value]="status.value">
                {{status.label}}
              </ion-select-option>
            </ion-select>
          </ion-item>
        </ion-col>
        <ion-col size="12" size-md="4">
          <ion-item>
            <ion-label position="stacked">Auto Generated</ion-label>
            <ion-select [(ngModel)]="autoGeneratedFilter" placeholder="All">
              <ion-select-option value="">All</ion-select-option>
              <ion-select-option value="true">Auto Generated</ion-select-option>
              <ion-select-option value="false">Manual</ion-select-option>
            </ion-select>
          </ion-item>
        </ion-col>
        <ion-col size="12" size-md="4">
          <div class="filter-actions">
            <ion-button expand="block" (click)="filterData()">
              <ion-icon name="search" slot="start"></ion-icon>
              Apply Filters
            </ion-button>
            <ion-button expand="block" fill="outline" (click)="clearFilters()">
              <ion-icon name="refresh" slot="start"></ion-icon>
              Clear
            </ion-button>
          </div>
        </ion-col>
      </ion-row>
    </div>
  </div>

  <!-- Purchase Orders List -->
  <div *ngIf="currentView === 'orders'">
    <!-- No Data Message -->
    <div class="no-data-section" *ngIf="!purchaseOrders || purchaseOrders.length === 0">
      <ion-card class="no-data-card">
        <ion-card-content>
          <div class="no-data-content">
            <ion-icon name="document-text-outline" class="no-data-icon"></ion-icon>
            <h3>No Purchase Orders Found</h3>
            <p>Create your first purchase order to get started.</p>
            <ion-button (click)="createPurchaseOrder()">
              <ion-icon name="add" slot="start"></ion-icon>
              Create Purchase Order
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Purchase Orders List -->
    <div class="po-list" *ngIf="purchaseOrders && purchaseOrders.length > 0">
      <ion-item
        *ngFor="let po of purchaseOrders"
        class="po-item"
        lines="none"
        (click)="viewPurchaseOrder(po.id!)">

        <!-- PO Icon -->
        <div class="po-icon" slot="start">
          <ion-icon name="document-text-outline" class="document-icon"></ion-icon>
        </div>

        <!-- PO Details -->
        <ion-label class="po-details">
          <h2 class="supplier-name">{{po.supplier_name}}</h2>

          <!-- PO Number and Date -->
          <div class="po-meta">
            <span class="po-number">{{po.po_number}}</span>
            <span class="po-date">{{formatDate(po.created_date!)}}</span>
          </div>

          <!-- Status and Amount -->
          <div class="po-status-amount">
            <ion-chip [color]="getStatusColor(po.status)" class="status-chip">
              {{getStatusLabel(po.status)}}
            </ion-chip>
            <span class="po-amount">{{formatCurrency(po.net_amount)}}</span>
          </div>

          <!-- Additional Info -->
          <div class="po-additional-info">
            <span class="items-count">{{po.items_count}} items</span>
            <span class="delivery-date" *ngIf="po.expected_delivery_date">
              <ion-icon name="calendar-outline" class="delivery-icon"></ion-icon>
              Delivery: {{formatDate(po.expected_delivery_date)}}
            </span>
            <span class="auto-generated" *ngIf="po.auto_generated">
              <ion-icon name="cog" class="auto-icon"></ion-icon>
              Auto Generated
            </span>
          </div>

          <!-- Order Details (Remarks/Terms) -->
          <div class="po-order-details" *ngIf="po.remarks || po.terms_and_conditions">
            <div class="remarks" *ngIf="po.remarks">
              <ion-icon name="chatbox-outline" class="remarks-icon"></ion-icon>
              <span class="remarks-text">{{po.remarks}}</span>
            </div>
            <div class="terms" *ngIf="po.terms_and_conditions">
              <ion-icon name="document-text-outline" class="terms-icon"></ion-icon>
              <span class="terms-text">{{po.terms_and_conditions}}</span>
            </div>
          </div>
        </ion-label>

        <!-- Action Buttons -->
        <div class="po-actions" slot="end">
          <ion-button
            fill="clear"
            size="small"
            (click)="$event.stopPropagation(); editPurchaseOrder(po.id!)"
            *ngIf="po.status === 'draft'">
            <ion-icon name="create" slot="icon-only"></ion-icon>
          </ion-button>

          <ion-button
            fill="clear"
            size="small"
            color="success"
            (click)="$event.stopPropagation(); approvePurchaseOrder(po)"
            *ngIf="po.status === 'pending_approval'">
            <ion-icon name="checkmark-circle" slot="icon-only"></ion-icon>
          </ion-button>

          <ion-button
            fill="clear"
            size="small"
            color="danger"
            (click)="$event.stopPropagation(); deletePurchaseOrder(po)"
            *ngIf="po.status === 'draft' || po.status === 'cancelled'">
            <ion-icon name="trash" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </ion-item>
    </div>
  </div>

  <!-- Purchase Order Drafts List -->
  <div *ngIf="currentView === 'drafts'">
    <!-- No Data Message -->
    <div class="no-data-section" *ngIf="!purchaseOrderDrafts || purchaseOrderDrafts.length === 0">
      <ion-card class="no-data-card">
        <ion-card-content>
          <div class="no-data-content">
            <ion-icon name="create-outline" class="no-data-icon"></ion-icon>
            <h3>No Drafts Found</h3>
            <p>Auto-generated drafts will appear here when inventory is low.</p>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Drafts List -->
    <div class="draft-list" *ngIf="purchaseOrderDrafts && purchaseOrderDrafts.length > 0">
      <ion-item
        *ngFor="let draft of purchaseOrderDrafts"
        class="draft-item"
        lines="none"
        (click)="editDraft(draft.id!)">

        <!-- Draft Icon -->
        <div class="draft-icon" slot="start">
          <ion-icon name="create-outline" class="document-icon"></ion-icon>
        </div>

        <!-- Draft Details -->
        <ion-label class="draft-details">
          <h2 class="supplier-name">{{draft.supplier_name}}</h2>

          <!-- Draft Date -->
          <div class="draft-meta">
            <span class="draft-date">{{formatDate(draft.created_on!)}}</span>
            <span class="forecast-based" *ngIf="draft.forecast_based">
              <ion-icon name="trending-up" class="forecast-icon"></ion-icon>
              Forecast Based
            </span>
          </div>

          <!-- Amount and Items -->
          <div class="draft-amount-items">
            <span class="draft-amount">{{formatCurrency(draft.total_estimated_amount)}}</span>
            <span class="items-count">{{draft.items_count}} items</span>
          </div>

          <!-- Auto Generated Badge -->
          <div class="draft-badges" *ngIf="draft.auto_generated">
            <ion-chip color="primary" class="auto-chip">
              <ion-icon name="cog" slot="start"></ion-icon>
              Auto Generated
            </ion-chip>
          </div>
        </ion-label>

        <!-- Action Buttons -->
        <div class="draft-actions" slot="end">
          <ion-button
            fill="clear"
            size="small"
            color="success"
            (click)="$event.stopPropagation(); convertDraftToPO(draft)">
            <ion-icon name="arrow-forward-circle" slot="icon-only"></ion-icon>
          </ion-button>

          <ion-button
            fill="clear"
            size="small"
            (click)="$event.stopPropagation(); editDraft(draft.id!)">
            <ion-icon name="create" slot="icon-only"></ion-icon>
          </ion-button>

          <ion-button
            fill="clear"
            size="small"
            color="danger"
            (click)="$event.stopPropagation(); deleteDraft(draft)">
            <ion-icon name="trash" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </ion-item>
    </div>
  </div>
</ion-content>

<ion-footer>
  <ion-fab vertical="bottom" horizontal="start" *ngIf="currentView === 'orders'">
    <ion-fab-button (click)="createPurchaseOrder()">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>
