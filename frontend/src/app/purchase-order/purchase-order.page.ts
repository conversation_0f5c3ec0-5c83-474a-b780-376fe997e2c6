import { Component, OnInit } from '@angular/core';
import { NavController, AlertController, LoadingController } from '@ionic/angular';
import { PurchaseOrderService, PurchaseOrder, PurchaseOrderDraft } from '../shared/services/purchase-order.service';
import { ToastService } from '../shared/services/toast.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';

@Component({
  selector: 'app-purchase-order',
  templateUrl: './purchase-order.page.html',
  styleUrls: ['./purchase-order.page.scss'],
})
export class PurchaseOrderPage implements OnInit {
  purchaseOrders: PurchaseOrder[] = [];
  purchaseOrderDrafts: PurchaseOrderDraft[] = [];

  // Filter options
  selectedStatus = '';
  selectedSupplier = '';
  autoGeneratedFilter = '';

  // View options
  currentView = 'orders'; // 'orders' or 'drafts'
  showFilters = false;
  showSummary = false;

  // Summary data
  summary = {
    total_pos: 0,
    draft_count: 0,
    approved_count: 0,
    pending_approval_count: 0
  };

  // Status options for filter
  statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'draft', label: 'Draft' },
    { value: 'pending_approval', label: 'Pending Approval' },
    { value: 'approved', label: 'Approved' },
    { value: 'sent_to_supplier', label: 'Sent to Supplier' },
    { value: 'partially_received', label: 'Partially Received' },
    { value: 'fully_received', label: 'Fully Received' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  constructor(
    private purchaseOrderService: PurchaseOrderService,
    private nav: NavController,
    private alertController: AlertController,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService
  ) { }

  ngOnInit() {
    this.loadData();
  }

  ionViewWillEnter() {
    this.loadData();
  }

  async loadData() {
    await this.ionLoaderService.startLoader();

    try {
      if (this.currentView === 'orders') {
        await this.loadPurchaseOrders();
      } else {
        await this.loadPurchaseOrderDrafts();
      }
    } catch (error) {
      console.error('Error in loadData:', error);
      this.toast.toastServices('Error loading data', 'danger', 'top');
    } finally {
      // Ensure loader is always dismissed
      await this.ionLoaderService.dismissLoader();
    }
  }

  async loadPurchaseOrders() {
    try {
      const filters = {
        status: this.selectedStatus || undefined,
        auto_generated: this.autoGeneratedFilter ? this.autoGeneratedFilter === 'true' : undefined
      };

      const response: any = await this.purchaseOrderService.getPurchaseOrders(filters).toPromise();

      if (response && response.success) {
        this.purchaseOrders = response.data || [];
        this.summary = response.summary || this.summary;
        this.toast.toastServices(response.message || 'Purchase orders loaded successfully', 'success', 'top');
      } else {
        this.toast.toastServices(response?.message || 'Failed to load purchase orders', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error loading purchase orders:', error);
      this.toast.toastServices('Error loading purchase orders', 'danger', 'top');
      this.purchaseOrders = [];
    }
  }

  async loadPurchaseOrderDrafts() {
    try {
      const response: any = await this.purchaseOrderService.getPurchaseOrderDrafts().toPromise();

      if (response && response.success) {
        this.purchaseOrderDrafts = response.data || [];
        this.toast.toastServices(response.message || 'Purchase order drafts loaded successfully', 'success', 'top');
      } else {
        this.toast.toastServices(response?.message || 'Failed to load purchase order drafts', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error loading purchase order drafts:', error);
      this.toast.toastServices('Error loading purchase order drafts', 'danger', 'top');
      this.purchaseOrderDrafts = [];
    }
  }

  // Navigation methods
  createPurchaseOrder() {
    this.nav.navigateForward('/create-purchase-order');
  }

  editPurchaseOrder(poId: number) {
    this.nav.navigateForward(`/create-purchase-order?id=${poId}`);
  }

  viewPurchaseOrder(poId: number) {
    this.nav.navigateForward(`/create-purchase-order?id=${poId}&mode=view`);
  }

  editDraft(draftId: number) {
    this.nav.navigateForward(`/create-purchase-order?draft_id=${draftId}`);
  }

  // Filter methods
  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  toggleSummary() {
    this.showSummary = !this.showSummary;
  }

  filterData() {
    this.loadData();
  }

  clearFilters() {
    this.selectedStatus = '';
    this.selectedSupplier = '';
    this.autoGeneratedFilter = '';
    this.loadData();
  }

  // View switching
  switchView(view: string) {
    this.currentView = view;
    this.loadData();
  }

  // Utility methods
  getStatusColor(status: string): string {
    return this.purchaseOrderService.getStatusColor(status);
  }

  getStatusLabel(status: string): string {
    return this.purchaseOrderService.getStatusLabel(status);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount || 0);
  }

  // Action methods
  async approvePurchaseOrder(po: PurchaseOrder) {
    const alert = await this.alertController.create({
      header: 'Approve Purchase Order',
      message: `Are you sure you want to approve PO ${po.po_number}?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Approve',
          handler: async () => {
            await this.ionLoaderService.startLoader();

            try {
              const response: any = await this.purchaseOrderService.approvePurchaseOrder(po.id!).toPromise();

              if (response.success) {
                this.toast.toastServices('Purchase Order approved successfully', 'success', 'top');
                this.loadData();
              } else {
                this.toast.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              this.toast.toastServices('Error approving purchase order', 'danger', 'top');
            } finally {
              await this.ionLoaderService.dismissLoader();
            }
          }
        }
      ]
    });

    await alert.present();
  }

  async convertDraftToPO(draft: PurchaseOrderDraft) {
    const alert = await this.alertController.create({
      header: 'Convert Draft to Purchase Order',
      message: `Convert draft for ${draft.supplier_name} to Purchase Order?`,
      inputs: [
        {
          name: 'expectedDeliveryDate',
          type: 'date',
          placeholder: 'Expected Delivery Date (Optional)'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Convert',
          handler: async (data) => {
            await this.ionLoaderService.startLoader();

            try {
              const response: any = await this.purchaseOrderService.convertDraftToPurchaseOrder(
                draft.id!,
                data.expectedDeliveryDate
              ).toPromise();

              if (response.success) {
                this.toast.toastServices(response.data.message, 'success', 'top');
                this.loadData();
              } else {
                this.toast.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              this.toast.toastServices('Error converting draft', 'danger', 'top');
            } finally {
              await this.ionLoaderService.dismissLoader();
            }
          }
        }
      ]
    });

    await alert.present();
  }

  async deletePurchaseOrder(po: PurchaseOrder) {
    const alert = await this.alertController.create({
      header: 'Delete Purchase Order',
      message: `Are you sure you want to delete PO ${po.po_number}? This action cannot be undone.`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          cssClass: 'danger',
          handler: async () => {
            await this.ionLoaderService.startLoader();

            try {
              const response: any = await this.purchaseOrderService.deletePurchaseOrder(po.id!).toPromise();

              if (response.success) {
                this.toast.toastServices('Purchase Order deleted successfully', 'success', 'top');
                this.loadData();
              } else {
                this.toast.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              this.toast.toastServices('Error deleting purchase order', 'danger', 'top');
            } finally {
              await this.ionLoaderService.dismissLoader();
            }
          }
        }
      ]
    });

    await alert.present();
  }

  async deleteDraft(draft: PurchaseOrderDraft) {
    const alert = await this.alertController.create({
      header: 'Delete Draft',
      message: `Are you sure you want to delete this draft for ${draft.supplier_name}?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          cssClass: 'danger',
          handler: async () => {
            await this.ionLoaderService.startLoader();

            try {
              const response: any = await this.purchaseOrderService.deletePurchaseOrderDraft(draft.id!).toPromise();

              if (response.success) {
                this.toast.toastServices('Draft deleted successfully', 'success', 'top');
                this.loadData();
              } else {
                this.toast.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              this.toast.toastServices('Error deleting draft', 'danger', 'top');
            } finally {
              await this.ionLoaderService.dismissLoader();
            }
          }
        }
      ]
    });

    await alert.present();
  }
}
