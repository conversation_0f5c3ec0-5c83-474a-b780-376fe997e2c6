import { Injectable } from '@angular/core';

import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TallyService {

  constructor(private http: HttpClient) { }
  
  getTally(from_date,to_date) {
    return this.http
      .get(`${environment.apiUrl}/tally/?from_date=${from_date}&to_date=${to_date}`)
      .toPromise();
  }
}
