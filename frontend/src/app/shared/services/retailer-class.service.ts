import { Injectable } from '@angular/core';


import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: 'root'
})
export class RetailerClassService {



  constructor(private http: HttpClient) { }

  getBuyerClass() {
    return this.http.get(`${environment.apiUrl}/buyer_class/`).toPromise();
  }

  postBuyerClass(data) {
    return this.http.post(`${environment.apiUrl}/buyer_class/`, data).toPromise();
  }
  putBuyerClass(data) {
    return this.http.put(`${environment.apiUrl}/buyer_class/`, data).toPromise();
  }
  deleteBuyerClass(buyer_class_id) {
    return this.http.delete(`${environment.apiUrl}/buyer_class/?buyer_class_id=${buyer_class_id}`).toPromise();
  }
  getBuyerClassMargin(id) {
    return this.http.get(`${environment.apiUrl}/buyer_class_margin/?buyer_class_id=${id}`).toPromise();

  }
  postBuyerClassMargin(buyer_class_id, data) {
    return this.http.post(`${environment.apiUrl}/buyer_class_margin/?buyer_class_id=${buyer_class_id}`, data).toPromise();
  }
}
