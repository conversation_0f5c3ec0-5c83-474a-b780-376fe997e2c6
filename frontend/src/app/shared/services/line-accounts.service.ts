import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class LineAccountsService {
  constructor(private http: HttpClient) {}
  getLineAccountSupplierData() {
    return this.http
      .get(`${environment.apiUrl}/line_account_data/`)
      .toPromise();
  }
  getInvoiceForSelectedSupplier(id) {
    return this.http
      .get(`${environment.apiUrl}/line_account_data/?purchase_bill=${id}`)
      .toPromise();
  }
  savePurchaseInvoice(data) {
    return this.http
      .post(`${environment.apiUrl}/line_account_data/`, data)
      .toPromise();
  }
  editPurchaseInvoice(data) {
    return this.http
      .put(`${environment.apiUrl}/line_account_data/`, data)
      .toPromise();
  }
  deletePurchaseInvoice(data) {
    return this.http
      .delete(`${environment.apiUrl}/line_account_data/?purchase_invoice_id=${data}`)
      .toPromise();
  }
  getPurchaseInvoice(date){
    return this.http
      .get(`${environment.apiUrl}/line_account_data/?list=${date}`)
      .toPromise();
  }
}
