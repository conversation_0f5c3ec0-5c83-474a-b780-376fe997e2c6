import { Injectable } from "@angular/core";

import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class SalesInvoiceService {
  
  constructor(private http: HttpClient) {}

  getSalesInvoice(date) {
    return this.http.get(`${environment.apiUrl}/sales_invoice/?date=${date}`).toPromise();
  }
  getSalesInvoiceById(id) {
    return this.http
      .get(`${environment.apiUrl}/sales_invoice/?invoice_id=${id}`)
      .toPromise();
  }
  saveSalesInvoice(data,status) {
    return this.http
      .post(`${environment.apiUrl}/sales_invoice/${ status == 'order' ?'?status=order':''}`, data)
      .toPromise();
  }
  editSalesInvoice(data) {
    return this.http
      .put(`${environment.apiUrl}/sales_invoice/`, data)
      .toPromise();
  }
  createSalesInvoice() {
    return this.http
      .get(`${environment.apiUrl}/sales_invoice/?create_invoice=true`)
      .toPromise();
  }
  getBuyerSalesInvoice(id) {
    return this.http
      .get(`${environment.apiUrl}/sales_invoice/?buyer_id=${id}`)
      .toPromise();
  }

  deleteSalesInvoice(data) {
    return this.http
      .delete(`${environment.apiUrl}/sales_invoice/?create_invoice=true`, {
        body: data,
      })
      .toPromise();
  }
  changeInvoiceType(invoice_id: any) {
    return this.http
      .patch(`${environment.apiUrl}/sales_invoice/?invoice_type=${invoice_id}`, {})
      .toPromise();
  }
}
