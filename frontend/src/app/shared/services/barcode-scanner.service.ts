import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class BarcodeScannerService {
  private stream: MediaStream | null = null;
  private isScanning = false;

  constructor(private platform: Platform) {}

  /**
   * Start barcode scanning using device camera
   * @returns Promise<string> - Scanned barcode data
   */
  async startScan(): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try {
        // Check if camera is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          reject(new Error('Camera not available on this device'));
          return;
        }

        // Request camera permission
        this.stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: 'environment', // Use back camera
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        });

        this.isScanning = true;

        // For now, we'll use a simple implementation
        // In a real app, you'd integrate with a barcode detection library
        // This is a placeholder that simulates scanning
        setTimeout(() => {
          this.stopScan();
          // Simulate a scanned barcode (company code + invoice ID format)
          const mockBarcode = 'ABC000001'; // This would be the actual scanned data
          resolve(mockBarcode);
        }, 3000);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Stop barcode scanning and release camera
   */
  stopScan(): void {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.isScanning = false;
  }

  /**
   * Check if currently scanning
   */
  getIsScanning(): boolean {
    return this.isScanning;
  }

  /**
   * Parse barcode data to extract invoice information
   * @param barcodeData - Raw barcode data
   * @returns Object with parsed invoice information
   */
  parseBarcodeData(barcodeData: string): { companyCode: string; invoiceId: number } | null {
    try {
      // Expected format: {COMPANY_CODE}{INVOICE_ID_PADDED}
      // Example: ABC000123 -> companyCode: ABC, invoiceId: 123
      
      const companyCode = localStorage.getItem('company_code') || 'INV';
      
      if (barcodeData.startsWith(companyCode)) {
        const invoiceIdStr = barcodeData.substring(companyCode.length);
        const invoiceId = parseInt(invoiceIdStr, 10);
        
        if (!isNaN(invoiceId)) {
          return {
            companyCode,
            invoiceId
          };
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error parsing barcode data:', error);
      return null;
    }
  }

  /**
   * Check camera permissions
   */
  async checkCameraPermission(): Promise<boolean> {
    try {
      const result = await navigator.permissions.query({ name: 'camera' as PermissionName });
      return result.state === 'granted';
    } catch (error) {
      console.error('Error checking camera permission:', error);
      return false;
    }
  }

  /**
   * Request camera permissions
   */
  async requestCameraPermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Camera permission denied:', error);
      return false;
    }
  }
}
