import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import Papa from 'papaparse';
import { PrintServiceService } from './print-service.service';
import { UtilService } from './util.service';

@Injectable({
    providedIn: "root",
})
export class ReportsService {
    private apiUrl = environment.apiUrl;
    rows: any;
    columns: string[];

    constructor(
        private http: HttpClient,
        private printService: PrintServiceService,
        private util:UtilService
    ) { }

    getGstReport(from_date: string, to_date: string) {
        return this.http.get(`${this.apiUrl}/sales_invoice_report`, {
            params: { from_date, to_date },
            responseType: 'text' // Expect plain text for CSV
        }).toPromise();
    }
    getPayInReport(from_date:string,to_date: string,report_type:string): Observable<any> {
        return this.http.get(`${this.apiUrl}/report`,{params:{ from_date,to_date,report_type}})
    }
    getModeOfPaymentReport(from_date:string,to_date: string,report_type:string,mode_of_payment): Observable<any> {
        return this.http.get(`${this.apiUrl}/report`,{params:{ from_date,to_date,report_type,mode_of_payment}})
    }
    getSalesPerson(): Observable<any> {
        return this.http.get(`${this.apiUrl}/incentive`)
    }
    getIncentiveReport(from_date:string,to_date: string,sales_person): Observable<any> {
        return this.http.get(`${this.apiUrl}/incentive`,{params:{ from_date,to_date,sales_person}})
    }

    // New method for Brand-wise Sales Report
    getBrandWiseSalesReport(from_date: string, to_date: string, brand_ids?: number[], include_zero_sales: boolean = true): Observable<any> {
        let params: any = { 
            from_date, 
            to_date, 
            report_type: 'brand_wise_sales',
            include_zero_sales 
        };
        
        if (brand_ids && brand_ids.length > 0) {
            params.brand_ids = brand_ids;
        }
        
        return this.http.get(`${this.apiUrl}/report`, { params });
    }

    // New method for General Sales Report
    getGeneralSalesReport(from_date: string, to_date: string, include_zero_sales: boolean = true): Observable<any> {
        const params = { 
            from_date, 
            to_date, 
            report_type: 'general_sales',
            include_zero_sales 
        };
        
        return this.http.get(`${this.apiUrl}/report`, { params });
    }

    // New method to get brands for dropdown
    getBrands(): Observable<any> {
        return this.http.get(`${this.apiUrl}/brand/`);
    }

    generatePrintableHTML(parsedData) {
        this.rows = parsedData;
        this.columns = Object.keys(parsedData[0] || {});
        
        // Generate table content with enhanced styling
        let tableContent = `<table>
            <thead>
                <tr>`;
        this.columns.forEach(column => {
            tableContent += `<th>${column}</th>`;
        });
        tableContent += `</tr>
            </thead>
            <tbody>`;
        
        this.rows.forEach((row, index) => {
            const isSummaryRow = row[this.columns[0]] && (
                row[this.columns[0]].includes('SUMMARY') || 
                row[this.columns[0]].includes('BRAND:') ||
                row[this.columns[0]].includes('OVERALL')
            );
            
            const rowClass = isSummaryRow ? 'total-row' : '';
            tableContent += `<tr class="${rowClass}">`;
            
            this.columns.forEach(column => {
                const value = row[column] || '';
                const isAmount = column.toLowerCase().includes('total') || column.toLowerCase().includes('amount') || column.toLowerCase().includes('rate');
                const isNumber = column.toLowerCase().includes('quantity') || column.toLowerCase().includes('pieces') || column.toLowerCase().includes('count');
                const isName = column.toLowerCase().includes('buyer') || column.toLowerCase().includes('brand') || column.toLowerCase().includes('product');
                
                let cellClass = '';
                if (isAmount) cellClass = 'amount-cell';
                else if (isNumber) cellClass = 'number-cell';
                else if (isName) cellClass = 'name-cell';
                
                tableContent += `<td class="${cellClass}">${value}</td>`;
            });
            tableContent += '</tr>';
        });
        
        tableContent += `</tbody>
        </table>`;
        
        return tableContent;
    }

    parseCSV(csvData: string) {
        const parsedData = Papa.parse(csvData, { header: true });
        return parsedData.data; // Returns an array of objects
    }
    getPrint(content,fileName) {
        let htmlContent = `<!DOCTYPE html>
        <html>
        
        <head>
            <title>${fileName}</title>
            <meta charset="UTF-8" />
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
            <style>
                * {
                    font-family: "arial";
                    width: 100 %;
                }
        
                html,
                body {
                    margin: 0;
                    padding: 0;
                }
        
                .page {
                    page-break-after: always;
                    page-break-inside: avoid;
                }
        
                .container {
                    display: table;
                }
        
                .container: nth - child(21) {
                    page-break-after: always;
                    page-break-inside: avoid;
                }
        
                .container {
                    font-size: 0;
                }
        
                .header - item {
                    display: inline-block;
                    font-size: 22px !important;
                }
        
                < !--div {
                    box-sizing: border - box;
                }
        
                -->
                table {
                    font-family: arial, sans - serif;
                    border-collapse: collapse;
                    width: 100 %;
                    font-size: 12px;
                }
        
                td,
                th {
                    border: 1px solid #dddddd;
                    text-align: center;
                }
        
        
                .page {
                    min-height: 600px;
                }
        
                @page {
                    size: A5;
                }
        
        
                @media print {
        
        
                    body {
                        margin: 0;
                        zoom: 65 %;
                    }
        
                    thead {
                        display: table-header-group;
                    }
        
                    tfoot {
                        display: table-footer-group;
                    }
                }
            </style>
        </head>
        
        <body class="A5 ">
            <h3 style="text-align:center;margin-top:12px;">${localStorage.getItem("company_name")} Report </h3>
            <h3 style="text-align:center;margin-top:12px;">${fileName}</h3>
            ${content}
            </body>
        
        </html>`;
        this.util.isCordova() ?  this.printService.printDocument(htmlContent,fileName) : this.printService.printHtmlDocument(htmlContent);;
    }
}