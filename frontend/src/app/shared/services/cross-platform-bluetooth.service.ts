import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { AlertController } from '@ionic/angular';
import { BluetoothService } from './bluetooth.service';

export interface BluetoothDevice {
  id: string;
  name: string;
  address?: string;
}

export interface BluetoothPrintResult {
  success: boolean;
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class CrossPlatformBluetoothService {
  private webBluetoothDevice: BluetoothDevice | null = null;
  private webBluetoothCharacteristic: BluetoothRemoteGATTCharacteristic | null = null;

  constructor(
    private platform: Platform,
    private alertController: AlertController,
    private androidBluetoothService: BluetoothService
  ) {}

  /**
   * Check if Bluetooth is supported on current platform
   */
  isBluetoothSupported(): boolean {
    if (this.platform.is('ios')) {
      // Check for Web Bluetooth API support
      return 'bluetooth' in navigator;
    } else if (this.platform.is('android')) {
      // Android uses Cordova plugin
      return true;
    }
    return false;
  }

  /**
   * Get available Bluetooth devices
   */
  async getDeviceList(): Promise<BluetoothDevice[]> {
    if (this.platform.is('android')) {
      return this.getAndroidDevices();
    } else if (this.platform.is('ios')) {
      return this.getIOSDevices();
    }
    throw new Error('Platform not supported');
  }

  /**
   * Connect to a Bluetooth device
   */
  async connectToDevice(deviceId: string): Promise<boolean> {
    if (this.platform.is('android')) {
      return this.connectAndroidDevice(deviceId);
    } else if (this.platform.is('ios')) {
      return this.connectIOSDevice(deviceId);
    }
    return false;
  }

  /**
   * Print data to connected device
   */
  async printData(data: Uint8Array): Promise<BluetoothPrintResult> {
    if (this.platform.is('android')) {
      return this.printToAndroidDevice(data);
    } else if (this.platform.is('ios')) {
      return this.printToIOSDevice(data);
    }
    return { success: false, message: 'Platform not supported' };
  }

  /**
   * Disconnect from current device
   */
  async disconnect(): Promise<void> {
    if (this.platform.is('android')) {
      this.androidBluetoothService.disconnectPrinter();
    } else if (this.platform.is('ios')) {
      await this.disconnectIOSDevice();
    }
  }

  /**
   * Show device selection dialog
   */
  async showDeviceSelection(): Promise<void> {
    if (this.platform.is('android')) {
      this.androidBluetoothService.getList();
    } else if (this.platform.is('ios')) {
      await this.showIOSDeviceSelection();
    }
  }

  // Android-specific methods
  private async getAndroidDevices(): Promise<BluetoothDevice[]> {
    // Use existing Android Bluetooth service
    const devices = JSON.parse(localStorage.getItem('devices_list') || '[]');
    return devices.map((device: any) => ({
      id: device.id,
      name: device.name || 'Unnamed Device',
      address: device.address
    }));
  }

  private async connectAndroidDevice(deviceId: string): Promise<boolean> {
    localStorage.setItem('deviceId', deviceId);
    return true;
  }

  private async printToAndroidDevice(data: Uint8Array): Promise<BluetoothPrintResult> {
    try {
      this.androidBluetoothService.printToDevice(data);
      return { success: true, message: 'Print sent successfully' };
    } catch (error) {
      return { success: false, message: `Print failed: ${error}` };
    }
  }

  // iOS-specific methods using Web Bluetooth API
  private async getIOSDevices(): Promise<BluetoothDevice[]> {
    // For iOS, we'll use device selection through Web Bluetooth
    // This returns empty array as Web Bluetooth requires user interaction
    return [];
  }

  private async connectIOSDevice(deviceId: string): Promise<boolean> {
    try {
      if (!('bluetooth' in navigator)) {
        throw new Error('Web Bluetooth not supported');
      }

      const device = await (navigator as any).bluetooth.requestDevice({
        acceptAllDevices: true,
        optionalServices: ['battery_service', 'device_information']
      });

      const server = await device.gatt.connect();
      
      // Store device for later use
      this.webBluetoothDevice = {
        id: device.id,
        name: device.name || 'Unknown Device'
      };

      // Try to find a writable characteristic
      const services = await server.getPrimaryServices();
      for (const service of services) {
        const characteristics = await service.getCharacteristics();
        for (const characteristic of characteristics) {
          if (characteristic.properties.write || characteristic.properties.writeWithoutResponse) {
            this.webBluetoothCharacteristic = characteristic;
            break;
          }
        }
        if (this.webBluetoothCharacteristic) break;
      }

      return true;
    } catch (error) {
      console.error('iOS Bluetooth connection failed:', error);
      return false;
    }
  }

  private async printToIOSDevice(data: Uint8Array): Promise<BluetoothPrintResult> {
    try {
      if (!this.webBluetoothCharacteristic) {
        throw new Error('No writable characteristic found');
      }

      // Use type assertion to access properties
      const characteristic = this.webBluetoothCharacteristic as any;

      if (characteristic.properties?.writeWithoutResponse) {
        await characteristic.writeValueWithoutResponse(data);
      } else if (characteristic.properties?.write) {
        await characteristic.writeValue(data);
      } else {
        // Fallback: try writeValue anyway
        await characteristic.writeValue(data);
      }

      return { success: true, message: 'Print sent successfully' };
    } catch (error) {
      return { success: false, message: `Print failed: ${error}` };
    }
  }

  private async disconnectIOSDevice(): Promise<void> {
    if (this.webBluetoothDevice && (this.webBluetoothDevice as any).gatt) {
      await (this.webBluetoothDevice as any).gatt.disconnect();
    }
    this.webBluetoothDevice = null;
    this.webBluetoothCharacteristic = null;
  }

  private async showIOSDeviceSelection(): Promise<void> {
    try {
      const connected = await this.connectIOSDevice('');
      if (connected) {
        const alert = await this.alertController.create({
          header: 'Bluetooth Device Connected',
          message: `Connected to: ${this.webBluetoothDevice?.name || 'Unknown Device'}`,
          buttons: ['OK']
        });
        await alert.present();
      }
    } catch (error) {
      const alert = await this.alertController.create({
        header: 'Connection Failed',
        message: 'Failed to connect to Bluetooth device. Please ensure the device is in pairing mode.',
        buttons: ['OK']
      });
      await alert.present();
    }
  }

  /**
   * Get currently selected/connected device
   */
  getSelectedDevice(): BluetoothDevice | null {
    if (this.platform.is('android')) {
      return this.androidBluetoothService.getSelectedDevice();
    } else if (this.platform.is('ios')) {
      return this.webBluetoothDevice;
    }
    return null;
  }

  /**
   * Check if device is currently connected
   */
  isConnected(): boolean {
    if (this.platform.is('android')) {
      return !!localStorage.getItem('deviceId');
    } else if (this.platform.is('ios')) {
      return !!this.webBluetoothDevice;
    }
    return false;
  }
}
