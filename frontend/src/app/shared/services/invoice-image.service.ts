import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

export interface InvoiceImage {
  id: number;
  sales_invoice: number;
  image: string;
  image_url: string;
  filename: string;
  file_size: number;
  file_type: string;
  description: string;
  uploaded_at: string;
  uploaded_by: number;
  uploaded_by_name: string;
}

@Injectable({
  providedIn: 'root'
})
export class InvoiceImageService {
  
  constructor(private http: HttpClient) {}

  /**
   * Get all images for a specific invoice
   */
  getInvoiceImages(invoiceId: number): Promise<any> {
    return this.http.get(`${environment.apiUrl}/invoice-image/?invoice_id=${invoiceId}`).toPromise();
  }

  /**
   * Upload an image for an invoice
   */
  uploadInvoiceImage(invoiceId: number, imageFile: File, description: string = ''): Promise<any> {
    const formData = new FormData();
    formData.append('invoice_id', invoiceId.toString());
    formData.append('image', imageFile);
    formData.append('description', description);

    return this.http.post(`${environment.apiUrl}/invoice-image/`, formData).toPromise();
  }

  /**
   * Delete an invoice image
   */
  deleteInvoiceImage(imageId: number): Promise<any> {
    return this.http.delete(`${environment.apiUrl}/invoice-image/?image_id=${imageId}`).toPromise();
  }
} 