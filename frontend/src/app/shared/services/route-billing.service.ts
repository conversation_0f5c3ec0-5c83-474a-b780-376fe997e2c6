import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface RouteBillingSummary {
  id?: number;
  route: number;
  route_name?: string;
  date: string;
  total_shops: number;
  billed_shops: number;
  unbilled_shops: number;
  total_amount: number;
  completion_percentage: number;
  is_complete?: boolean;
  weekday?: string;
  unbilled_buyers?: any[];
  created_at?: string;
  updated_at?: string;
}

export interface ShopFreezerPhoto {
  id?: number;
  buyer: number;
  buyer_name?: string;
  route: number;
  route_name?: string;
  photo: string;
  date_taken?: string;
  time_taken?: string;
  notes?: string;
  uploaded_by?: number;
  uploaded_by_name?: string;
  location_latitude?: number;
  location_longitude?: number;
}

export interface RouteSchedule {
  id?: number;
  route: number;
  route_name?: string;
  weekday: string;
  is_active: boolean;
  expected_billing_time?: string;
  notes?: string;
}

@Injectable({
  providedIn: 'root'
})
export class RouteBillingService {

  constructor(private http: HttpClient) { }

  /**
   * Get route billing summaries
   */
  getRouteBillingSummaries(filters?: {
    date?: string;
    route_id?: number;
    weekday?: string;
  }): Promise<any> {
    let params = '';
    if (filters) {
      const queryParams = new URLSearchParams();
      if (filters.date) queryParams.append('date', filters.date);
      if (filters.route_id) queryParams.append('route_id', filters.route_id.toString());
      if (filters.weekday) queryParams.append('weekday', filters.weekday);
      params = queryParams.toString() ? `?${queryParams.toString()}` : '';
    }
    
    return this.http
      .get(`${environment.apiUrl}/route-billing-summary/${params}`)
      .toPromise();
  }

  /**
   * Refresh billing summary for a specific route and date
   */
  refreshBillingSummary(routeId: number, date: string): Promise<any> {
    return this.http
      .post(`${environment.apiUrl}/route-billing-summary/`, {
        route_id: routeId,
        date: date
      })
      .toPromise();
  }

  /**
   * Get freezer photos
   */
  getFreezerPhotos(filters?: {
    route_id?: number;
    buyer_id?: number;
    date?: string;
  }): Promise<any> {
    let params = '';
    if (filters) {
      const queryParams = new URLSearchParams();
      if (filters.route_id) queryParams.append('route_id', filters.route_id.toString());
      if (filters.buyer_id) queryParams.append('buyer_id', filters.buyer_id.toString());
      if (filters.date) queryParams.append('date', filters.date);
      params = queryParams.toString() ? `?${queryParams.toString()}` : '';
    }
    
    return this.http
      .get(`${environment.apiUrl}/shop-freezer-photo/${params}`)
      .toPromise();
  }

  /**
   * Upload freezer photo
   */
  uploadFreezerPhoto(
    buyerId: number, 
    routeId: number, 
    photoFile: File, 
    notes?: string,
    location?: { latitude: number; longitude: number }
  ): Promise<any> {
    const formData = new FormData();
    formData.append('buyer_id', buyerId.toString());
    formData.append('route_id', routeId.toString());
    formData.append('photo', photoFile);
    
    if (notes) {
      formData.append('notes', notes);
    }
    
    if (location) {
      formData.append('location_latitude', location.latitude.toString());
      formData.append('location_longitude', location.longitude.toString());
    }
    
    return this.http
      .post(`${environment.apiUrl}/shop-freezer-photo/`, formData)
      .toPromise();
  }

  /**
   * Delete freezer photo
   */
  deleteFreezerPhoto(photoId: number): Promise<any> {
    return this.http
      .delete(`${environment.apiUrl}/shop-freezer-photo/`, { body: { id: photoId } })
      .toPromise();
  }

  /**
   * Get today's route summaries
   */
  getTodayRouteSummaries(): Promise<any> {
    const today = new Date().toISOString().split('T')[0];
    return this.getRouteBillingSummaries({ date: today });
  }

  /**
   * Get route summaries for a specific weekday
   */
  getWeekdayRouteSummaries(weekday: string, date?: string): Promise<any> {
    const targetDate = date || new Date().toISOString().split('T')[0];
    return this.getRouteBillingSummaries({ 
      date: targetDate, 
      weekday: weekday 
    });
  }

  /**
   * Get unbilled shops for a route and date
   */
  getUnbilledShops(routeId: number, date: string): Promise<any> {
    return this.getRouteBillingSummaries({ 
      route_id: routeId, 
      date: date 
    }).then(response => {
      if (response.success && response.data.length > 0) {
        return {
          success: true,
          data: response.data[0].unbilled_buyers || []
        };
      }
      return {
        success: false,
        data: []
      };
    });
  }

  /**
   * Get route completion status for dashboard
   */
  getRouteCompletionStatus(date?: string): Promise<any> {
    const targetDate = date || new Date().toISOString().split('T')[0];
    return this.getRouteBillingSummaries({ date: targetDate }).then(response => {
      if (response.success) {
        const summaries = response.data;
        const totalRoutes = summaries.length;
        const completedRoutes = summaries.filter(s => s.is_complete).length;
        const totalShops = summaries.reduce((sum, s) => sum + s.total_shops, 0);
        const billedShops = summaries.reduce((sum, s) => sum + s.billed_shops, 0);
        const totalAmount = summaries.reduce((sum, s) => sum + s.total_amount, 0);

        return {
          success: true,
          data: {
            total_routes: totalRoutes,
            completed_routes: completedRoutes,
            completion_percentage: totalRoutes > 0 ? (completedRoutes / totalRoutes) * 100 : 0,
            total_shops: totalShops,
            billed_shops: billedShops,
            unbilled_shops: totalShops - billedShops,
            total_amount: totalAmount,
            route_summaries: summaries
          }
        };
      }
      return response;
    });
  }

  /**
   * Get current location
   */
  getCurrentLocation(): Promise<{ latitude: number; longitude: number }> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  }



}
