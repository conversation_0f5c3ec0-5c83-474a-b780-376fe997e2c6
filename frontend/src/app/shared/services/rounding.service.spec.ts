import { TestBed } from '@angular/core/testing';
import { RoundingService, RoundingConfig } from './rounding.service';

describe('RoundingService', () => {
  let service: RoundingService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(RoundingService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('applyRoundingToNearest', () => {
    it('should return original amount when amount is 0', () => {
      const result = service.applyRoundingToNearest(0);
      expect(result.originalAmount).toBe(0);
      expect(result.roundedAmount).toBe(0);
      expect(result.adjustment).toBe(0);
    });

    it('should round to nearest rupee correctly', () => {
      // Test rounding down
      let result = service.applyRoundingToNearest(123.45);
      expect(result.originalAmount).toBe(123.45);
      expect(result.roundedAmount).toBe(123);
      expect(result.adjustment).toBeCloseTo(-0.45, 2);

      // Test rounding up
      result = service.applyRoundingToNearest(123.55);
      expect(result.originalAmount).toBe(123.55);
      expect(result.roundedAmount).toBe(124);
      expect(result.adjustment).toBeCloseTo(0.45, 2);

      // Test exact amount
      result = service.applyRoundingToNearest(123.00);
      expect(result.originalAmount).toBe(123.00);
      expect(result.roundedAmount).toBe(123);
      expect(result.adjustment).toBe(0);
    });


  });

  describe('calculateInvoiceTotals', () => {
    it('should calculate invoice totals with rounding enabled', () => {
      const config: RoundingConfig = { enabled: true };
      const result = service.calculateInvoiceTotals(100, 23.45, config);

      expect(result.subtotal).toBe(100);
      expect(result.taxAmount).toBe(23.45);
      expect(result.grossTotal).toBe(123.45);
      expect(result.roundingAdjustment).toBeCloseTo(-0.45, 2);
      expect(result.netAmount).toBe(123);
    });

    it('should return original totals when rounding disabled', () => {
      const config: RoundingConfig = { enabled: false };
      const result = service.calculateInvoiceTotals(100, 23.45, config);

      expect(result.subtotal).toBe(100);
      expect(result.taxAmount).toBe(23.45);
      expect(result.grossTotal).toBe(123.45);
      expect(result.roundingAdjustment).toBe(0);
      expect(result.netAmount).toBe(123.45);
    });
  });

  describe('formatRoundingAdjustment', () => {
    it('should format positive adjustment correctly', () => {
      const result = service.formatRoundingAdjustment(0.45);
      expect(result).toBe('+₹0.45');
    });

    it('should format negative adjustment correctly', () => {
      const result = service.formatRoundingAdjustment(-0.45);
      expect(result).toBe('-₹0.45');
    });

    it('should format zero adjustment correctly', () => {
      const result = service.formatRoundingAdjustment(0);
      expect(result).toBe('₹0.00');
    });
  });



  describe('isRoundingBeneficial', () => {
    it('should return true for amounts with decimals', () => {
      expect(service.isRoundingBeneficial(123.45)).toBe(true);
      expect(service.isRoundingBeneficial(123.01)).toBe(true);
    });

    it('should return false for whole amounts', () => {
      expect(service.isRoundingBeneficial(123)).toBe(false);
      expect(service.isRoundingBeneficial(123.00)).toBe(false);
    });
  });
});
