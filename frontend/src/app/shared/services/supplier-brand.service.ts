import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface SupplierBrand {
  id?: number;
  supplier_id: number;
  brand_id: number;
  supplier_name?: string;
  brand_name?: string;
  is_active?: boolean;
  created_date?: string;
}

export interface SupplierProductsResponse {
  products: any[];
  brands: any[];
  supplier_id: number;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SupplierBrandService {
  private apiUrl = `${environment.apiUrl}/supplier_brand/`;
  private supplierProductsUrl = `${environment.apiUrl}/supplier_products/`;

  constructor(private http: HttpClient) { }

  // Get supplier-brand relationships
  getSupplierBrands(supplierId?: number, brandId?: number): Observable<any> {
    let url = this.apiUrl;
    const params: string[] = [];
    
    if (supplierId) {
      params.push(`supplier_id=${supplierId}`);
    }
    if (brandId) {
      params.push(`brand_id=${brandId}`);
    }
    
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }
    
    return this.http.get(url);
  }

  // Create supplier-brand relationship
  createSupplierBrand(supplierBrand: { supplier_id: number; brand_id: number }): Observable<any> {
    return this.http.post(this.apiUrl, supplierBrand);
  }

  // Delete supplier-brand relationship
  deleteSupplierBrand(supplierBrandId: number): Observable<any> {
    return this.http.delete(this.apiUrl, { body: { id: supplierBrandId } });
  }

  // Get products filtered by supplier brands
  getProductsBySupplier(supplierId: number): Observable<any> {
    return this.http.get(`${this.supplierProductsUrl}?supplier_id=${supplierId}`);
  }

  // Bulk create supplier-brand relationships
  createBulkSupplierBrands(supplierId: number, brandIds: number[]): Observable<any[]> {
    const requests = brandIds.map(brandId => 
      this.createSupplierBrand({ supplier_id: supplierId, brand_id: brandId })
    );
    return new Observable(observer => {
      Promise.all(requests.map(req => req.toPromise()))
        .then(results => {
          observer.next(results);
          observer.complete();
        })
        .catch(error => observer.error(error));
    });
  }

  // Bulk delete supplier-brand relationships
  deleteBulkSupplierBrands(supplierBrandIds: number[]): Observable<any[]> {
    const requests = supplierBrandIds.map(id => 
      this.deleteSupplierBrand(id)
    );
    return new Observable(observer => {
      Promise.all(requests.map(req => req.toPromise()))
        .then(results => {
          observer.next(results);
          observer.complete();
        })
        .catch(error => observer.error(error));
    });
  }
}
