import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Translation {
  [key: string]: string | Translation;
}

export interface LanguageConfig {
  code: string;
  name: string;
  nativeName: string;
  rtl: boolean;
  dateFormat: string;
  numberFormat: string;
}

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private currentLanguage$ = new BehaviorSubject<string>('en');
  private translations: Map<string, Translation> = new Map();
  
  // Supported languages configuration
  private languages: LanguageConfig[] = [
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      rtl: false,
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'en-US'
    },
    {
      code: 'ta',
      name: 'Tamil',
      nativeName: 'தமிழ்',
      rtl: false,
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'ta-IN'
    }
  ];

  constructor() {
    // Load translations first
    this.loadTranslations();
    // Initialize language after translations are loaded
    this.initializeLanguage();
  }

  /**
   * Initialize language from metadata or localStorage
   */
  private initializeLanguage(): void {
    try {
      // First check metadata for user language preference
      const metadata = localStorage.getItem('metadata');
      if (metadata) {
        const parsedMetadata = JSON.parse(metadata);
        if (parsedMetadata?.userPreferences?.language) {
          this.setLanguage(parsedMetadata.userPreferences.language);
          return;
        }
      }
      
      // Fallback to localStorage language preference
      const savedLanguage = localStorage.getItem('app_language') || 'en';
      this.setLanguage(savedLanguage);
    } catch (error) {
      console.error('Error initializing language:', error);
      // Default to English if there's any error
      this.setLanguage('en');
    }
  }

  /**
   * Update language preference in metadata
   */
  updateLanguageInMetadata(languageCode: string): void {
    try {
      const metadata = localStorage.getItem('metadata');
      if (metadata) {
        const parsedMetadata = JSON.parse(metadata);
        if (!parsedMetadata.userPreferences) {
          parsedMetadata.userPreferences = {};
        }
        parsedMetadata.userPreferences.language = languageCode;
        localStorage.setItem('metadata', JSON.stringify(parsedMetadata));
      }
    } catch (error) {
      console.error('Error updating language in metadata:', error);
    }
  }

  /**
   * Get current language as observable
   */
  getCurrentLanguage(): Observable<string> {
    return this.currentLanguage$.asObservable();
  }

  /**
   * Get current language code
   */
  getCurrentLanguageCode(): string {
    return this.currentLanguage$.value;
  }

  /**
   * Set current language
   */
  setLanguage(languageCode: string): void {
    if (this.isLanguageSupported(languageCode)) {
      this.currentLanguage$.next(languageCode);
      localStorage.setItem('app_language', languageCode);
      
      // Update language preference in metadata
      this.updateLanguageInMetadata(languageCode);
      
      // Update document language attribute
      document.documentElement.lang = languageCode;
      
      // Update body direction for RTL languages
      const language = this.getLanguageConfig(languageCode);
      document.body.dir = language?.rtl ? 'rtl' : 'ltr';
    }
  }

  /**
   * Get available languages
   */
  getAvailableLanguages(): LanguageConfig[] {
    return [...this.languages];
  }

  /**
   * Check if language is supported
   */
  isLanguageSupported(languageCode: string): boolean {
    return this.languages.some(lang => lang.code === languageCode);
  }

  /**
   * Get language configuration
   */
  getLanguageConfig(languageCode: string): LanguageConfig | undefined {
    return this.languages.find(lang => lang.code === languageCode);
  }

  /**
   * Translate a key to current language
   */
  translate(key: string, params?: { [key: string]: any }): string {
    const currentLang = this.getCurrentLanguageCode();
    const translations = this.translations.get(currentLang);
    
    if (!translations) {
      console.warn(`No translations found for language: ${currentLang}`);
      return key;
    }

    const translation = this.getNestedValue(translations, key);
    
    if (!translation) {
      console.warn(`Translation not found for key: ${key} in language: ${currentLang}`);
      return key;
    }

    return this.interpolateParams(translation, params);
  }

  /**
   * Get instant translation (alias for translate)
   */
  instant(key: string, params?: { [key: string]: any }): string {
    return this.translate(key, params);
  }

  /**
   * Load all translations
   */
  private async loadTranslations(): Promise<void> {
    try {
      // Load English translations
      const enTranslations = await this.loadEnglishTranslations();
      this.translations.set('en', enTranslations);

      // Load Tamil translations
      const taTranslations = await this.loadTamilTranslations();
      this.translations.set('ta', taTranslations);

    } catch (error) {
      console.error('Error loading translations:', error);
    }
  }

  /**
   * Load English translations
   */
  private async loadEnglishTranslations(): Promise<Translation> {
    return {
      // Common UI elements
      common: {
        save: 'Save',
        cancel: 'Cancel',
        delete: 'Delete',
        edit: 'Edit',
        add: 'Add',
        search: 'Search',
        filter: 'Filter',
        loading: 'Loading...',
        noData: 'No data available',
        error: 'Error',
        success: 'Success',
        warning: 'Warning',
        confirm: 'Confirm',
        yes: 'Yes',
        no: 'No',
        ok: 'OK',
        close: 'Close',
        back: 'Back',
        next: 'Next',
        previous: 'Previous',
        submit: 'Submit',
        reset: 'Reset',
        clear: 'Clear',
        total: 'Total',
        amount: 'Amount',
        date: 'Date',
        name: 'Name',
        phone: 'Phone',
        email: 'Email',
        address: 'Address',
        active: 'Active',
        inactive: 'Inactive',
        status: 'Status'
      },

      // Navigation
      nav: {
        home: 'Home',
        products: 'Products',
        buyers: 'Buyers',
        suppliers: 'Suppliers',
        sales: 'Sales',
        purchase: 'Purchase',
        reports: 'Reports',
        settings: 'Settings',
        profile: 'Profile',
        logout: 'Logout'
      },

      // Products
      products: {
        title: 'Products',
        add: 'Add Product',
        edit: 'Edit Product',
        name: 'Product Name',
        shortCode: 'Short Code',
        brand: 'Brand',
        mrp: 'MRP',
        rate: 'Rate',
        margin: 'Margin',
        stock: 'Stock',
        unit: 'Unit',
        description: 'Description',
        category: 'Category',
        hsn: 'HSN Code',
        tax: 'Tax Rate',
        lowStock: 'Low Stock',
        outOfStock: 'Out of Stock',
        inStock: 'In Stock'
      },

      // Sales
      sales: {
        title: 'Sales',
        invoice: 'Invoice',
        createInvoice: 'Create Invoice',
        editInvoice: 'Edit Invoice',
        billNo: 'Bill No',
        billDate: 'Bill Date',
        customer: 'Customer',
        items: 'Items',
        quantity: 'Quantity',
        price: 'Price',
        total: 'Total',
        subtotal: 'Subtotal',
        discount: 'Discount',
        taxAmount: 'Tax Amount',
        grandTotal: 'Grand Total',
        payment: 'Payment',
        balance: 'Balance',
        paid: 'Paid',
        pending: 'Pending'
      },

      // Buyers
      buyers: {
        title: 'Buyers',
        add: 'Add Buyer',
        edit: 'Edit Buyer',
        fullName: 'Full Name',
        contactPerson: 'Contact Person',
        phoneNumber: 'Phone Number',
        emailAddress: 'Email Address',
        address: 'Address',
        city: 'City',
        state: 'State',
        pincode: 'Pincode',
        gstNumber: 'GST Number',
        creditLimit: 'Credit Limit',
        route: 'Route',
        outstanding: 'Outstanding',
        totalPurchases: 'Total Purchases'
      },

      // Messages
      messages: {
        saveSuccess: 'Saved successfully',
        updateSuccess: 'Updated successfully',
        deleteSuccess: 'Deleted successfully',
        deleteConfirm: 'Are you sure you want to delete this item?',
        validationError: 'Please fill all required fields',
        networkError: 'Network error. Please try again.',
        permissionDenied: 'Permission denied',
        sessionExpired: 'Session expired. Please login again.',
        languageChanged: 'Language changed successfully'
      }
    };
  }

  /**
   * Load Tamil translations
   */
  private async loadTamilTranslations(): Promise<Translation> {
    return {
      // Common UI elements
      common: {
        save: 'சேமி',
        cancel: 'ரத்து',
        delete: 'நீக்கு',
        edit: 'திருத்து',
        add: 'சேர்',
        search: 'தேடு',
        filter: 'வடிகட்டு',
        loading: 'ஏற்றுகிறது...',
        noData: 'தரவு கிடைக்கவில்லை',
        error: 'பிழை',
        success: 'வெற்றி',
        warning: 'எச்சரிக்கை',
        confirm: 'உறுதிப்படுத்து',
        yes: 'ஆம்',
        no: 'இல்லை',
        ok: 'சரி',
        close: 'மூடு',
        back: 'பின்',
        next: 'அடுத்து',
        previous: 'முந்தைய',
        submit: 'சமர்ப்பி',
        reset: 'மீட்டமை',
        clear: 'அழி',
        total: 'மொத்தம்',
        amount: 'தொகை',
        date: 'தேதி',
        name: 'பெயர்',
        phone: 'தொலைபேசி',
        email: 'மின்னஞ்சல்',
        address: 'முகவரி',
        active: 'செயலில்',
        inactive: 'செயலற்ற',
        status: 'நிலை'
      },

      // Navigation
      nav: {
        home: 'முகப்பு',
        products: 'பொருட்கள்',
        buyers: 'வாங்குவோர்',
        suppliers: 'சப்ளையர்கள்',
        sales: 'விற்பனை',
        purchase: 'கொள்முதல்',
        reports: 'அறிக்கைகள்',
        settings: 'அமைப்புகள்',
        profile: 'சுயவிவரம்',
        logout: 'வெளியேறு'
      },

      // Products
      products: {
        title: 'பொருட்கள்',
        add: 'பொருள் சேர்',
        edit: 'பொருள் திருத்து',
        name: 'பொருள் பெயர்',
        shortCode: 'குறுகிய குறியீடு',
        brand: 'பிராண்ட்',
        mrp: 'எம்.ஆர்.பி',
        rate: 'விலை',
        margin: 'லாப வீதம்',
        stock: 'இருப்பு',
        unit: 'அலகு',
        description: 'விளக்கம்',
        category: 'வகை',
        hsn: 'HSN குறியீடு',
        tax: 'வரி வீதம்',
        lowStock: 'குறைந்த இருப்பு',
        outOfStock: 'இருப்பு இல்லை',
        inStock: 'இருப்பில் உள்ளது'
      },

      // Sales
      sales: {
        title: 'விற்பனை',
        invoice: 'விலைப்பட்டியல்',
        createInvoice: 'விலைப்பட்டியல் உருவாக்கு',
        editInvoice: 'விலைப்பட்டியல் திருத்து',
        billNo: 'பில் எண்',
        billDate: 'பில் தேதி',
        customer: 'வாடிக்கையாளர்',
        items: 'பொருட்கள்',
        quantity: 'அளவு',
        price: 'விலை',
        total: 'மொத்தம்',
        subtotal: 'துணை மொத்தம்',
        discount: 'தள்ளுபடி',
        taxAmount: 'வரி தொகை',
        grandTotal: 'மொத்த தொகை',
        payment: 'பணம்',
        balance: 'மீதம்',
        paid: 'செலுத்தப்பட்டது',
        pending: 'நிலுவையில்'
      },

      // Buyers
      buyers: {
        title: 'வாங்குவோர்',
        add: 'வாங்குவோர் சேர்',
        edit: 'வாங்குவோர் திருத்து',
        fullName: 'முழு பெயர்',
        contactPerson: 'தொடர்பு நபர்',
        phoneNumber: 'தொலைபேசி எண்',
        emailAddress: 'மின்னஞ்சல் முகவரி',
        address: 'முகவரி',
        city: 'நகரம்',
        state: 'மாநிலம்',
        pincode: 'அஞ்சல் குறியீடு',
        gstNumber: 'GST எண்',
        creditLimit: 'கடன் வரம்பு',
        route: 'வழி',
        outstanding: 'நிலுவை',
        totalPurchases: 'மொத்த கொள்முதல்'
      },

      // Messages
      messages: {
        saveSuccess: 'வெற்றிகரமாக சேமிக்கப்பட்டது',
        updateSuccess: 'வெற்றிகரமாக புதுப்பிக்கப்பட்டது',
        deleteSuccess: 'வெற்றிகரமாக நீக்கப்பட்டது',
        deleteConfirm: 'இந்த உருப்படியை நீக்க விரும்புகிறீர்களா?',
        validationError: 'தேவையான அனைத்து புலங்களையும் நிரப்பவும்',
        networkError: 'நெட்வர்க் பிழை. மீண்டும் முயற்சிக்கவும்.',
        permissionDenied: 'அனுமதி மறுக்கப்பட்டது',
        sessionExpired: 'அமர்வு காலாவதியானது. மீண்டும் உள்நுழையவும்.',
        languageChanged: 'மொழி வெற்றிகரமாக மாற்றப்பட்டது'
      }
    };
  }

  /**
   * Get nested value from translation object
   */
  private getNestedValue(obj: Translation, key: string): string {
    const keys = key.split('.');
    let current: any = obj;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return '';
      }
    }

    return typeof current === 'string' ? current : '';
  }

  /**
   * Interpolate parameters in translation string
   */
  private interpolateParams(translation: string, params?: { [key: string]: any }): string {
    if (!params) {
      return translation;
    }

    let result = translation;
    Object.keys(params).forEach(key => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(params[key]));
    });

    return result;
  }

  /**
   * Format number according to current language
   */
  formatNumber(num: number): string {
    const languageConfig = this.getLanguageConfig(this.getCurrentLanguageCode());
    const formatter = new Intl.NumberFormat(languageConfig?.numberFormat || 'en-US');
    return formatter.format(num);
  }

  /**
   * Format currency according to current language
   */
  formatCurrency(amount: number, currency: string = 'INR'): string {
    const languageConfig = this.getLanguageConfig(this.getCurrentLanguageCode());
    const formatter = new Intl.NumberFormat(languageConfig?.numberFormat || 'en-US', {
      style: 'currency',
      currency: currency
    });
    return formatter.format(amount);
  }

  /**
   * Format date according to current language
   */
  formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const languageConfig = this.getLanguageConfig(this.getCurrentLanguageCode());
    
    if (languageConfig?.code === 'ta') {
      // Tamil date formatting
      return dateObj.toLocaleDateString('ta-IN');
    }
    
    return dateObj.toLocaleDateString('en-IN');
  }
}