import { BluetoothService } from './bluetooth.service';
import { CrossPlatformPrintService } from './cross-platform-print.service';
import { Injectable } from '@angular/core';
import { UtilService } from './util.service';
import { Platform } from '@ionic/angular';
import EscPosEncoder from 'esc-pos-encoder-ionic';
import { environment } from "src/environments/environment";
import { InvoiceTemplateService, InvoiceTemplateConfig } from './InvoiceTemplate.service';
import { Printer } from '@bcyesil/capacitor-plugin-printer';

export interface InvoiceData {
  id: number;
  name: string;
  date: string;
  phone_no: string;
  place: string;
  gst_no?: string;
  sales_invoice_items: InvoiceItem[];
  bill_amount: number;
  previous_balance: number;
  received_amount: number;
  current_balance: number;
  rounding_enabled?: boolean;
  rounding_adjustment?: number;
  gross_total?: number;
  delivery_challan?: boolean;
  invoice_status?: string;
  remarks?: string;
  headerdata?: any[];
  metadata?: any[];
  billed_by?: string;
  delivery_by?: string;
  collected_by?: string;
}

export interface InvoiceItem {
  product_name: string;
  no: number;
  weight: number;
  rate: number;
  line_total: number;
  mrp: number;
  tax_rate: number;
  hsn_code?: string;
  remarks?: string;
}


@Injectable({
    providedIn: 'root'
})
export class PrintServiceService {
    billing_field_settings: any = JSON.parse(localStorage.getItem('metadata')).billing_field_settings.filter(item => item.print_active).sort((a, b) => a.print_order - b.print_order);

    constructor(
        private util: UtilService,
        private bluetoothService: BluetoothService,
        private invoiceService: InvoiceTemplateService,
        private platform: Platform
    ) { }

    getMaxCharsPerLine(paperWidthMm) {
        // Average character width in mm (adjust based on your printer and font)
        const avgCharWidthMm = paperWidthMm == 58 ? 1.88 : 1.65;
        return Math.floor(paperWidthMm / avgCharWidthMm);
    }
    centerText(text, maxCharsPerLine) {
        const space = (maxCharsPerLine - text.length) / 2;
        return ' '.repeat(Math.max(0, space)) + text;
    }


    getTaxDetails(data) {
        let distinctValues = [...new Set(data.sales_invoice_items.map(item => item.tax_rate))];
        let taxDetails = distinctValues.map(value => ({
            value,
            tax_amount: data.sales_invoice_items
                .filter(item => item.tax_rate === value)
                .reduce((acc, cur) => acc + (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate), 0),
            taxable_amount: data.sales_invoice_items
                .filter(item => item.tax_rate === value)
                .reduce((acc, cur) => acc + (cur.line_total - (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate)), 0),
        }));
        let totalTaxAmount = data.sales_invoice_items.reduce((acc, cur) => acc + (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate), 0)
        return { taxDetails, totalTaxAmount }
    }
    getTotalValue(data) {
        let totalWeight = 0,
            totalNo = 0,
            totalAmount = 0;
        data.sales_invoice_items.forEach((element) => {
            if (element.weight) {
                totalWeight += element.weight;
            }
            if (element.no) {
                totalNo += element.no;
            }
            if (element.line_total) {
                totalAmount += element.line_total;
            }
        });
        return { totalWeight, totalNo, totalAmount }
    }

    /**
     * Generate staff information for mobile format
     * @param data - Invoice data
     * @returns HTML string for staff information
     */
    getStaffInfoForMobile(data) {
        let staffInfo = '';

        if (data.billed_by || data.delivery_by || data.collected_by) {
            if (data.billed_by) {
                staffInfo += `<p style="margin: 3px;text-align: center;"><strong>Billed by:</strong> ${data.billed_by}</p>`;
            }

            if (data.delivery_by) {
                staffInfo += `<p style="margin: 3px;text-align: center;"><strong>Delivery by:</strong> ${data.delivery_by}</p>`;
            }

            if (data.collected_by) {
                staffInfo += `<p style="margin: 3px;text-align: center;"><strong>Collected by:</strong> ${data.collected_by}</p>`;
            }

            staffInfo += `<p style="margin: 3px;text-align: center;">*******************************</p>`;
        }

        return staffInfo;
    }

    /**
     * Generate staff information for A5 format
     * @param data - Invoice data
     * @returns HTML string for staff information
     */
    getStaffInfoForA5(data) {
        let staffInfo = '';

        if (data.billed_by || data.delivery_by || data.collected_by) {
            if (data.billed_by) {
                staffInfo += `<div style="display: flex;">
                    <p style="margin: 2px; font-size: 12px; text-align: left; padding-left: 10px;">
                        <strong>Billed by:</strong> ${data.billed_by}</p>
                </div>`;
            }

            if (data.delivery_by) {
                staffInfo += `<div style="display: flex;">
                    <p style="margin: 2px; font-size: 12px; text-align: left; padding-left: 10px;">
                        <strong>Delivery by:</strong> ${data.delivery_by}</p>
                </div>`;
            }

            if (data.collected_by) {
                staffInfo += `<div style="display: flex;">
                    <p style="margin: 2px; font-size: 12px; text-align: left; padding-left: 10px;">
                        <strong>Collected by:</strong> ${data.collected_by}</p>
                </div>`;
            }
        }

        return staffInfo;
    }

    /**
     * Add thermal printer staff information section
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param divider - Divider line string
     */
    addThermalStaffInfo(encoder: any, data: any, divider: string): void {
        if (data.billed_by || data.delivery_by || data.collected_by) {
            encoder.align('center').text("Staff Information").newline();

            if (data.billed_by) {
                encoder.align('center').text(`Billed by: ${data.billed_by}`).newline();
            }

            if (data.delivery_by) {
                encoder.align('center').text(`Delivery by: ${data.delivery_by}`).newline();
            }

            if (data.collected_by) {
                encoder.align('center').text(`Collected by: ${data.collected_by}`).newline();
            }

            encoder.align('left').text(divider).newline();
        }
    }

    /**
     * Set UPI configuration for QR code generation
     * @param upiId - UPI ID for payments
     */
    setUpiConfiguration(upiId: string): void {
        localStorage.setItem("upi_id", upiId);
    }

    /**
     * Get UPI configuration
     * @returns UPI ID or null if not set
     */
    getUpiConfiguration(): string | null {
        return localStorage.getItem("upi_id");
    }

    /**
     * Load image from URL and convert to bitmap data for thermal printing
     * @param imageUrl - URL of the image to load
     * @param width - Target width for the image
     * @param height - Target height for the image
     * @returns Promise<ImageData> - Processed image data for thermal printing
     */
    async loadImageForThermal(imageUrl: string, width: number, height: number): Promise<ImageData> {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';

            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                if (!ctx) {
                    reject(new Error('Canvas context not available'));
                    return;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw image scaled to target size
                ctx.drawImage(img, 0, 0, width, height);

                try {
                    const imageData = ctx.getImageData(0, 0, width, height);
                    resolve(imageData);
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = () => {
                reject(new Error('Failed to load image'));
            };

            img.src = imageUrl;
        });
    }

    /**
     * Convert ImageData to monochrome bitmap for thermal printing
     * @param imageData - Image data from canvas
     * @returns Uint8Array - Monochrome bitmap data
     */
    convertToMonochrome(imageData: ImageData): Uint8Array {
        const { data, width, height } = imageData;
        const bitmap = new Uint8Array(Math.ceil(width / 8) * height);

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const pixelIndex = (y * width + x) * 4;
                const r = data[pixelIndex];
                const g = data[pixelIndex + 1];
                const b = data[pixelIndex + 2];

                // Convert to grayscale and threshold
                const gray = (r + g + b) / 3;
                const isBlack = gray < 128;

                if (isBlack) {
                    const byteIndex = Math.floor(y * Math.ceil(width / 8) + x / 8);
                    const bitIndex = 7 - (x % 8);
                    bitmap[byteIndex] |= (1 << bitIndex);
                }
            }
        }

        return bitmap;
    }

    /**
     * Add thermal printer barcode information section
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param divider - Divider line string
     */
    async addThermalBarcodeInfo(encoder: any, data: any, divider: string): Promise<void> {
        const companyCode = localStorage.getItem("company_code") || "INV";
        const barcodeData = `${companyCode}${String(data.id).padStart(6, '0')}`;

        encoder.align('center').text("INVOICE BARCODE").newline();

        try {
            // Try direct ESC/POS barcode command first
            encoder.align('center')
                .code128(barcodeData, 60, 2, 'below', 'a')
                .newline();
        } catch (directError) {
            try {
                // Fallback: Try with different barcode method
                encoder.align('center')
                    .barcode(barcodeData, 'code128', 60, 2, 'below', 'a')
                    .newline();
            } catch (barcodeError) {
                // Final fallback to text
                console.warn('Barcode generation failed, using text fallback:', barcodeError);
                encoder.align('center').text(barcodeData).newline();
            }
        }

        encoder.align('center').text("(Scan for invoice details)").newline();
        encoder.align('left').text(divider).newline();
    }

    /**
     * Add thermal printer UPI payment information section
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param divider - Divider line string
     */
    async addThermalUpiInfo(encoder: any, data: any, divider: string): Promise<void> {
        const merchantUpiId = localStorage.getItem("upi_id");

        if (merchantUpiId && (data.current_balance > 0 || data.bill_amount > 0)) {
            const amount = data.current_balance || data.bill_amount || 0;
            const merchantName = localStorage.getItem("company_name") || "Merchant";

            encoder.align('center').text("UPI PAYMENT").newline();
            encoder.align('center').text(`Pay Rs.${amount.toFixed(2)} to`).newline();
            encoder.align('center').text(merchantName).newline();

            // Generate UPI payment URL for QR code
            const upiUrl = this.generateUpiPaymentUrl(data);

            try {
                // Try direct ESC/POS QR code command first
                encoder.align('center')
                    .qrcode(upiUrl, 1, 'l', 'a')  // Size 1, error correction L, auto mode
                    .newline();
            } catch (directError) {
                try {
                    // Fallback: Try with different QR method
                    encoder.align('center')
                        .qr(upiUrl, 3)  // Alternative QR method with size 3
                        .newline();
                } catch (qrError) {
                    // Final fallback to text
                    console.warn('QR code generation failed, using text fallback:', qrError);
                    encoder.align('center').text(`UPI ID: ${merchantUpiId}`).newline();
                }
            }

            encoder.align('center').text("Scan QR with any UPI app").newline();
            encoder.align('left').text(divider).newline();
        }
    }

    async getHtmlContentForMobile(data) {
        let totalValue = this.getTotalValue(data)
        let totalWeight = totalValue.totalWeight,
            totalNo = totalValue.totalNo,
            totalAmount = totalValue.totalAmount;
        let item = data.sales_invoice_items.map((e, index) => {
            return `
                    <tr style="text-align:left;">
                        <td style="width:60%">${e.product_name}${e.hsn_code ? `<br><span style="font-size:10px; color:#666;">HSN: ${e.hsn_code}</span>` : ''}</td>
                        <td style="width:10%">${e.no}</td>
                        <td style="width:10%">${e.weight} </td>
                        <td style="width:20%">${e.line_total.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td style="width:25%">${e.mrp}</td>
                        <td style="width:25%">${(e.rate - (e.rate * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)}</td>
                        <td style="width:25%">${e.tax_rate}</td>
                        <td style="width:25%">${((e.line_total * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)}</td>
                    </tr>
                    `;
        });
        let items = item.join("");
        let totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;
        let taxDetails = this.getTaxDetails(data).taxDetails
        let tax_item = taxDetails.map(e => {
            return `<tr style="text-align:center">
            <td style="width:20%">${e.value}</td>
            <td style="width:40%">${e.taxable_amount.toFixed(2)}</td>
            <td style="width:20%">${e.tax_amount.toFixed(2)} </td>
            <td style="width:20%">${e.tax_amount.toFixed(2)}</td>
            </tr>`;
        });
        let tax_items = tax_item.join("");
        let html_content = `
                <!DOCTYPE html>
                <html lang="en">
                
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${data.name} - ${data.date}</title>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
                    <style>
                        @media print {
                            body {
                                width: 70mm;
                                /* height: 3276mm;*/
                                margin: 0.2mm 0.2mm 0.2mm 0.2mm;
                                /* change the margins as you want them to be. */
                            }
                        }
                
                        #options {
                            align-content: center;
                            align-items: center;
                            text-align: center;
                        }
                    </style>
                </head>
                
                <body>
                    <div style="text-align: center;word-wrap: break-word;">
                        <h4 style="padding:2px;margin: 3px;">${localStorage.getItem("company_name")}</h4>
                        <p style="margin: 3px;">
                        ${localStorage.getItem("address")}
                        </p>
                        <p style="margin: 3px;">GST NO:-${localStorage.getItem('gst_no')}</p>
                        <p style="margin: 3px;">FSSAI NO:-${localStorage.getItem('fssai_no')}</p>
                        <p style="margin: 3px;">${localStorage.getItem('contact_no_left')},${localStorage.getItem('contact_no_right')}</p>
                        <p style="margin: 3px;">*******************************</p>
                        <p style="margin: 3px;">${this.getInvoiceTitle(data)}</p>
                        <p style="margin: 3px;">*******************************</p>
                    </div>
                    <div>
                        <p style="margin: 3px;"><b style="float:left;">Bill No:-${data.id}</b> <b style="float:right;">Date:-${data.date}</b></p>
                        <p style="margin: 3px;text-align: center;font-weight:bold;">Customer Details</p>
                        <p style="margin: 3px;">Name:${data.name}</p>
                        <p style="margin: 3px;">Number:${data.phone_no}</p>
                        <p style="margin: 3px;">Place:${data.place}</p>
                        <p style="margin: 3px;">Place of supply:Tamilnadu (33)</p>
                        <p style="margin: 3px;">GST:${data.gst_no}</p>
                        <p style="margin: 3px;text-align: center;">*******************************</p>
                    </div>
                    <table>
                        <tr style="text-align:left;">
                            <th style="width:60%">Item</th>
                            <th style="width:10%">Qty</th>
                            <th style="width:10%">Pcs</th>
                            <th style="width:20%">Amount</th>
                        </tr>
                        <tr>
                            <th style="width:25%">Mrp</th>
                            <th style="width:25%">Rate</th>
                            <th style="width:25%">Tax%</th>
                            <th style="width:25%">Tax ₹</th>
                        </tr>
                        ${items}
                        <tr>
                            <th style="width:60%">Total</th>
                            <th style="width:10%">${totalNo}</th>
                            <th style="width:10%">${totalWeight} </th>
                            <th style="width:20%">₹${totalAmount}</th>
                        </tr>
                    </table>
                    <p style="margin: 3px;text-align: center;">*******************************</p>
                    <table>
                        <tr>
                            <th style="width:20%">Tax rate</th>
                            <th style="width:40%">Taxable</th>
                            <th style="width:20%">CGST</th>
                            <th style="width:20%">SGST</th>
                        </tr>
                        ${tax_items}
                    </table>
                    <p style="margin: 3px;text-align: center;">*******************************</p>
                    <table>
                        <tr>
                            <th style="width:60%">Subtotal</th>
                            <td style="width:40%">₹${(totalAmount - totalTaxAmount).toFixed(2)}</td>
                        </tr>
                        <tr>
                            <th style="width:60%">Tax Total</th>
                            <td style="width:40%">₹${totalTaxAmount.toFixed(2)}</td>
                        </tr>
                        <tr>
                            <th style="width:60%">Gross Total</th>
                            <td style="width:40%">₹${(data.gross_total || totalAmount).toFixed(2)}</td>
                        </tr>
                        <tr>
                            <th style="width:60%">Rounding Off</th>
                            <td style="width:40%">₹${((data.rounding_enabled && data.rounding_adjustment) ? (data.rounding_adjustment > 0 ? '+' : '') + data.rounding_adjustment.toFixed(2) : '0.00')}</td>
                        </tr>
                        <tr style="font-weight: bold;">
                            <th style="width:60%">Net Amount</th>
                            <td style="width:40%">₹${data.bill_amount.toFixed(2)}</td>
                        </tr>
                    </table>
                    <p style="margin: 3px;text-align: center;">*******************************</p>
                    <table>
                        <tr>
                            <th style="width:60%">Previous Balance</th>
                            <td style="width:10%">₹${data.previous_balance.toFixed(2)} </td>
                        </tr>
                        <tr>
                            <th style="width:60%">Bill Amount </th>
                            <td style="width:10%">₹${data.bill_amount.toFixed(2)}</td>
                        </tr>
                        <tr>
                            <th style="width:60%">Recieved Amount</th>
                            <td style="width:10%">₹${data.received_amount.toFixed(2)}</td>
                        </tr>
                        <tr>
                            <th style="width:60%">Closing Balance </th>
                            <td style="width:10%">₹${data.current_balance.toFixed(2)}</td>
                        </tr>
                    </table>
                    <p style="margin: 3px;text-align: center;">*******************************</p>
                    ${this.getStaffInfoForMobile(data)}
                    <!-- COMMENTED OUT - Barcode and QR code not working properly -->
                    <!-- ${await this.getInvoiceBarcodeHtml(data)} -->
                    <!-- ${await this.getUpiQrCodeHtml(data)} -->
                    <p style="margin: 3px;text-align: center;">Thank You</p>
                
                </body>
                </html>`;
        return html_content;

    }
    async getHtmlContentForWebA5(data) {
        let totalWeight = 0,
            totalNo = 0,
            totalAmount = 0;
        data.sales_invoice_items.forEach((element) => {
            if (element.weight) {
                totalWeight += element.weight;
            }
            if (element.no) {
                totalNo += element.no;
            }
            if (element.line_total) {
                totalAmount += element.line_total;
            }
        });
        let item = data.sales_invoice_items.map((e, index) => {
            let content = `<div class="header-item"
                                style=" width: 5%; font-size: 16px; padding: 3px;">
                                <strong>${index + 1} </strong>
                            </div>`;
            this.billing_field_settings.forEach(element => {
                switch (element.slug) {
                    case 'rate':
                        content += `<div class="header-item"
                                        style=" width: 10%; font-size: 16px; padding: 3px;">
                                        <strong> ${e.rate} </strong>
                                    </div>`
                        break;
                    case 'item':
                        content += `<div class="header-item"
                                        style=" width: 45%; font-size: 16px; padding: 3px;">
                                        <strong> ${e.product_name} </strong>${e.hsn_code ? `<br/><span style="font-size:12px; color:#666;">&emsp; HSN: ${e.hsn_code}</span>` : ''}${e.remarks ? `<br/><span style="font-size:18px;">&emsp; ${e.remarks}</span>` : ''}
                                    </div>`
                        break;
                    case 'pcs':
                        content += `<div class="header-item"
                                        style=" width: 14%; font-size: 16px; padding: 3px;">
                                        <strong> ${e.weight} </strong>
                                    </div>`
                        break;
                    case 'box':
                        content += ` <div class="header-item"
                                        style=" width: 8%; font-size: 16px; padding: 3px;">
                                        <strong> ${e.no} </strong>
                                    </div>`
                        break;
                    case 'total':
                        content += ` <div class="header-item"
                                        style=" width: 18%; font-size: 16px; padding: 3px;">
                                        <strong> ${e.line_total} </strong>
                                    </div>`
                        break;
                    default:
                        break;
                }
            });
            return content;
        });
        let header_content = ` <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 5%; font-size: 16px; padding: 3px;">
                                <strong> Sr. </strong>
                            </div>`;
        this.billing_field_settings.forEach(element => {
            switch (element.slug) {
                case 'rate':
                    header_content += `<div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 10%; font-size: 16px; padding: 3px;">
                                            <strong> ${element.displayName} </strong>
                                        </div>`
                    break;
                case 'item':
                    header_content += `<div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 45%; font-size: 16px; padding: 3px;">
                                            <strong> ${element.displayName}  </strong>
                                        </div>`
                    break;
                case 'pcs':
                    header_content += `<div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 14%; font-size: 16px; padding: 3px;">
                                            <strong> ${element.displayName} </strong>
                                        </div>`
                    break;
                case 'box':
                    header_content += `   <div class="header-item"
                                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 16px; padding: 3px;">
                                                <strong> ${element.displayName} </strong>
                                            </div>`
                    break;
                case 'total':
                    header_content += `  <div class="header-item"
                                            style="border-bottom:0.5px solid; width: 18%; font-size: 16px; padding: 3px;">
                                            <strong> ${element.displayName} </strong>
                                        </div>`
                    break;
                default:
                    break;
            }
        });
        let total_column = ` <div class="header-item"
                                style=" width: 5%; font-size: 16px; padding: 3px;">
                                <strong> &nbsp; </strong>
                            </div>`;
        this.billing_field_settings.forEach(element => {
            switch (element.slug) {
                case 'rate':
                    total_column += `<div class="header-item"
                                            style=" width: 10%; font-size: 16px; padding: 3px;">
                                            <strong> &nbsp; </strong>
                                        </div>`
                    break;
                case 'item':
                    total_column += `<div class="header-item"
                                            style=" width: 45%; font-size: 16px; padding: 3px;">
                                            <strong> Total </strong>
                                        </div>`
                    break;
                case 'pcs':
                    total_column += `<div class="header-item"
                                            style=" width: 14%; font-size: 16px; padding: 3px;">
                                            <strong> ${totalWeight} </strong>
                                        </div>`
                    break;
                case 'box':
                    total_column += `<div class="header-item"
                                            style=" width: 8%; font-size: 16px; padding: 3px;">
                                            <strong> ${totalNo} </strong>
                                        </div>`
                    break;
                case 'total':
                    total_column += `<div class="header-item"
                                            style="width: 18%; font-size: 16px; padding: 3px;">
                                            <strong> ${totalAmount} </strong>
                                        </div>`
                    break;
                default:
                    break;
            }
        });
        let customBillingItems = "";


        data.metadata.filter(item => item.value).forEach((element) => {
            switch (element.fieldType) {
                case 'discount':
                    customBillingItems += `<div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    ${element.displayName}(${element.operation == "add" ? "+" : "-"}) &emsp; (${element.percentage}%)</p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${element.value} </p>
                            </div>`
                    break;

                default:
                    customBillingItems += `<div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    ${element.displayName}(${element.operation == "add" ? "+" : "-"})</p>
                                </p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${element.value} </p>
                            </div>`;
                    break;
            }

        });
        let totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;
        let billing_items = `<div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    Subtotal</p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${(totalAmount - totalTaxAmount).toFixed(2)} </p>
                            </div>
                            <div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    Tax Total</p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${totalTaxAmount.toFixed(2)} </p>
                            </div>
                            <div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    Gross Total</p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${(data.gross_total || totalAmount).toFixed(2)} </p>
                            </div>`+ customBillingItems +
                            `<div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    Rounding Off</p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${((data.rounding_enabled && data.rounding_adjustment) ? (data.rounding_adjustment > 0 ? '+' : '') + data.rounding_adjustment.toFixed(2) : '0.00')} </p>
                            </div>` +
                            `<div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px; font-weight: bold;">
                                    Net Amount</p>
                                <p
                                    style="margin: 5px; font-size: 20px; font-weight:900;font-weight:1200em;width: 50%; text-align: left; padding-right: 10px;">
                                    : ${data.bill_amount.toFixed(2)}</p>
                            </div>`

        let items = item.join("");
        let html_content = `<!DOCTYPE html>
        <html>
        
        <head>
            <title>Invoice Print</title>
            <meta charset="UTF-8" />
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
            <style>
                * {
                    font-family: "arial";
                    width: 100%;
                    box-sizing: border-box;
                }
        
                .sheet {
        
                    page-break-after: auto !important;
                }
        
                html,
                body {
                    height: 100%;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden;
                }
        
                .page {
                    page-break-after: avoid;
                    page-break-inside: avoid;
                }
        
                .container {
                    display: table;
                }
        
                .container:nth-child(21) {
                    page-break-after: always;
                    page-break-inside: avoid;
                }
        
                .container {
                    font-size: 0;
                }
        
                .header-item {
                    display: inline-block;
                    font-size: 22px !important;
                }
        
                div {
                    box-sizing: border-box;
                }
        
                .page {
                    min-height: 600px;
                }
        
                @page {
                    size: A5;
        
                }
        
        
                @media print {
        
                    body {
                        margin: 0;
                        zoom: 65%;
                    }
        
        
        
                    thead {
                        display: table-header-group;
                    }
        
                    tfoot {
                        display: table-footer-group;
                    }
                }
            </style>
        </head>
        
        <body class="A5">
            <table class="sheet padding-10mm" cellspacing="0" cellpadding="0" width="100%" style="width: 100%;">
                <thead>
                    <tr>
                        <td width="100%">
                            <div style="display: flex;width: 100%;height:70%;">
                                <div style="width:auto;"> <img
                                        src="${environment.apiUrlClean + localStorage.getItem("company_logo")}"
                                        style="width: 125px;height: 140px;">
                                </div>
                                <div style="widht:100%;height:100%;margin-top: -25px;text-align: center;">
                                    <div style="display:flex;"><h4 style="padding:0;text-align: start;">Phone No:-${localStorage.getItem('contact_no_left')}</h4><h4 style="padding:0;text-align: right;">Phone No:-${localStorage.getItem('contact_no_right')}</h4></div>
                                    <h1 style="padding: 0px;margin: 0px;">${localStorage.getItem("company_name")}</h1>
                                    <h4>${localStorage.getItem("address")}</h4>
                                </div>
                            </div>
                            <div style="display: flex;">
                                <div
                                    style="width: 70%;margin-top: 3px; display: flex; flex-direction: column; border-top: 0.5px solid;border-left: 0.5px solid;border-bottom:none; border-right:none;">
                                    <p style="margin: 0; font-size: 25px;font-weight: 300;">To</p>
                                    <h1 style="margin: 0; font-size: 30px;">${data.name}</h1>
                                    <div style="display:flex;">
                                        <!--  <h4 style="margin: 0; font-size: 25px;font-weight: 300;">
                                                <strong> Phone : </strong> ${data.phone_no}
                                            </h4> -->
                                        <h4 style="margin: 0; font-size: 25px;font-weight: 300;">
                                            ${data.place}
                                        </h4>
                                    </div>
                                </div>
                                <div style="width: 30%;margin-top: 3px; padding: 10px; border-top: 0.5px solid;border-left: 0.5px solid;border-right: 0.5px solid; border-bottom:none; display: flex;"
                                    align="center">
                                    <div class="content" style="text-align: left; width: 100%;">
                                        <p style="font-size: 25px;font-weight: 300;margin: 0;">
                                            No &emsp;: ${data.id}
                                        </p>
                                        <p style="font-size: 25px;font-weight: 300;margin: 0;">
                                            Date &nbsp;: ${data.date}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="page" style="border: 0.5px solid;position:relative;padding-bottom:30px">
                                <div class="container" style="width: 100%;">
                                   ${header_content}
                                </div>
                                <div class="container" style="width: 100%;">
                                    ${items}
                                </div>
                                <div class="container" style="width: 100%;position:absolute;bottom:0;left:0;border-top:0.5px solid;border-left:0.5px solid;border-right:0.5px solid; ">
                                  ${total_column}
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td>
                            <div id="page-footer">
                                <div style="display: flex;">
                                    <div
                                        style="display: flex; flex-direction: column; width: 20%; border: 0.5px solid; padding-top: 5px;border-top:none;border-right:none;">
                                          <div style="display: flex;">
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: left; padding-left: 10px;">
                                                    Remarks:</p>
                                            </div>
                                          <div style="display: flex;">
                                                <p
                                                    style="margin: 5px; font-size: 14px; width: 50%; text-align: left; padding-right: 10px;">${data.remarks ? data.remarks : ""}
                                                </p>
                                            </div>
                                            ${this.getStaffInfoForA5(data)}
                                    </div>
                                    <div style="display: flex; flex-direction: column; width: 35%;  padding-top: 5px;border-bottom:0.5px solid">
                                     <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 16px; width: 50%; text-align: right; padding-left: 10px;">
                                                Previous Balance(முன் பாக்கி)</p>
                                            <p
                                                style="margin: 5px; font-size: 16px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                                : ${data.previous_balance} </p>
                                        </div>
                                        <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 16px; width: 50%; text-align: right; padding-left: 10px;">
                                                Net Amount</p>
                                            <p
                                                style="margin: 5px; font-size: 16px; font-weight:900;font-weight:1200em;width: 50%; text-align: left; padding-right: 10px;">
                                                : ${data.bill_amount}</p>
                                        </div>
                                        <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 16px; width: 50%; text-align: right; padding-left: 10px;">
                                                Received Amount(வரவு)</p>
                                            <p
                                                style="margin: 5px; font-size: 16px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                                : ${data.received_amount}</p>
                                        </div>
                                        <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 16px; width: 30%; text-align: left; padding-left: 10px;">
                                                Closing Balance(பாக்கி)</p>
                                            <p
                                                style="margin: 5px;  font-size: 18px;font-weight:900;width: 70%; text-align: right; padding-right: 10px;">
                                               <b>${data.current_balance}</b> </p>
                                        </div>
                                    </div>
                                    <div
                                        style="display: flex; flex-direction: column; width: 45%; border: 0.5px solid; padding-top: 5px;border-top:none;border-left:none;">
                                       ${billing_items}
                                       <!-- COMMENTED OUT - Barcode and QR code not working properly -->
                                       <!-- ${await this.getInvoiceBarcodeHtml(data)} -->
                                       <!-- ${await this.getUpiQrCodeHtml(data)} -->
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </body>
        
        </html>`;

        return html_content;
    }

    
    async getHtmlContentForWebA4(data) {
        let totalValue = this.getTotalValue(data)
        let totalWeight = totalValue.totalWeight,
            totalNo = totalValue.totalNo,
            totalAmount = totalValue.totalAmount;
        let totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;
        let taxDetails = this.getTaxDetails(data).taxDetails
        let tax_item = taxDetails.map(e => {
            return `<div style="display: flex;">
                            <p
                                style="margin: 2px; font-size: 20px; width: 20%; text-align: right; padding-left: 10px;">
                                ${e.value}</p>
                            <p
                                style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                ${e.taxable_amount.toFixed(2)}</p>
                            <p
                                style="margin: 5px; font-size: 20px; width: 30%; text-align: right; padding-left: 10px;">
                                ${(e.tax_amount / 2).toFixed(2)}</p>
                            <p
                                style="margin: 5px; font-size: 20px; width: 30%; text-align: right; padding-left: 10px;">
                                ${(e.tax_amount / 2).toFixed(2)}</p>
                        </div>`;
        });
        let item = data.sales_invoice_items.map((e, index) => {
            let content = `<div class="header-item"
                                style=" width: 4%; font-size: 16px; padding: 3px;">
                                <strong>${index + 1} </strong>
                            </div>`;
            this.billing_field_settings.forEach((element) => {

                switch (element.slug) {
                    case 'rate':
                        content += `<div class="header-item"
                                        style=" width: 8%; font-size: 9px; padding: 3px;text-align: right;align-items: center;">
                                        ${(e.rate - (e.rate * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)} 
                                    </div>`
                        break;
                    case 'item':
                        content += `<div class="header-item"
                                    style=" width: 32%; font-size: 9px; padding: 3px;text-align: left;">
                                    ${e.product_name}${e.hsn_code ? `<br/><span style="font-size:16px; color:#666;">&emsp; HSN: ${e.hsn_code}</span>` : ''}${e.remarks ? `<br/><span style="font-size:18px;">&emsp; ${e.remarks}</span>` : ''}
                                </div>`
                        break;
                    case 'pcs':
                        content += `<div class="header-item"
                                        style=" width: 8%; font-size: 9px; padding: 3px;text-align: right;align-items: center;">
                                        ${e.weight} 
                                    </div>`
                        break;
                    case 'box':
                        content += ` <div class="header-item"
                                        style=" width: 8%; font-size: 9px; padding: 3px;text-align: right;align-items: center;">
                                    ${e.no}
                                    </div>`
                        break;
                    case 'total':
                        content += `<div class="header-item"
                                        style=" width: 14%; font-size: 9px; padding: 3px;text-align: right;align-items: center;">
                                        ${e.line_total.toFixed(2)}
                                    </div>`
                        break;
                    case 'mrp':
                        content += `<div class="header-item"
                                        style=" width: 7%; font-size: 9px; padding: 3px;text-align: right;align-items: center;">
                                        ${e.mrp}
                                    </div>`
                        break;
                    case 'tax_rate':
                        content += `<div class="header-item"
                                        style=" width: 7%; font-size: 9px; padding: 3px;text-align: right;align-items: center;">
                                        ${e.tax_rate}
                                    </div>`
                        break;
                    case 'tax_amount':
                        content += `<div class="header-item"
                                        style=" width: 12%; font-size: 9px; padding: 3px;text-align: right;align-items: center;">
                                        ${((e.line_total * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)}
                                    </div>`
                        break;
                    default:
                        break;
                }
            });
            return content;
        });
        let header_content = ` <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 4%; font-size: 16px; padding: 3px;">
                                <strong> Sr. </strong>
                            </div>`;
        this.billing_field_settings.forEach(element => {
            switch (element.slug) {
                case 'rate':
                    header_content += `<div class="header-item"
                                        style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName} </strong>
                                    </div>`
                    break;
                case 'item':
                    header_content += `<div class="header-item"
                                        style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 32%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName} </strong>
                                    </div>`
                    break;
                case 'pcs':
                    header_content += `<div class="header-item"
                                        style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName} </strong>
                                    </div>`
                    break;
                case 'box':
                    header_content += ` <div class="header-item"
                                        style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName}  </strong>
                                    </div>`
                    break;
                case 'total':
                    header_content += `<div class="header-item"
                                        style="border-bottom:0.5px solid; width: 14%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName} </strong>
                                    </div>`
                    break;
                case 'mrp':
                    header_content += `<div class="header-item"
                                        style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 7%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName} </strong>
                                    </div>`
                    break;
                case 'tax_rate':
                    header_content += `<div class="header-item"
                                        style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 7%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName} </strong>
                                    </div>`
                    break;
                case 'tax_amount':
                    header_content += `<div class="header-item"
                                        style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 12%; font-size: 10px; padding: 3px;">
                                        <strong> ${element.displayName}</strong>
                                    </div>`
                    break;
                default:
                    break;
            }
        });

        let total_column = ``;
        this.billing_field_settings.forEach(element => {
            switch (element.slug) {
                case 'rate':
                    total_column += `<div class="header-item"
                                            style=" width: 8%; font-size: 10px; padding: 3px;">
                                            <strong> &nbsp; </strong>
                                        </div>`
                    break;
                case 'item':
                    total_column += `<div class="header-item"
                                            style=" width: 32%; font-size: 10px; padding: 3px;">
                                            <strong> Total </strong>
                                        </div>`
                    break;
                case 'pcs':
                    total_column += `<div class="header-item"
                                            style=" width: 8%; font-size: 10px; padding: 3px;text-align:right;">
                                            <strong> ${totalWeight} </strong>
                                        </div>`
                    break;
                case 'box':
                    total_column += ` <div class="header-item"
                                            style=" width: 8%; font-size: 10px; padding: 3px;text-align:right;">
                                            <strong> ${totalNo} </strong>
                                        </div>`
                    break;
                case 'total':
                    total_column += `<div class="header-item"
                                            style="width: 14%; font-size: 10px; padding: 3px;text-align:right;">
                                            <strong> ${totalAmount.toFixed(2)} </strong>
                                        </div>`
                    break;
                case 'mrp':
                    total_column += `<div class="header-item"
                                            style=" width: 7%; font-size: 10px; padding: 3px;">
                                            <strong> &nbsp;</strong>
                                        </div>`
                    break;
                case 'tax_rate':
                    total_column += `<div class="header-item"
                                            style=" width: 7%; font-size: 10px; padding: 3px;">
                                            <strong> &nbsp;</strong>
                                        </div>`
                    break;
                case 'tax_amount':
                    total_column += ` <div class="header-item"
                                            style=" width: 12%; font-size: 10px; padding: 3px;text-align:right;">
                                            <strong>  ${totalTaxAmount.toFixed(2)}</strong>
                                        </div>`
                    break;
                default:
                    break;
            }
        });

        let customBillingItems = "";
        let headerdata = data?.headerdata?.filter(e => e.value ).map(e => {
            return `<div style="display:flex;"><h4 style="margin: 0; font-size: 20px; font-weight: 300;">
                                            ${e.name}:-${e.value}
                                        </h4> </div>`
        })
        let headerdatas = headerdata ? headerdata?.join(""): ``;

        data?.metadata?.filter(item => item.value).forEach((element) => {
            switch (element.fieldType) {
                case 'discount':
                    customBillingItems += `<div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    ${element.displayName}(${element.operation == "add" ? "+" : "-"}) &emsp; (${element.percentage}%)</p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${element.value} </p>
                            </div>`
                    break;

                default:
                    customBillingItems += `<div style="display: flex;">
                                <p
                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                    ${element.displayName}(${element.operation == "add" ? "+" : "-"})</p>
                                <p
                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${element.value} </p>
                            </div>`;
                    break;
            }

        });
        let billing_items = ` <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Subtotal</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${(totalAmount - totalTaxAmount).toFixed(2)} </p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Tax Total</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${totalTaxAmount.toFixed(2)} </p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Gross Total</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${(data.gross_total || totalAmount).toFixed(2)} </p>
                                </div>`+ customBillingItems +
                                `${data.rounding_enabled && data.rounding_adjustment !== 0 ? `
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Rounding Off</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${(data.rounding_adjustment > 0 ? '+' : '') + data.rounding_adjustment.toFixed(2)} </p>
                                </div>` : ''}
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px; font-weight: bold;">
                                        Net Amount</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${data.bill_amount.toFixed(2)}</p>
                                </div>`
        let items = item.join("");

        let tax_items = tax_item.join("");
        let html_content = `<html>

        <head>
            <title>${data.name} - ${data.date}</title>
            <meta charset="UTF-8">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
            <style>
                * {
                    font-family: "arial";
                    width: 100%;
                    box-sizing: border-box;
                }
        
                .sheet {
        
                    page-break-after: auto !important;
                }
        
                html,
                body {
                    height: 100%;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden;
                }
        
                .page {
                    page-break-after: avoid;
                    page-break-inside: avoid;
                    font-size: smaller;
                }
        
                .container {
                    display: table;
                }
        
                .container:nth-child(21) {
                    page-break-after: always;
                    page-break-inside: avoid;
                }
        
                .container {
                    font-size: 0;
                }
        
                .header-item {
                    display: inline-block;
                    font-size: 22px !important;
                }
        
                div {
                    box-sizing: border-box;
                }
        
                .page {
                    min-height: 280mm;
                    max-height: 280mm;
                }
        
                @page {
                    size: A4;
        
                }
        
        
                @media print {
        
                    body {
                        margin: 0;
                        zoom: 65%;
                    }
        
        
        
                    thead {
                        display: table-header-group;
                    }
        
                    tfoot {
                        display: table-footer-group;
                    }
                }
            </style>
        </head>
        
        <body>
        
        
            <table class="sheet padding-10mm" cellspacing="0" cellpadding="0" width="100%" style="width: 100%;">
                <thead>
                    <tr>
                        <td width="100%">
                            <div style="display: flex;width: 100%;height:70%;">
                                <div style="width:auto;"> <img src="${environment.apiUrlClean + localStorage.getItem("company_logo")}" style="width: 125px;height: 140px;">
                                </div>
                                <div style="widht:100%;height:100%;margin-top: -25px;text-align: center;">
                                    <div style="display:flex;">
                                        <h5 style="padding:0;text-align: start; font-size: 25px;font-weight: 300;">Phone
                                            No:-${localStorage.getItem('contact_no_left')}</h5>
                                        <h5 style="padding:0;text-align: center;font-weight:bolder; font-size: 25px;font-weight: 300;">${this.getInvoiceTitle(data)}</h5>
                                        <h5 style="padding:0;text-align: right; font-size: 25px;font-weight: 300;">Phone
                                            No:-${localStorage.getItem('contact_no_right')}</h5>
                                    </div>
                                    <h5 style="padding: 0px;margin: 0px;font-weight:bolder; font-size: 30px;font-weight: 300;">
                                        ${localStorage.getItem("company_name")}</h5>
                                    <h5 style="padding: 0px;margin: 0px;font-size: 25px;font-weight: 300;">${localStorage.getItem("address")}</h5>
                                    <h5 style="font-size: 25px;font-weight: 300;margin: 5px;">GST NO:- ${localStorage.getItem('gst_no')} &emsp;FSSAI
                                        NO:-${localStorage.getItem('fssai_no')}</h6>
                                </div>
                            </div>
                            <div style="display: flex;">
                            <div style="width: 55%; margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid; border-bottom:none; border-right:none; justify-content:center;">
                                <div style="display:flex; flex-direction: column;">
                                    <p style="margin: 0; font-size: 25px; font-weight: 300;">To</p>
                                    <h1 style="margin: 0; font-size: 30px;">${data.name}</h1>
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 25px; font-weight: 300;">
                                            <strong>Phone:</strong> ${data.phone_no}
                                        </h4>
                                    </div>
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 25px; font-weight: 300;">
                                            <strong>Address:</strong> ${data.place}
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 35%; margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid; border-bottom:none; border-right:none; justify-content:center;">
                                <div style="display:flex; flex-direction: column;">
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 20px; font-weight: 300;">
                                            Place Of Supply: Tamilnadu(33)
                                        </h4>
                                    </div>
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 20px; font-weight: 300;">
                                            GST No:-${data.gst_no}
                                        </h4>
                                    </div>
                                    ${headerdatas}
                                </div>
                            </div>
                            <div style="width: 30%; margin-top: 3px; padding: 10px; border: 0.5px solid; border-bottom:none; display: flex;" align="center">
                                <div class="content" style="text-align: left; width: 100%;">
                                    <p style="font-size: 25px; font-weight: 300; margin: 0;">
                                        No : ${data.id}
                                    </p>
                                    <p style="font-size: 25px; font-weight: 300; margin: 0;">
                                        Date: ${data.date}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
        
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="page"
                                style="border: 0.5px solid;position:relative;padding-bottom:30px;font-size: smaller;">
                                <div class="container" style="width: 100%;">
                                    ${header_content}
                                </div>
                                <div class="container" style="width: 100%;">
                                    ${items}
                                </div>
                                <div class="container"
                                    style="width: 100%;position:absolute;bottom:${data.remarks ? '32px' : '0'};left:0;border-top: 0.5px solid;">
                                   ${total_column}
                                </div>
                                 ${data.remarks ? `<div class="container"
                                    style="width: 100%;position:absolute;bottom:0;left:0;border-top: 0.5px solid;">
                                    <div class="header-item"
                                        style=" width: 100%; font-size: 10px; padding: 3px;text-align:left;">
                                        <strong> Remarks:</strong> ${data.remarks}
                                    </div>
                                </div>` : ''}
                                
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td>
                            <div id="page-footer">
                                <div style="display: flex;">
                                    <div
                                        style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-right:none;">
                                        <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                Opening Balance</p>
                                            <p
                                                style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                 ${data.previous_balance.toFixed(2)} </p>
                                        </div>
                                        <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                Bill value</p>
                                            <p
                                                style="margin: 5px; font-size: 20px; font-weight:900;font-weight:1200em;width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                 ${data.bill_amount.toFixed(2)}</p>
                                        </div>
                                        <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                Received Amount</p>
                                            <p
                                                style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                 ${data.received_amount.toFixed(2)}</p>
                                        </div>
                                        <div style="display: flex;">
                                            <p
                                                style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                Closing Balance</p>
                                            <p
                                                style="margin: 5px; font-size: 25px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                 <b>${data.current_balance.toFixed(2)}</b> </p>
                                        </div>
                                    </div>

                                        <div
                                            style="display: flex; flex-direction: column; width: 40%; border: 0.5px solid; padding-top: 5px;border-top:none;">
                                            <div style="display: flex;border-bottom:0.5px solid;">
                                                <p
                                                    style="margin: 2px; font-size: 20px; width: 20%; text-align: right; padding-left: 10px;">
                                                    Tax Rate</p>
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                                    Taxable Amount</p>
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 30%; text-align: right; padding-left: 10px;">
                                                    CGST</p>
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 30%; text-align: right; padding-left: 10px;">
                                                    SGST</p>
                                            </div>
                                            ${tax_items}
                                        </div>
                                        <div
                                            style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-left:none;">
                                            ${billing_items}
                                            <!-- COMMENTED OUT - Barcode and QR code not working properly -->
                                            <!-- ${await this.getInvoiceBarcodeHtml(data)} -->
                                            <!-- ${await this.getUpiQrCodeHtml(data)} -->
                                            <div style="display: flex;">
                                                <p
                                                    style="margin: 5px; font-size: 20px;  text-align: center; padding-left: 10px; border-top: 0.5px solid;">
                                                    Authorized Sign</p>

                                            </div>
                                        </div>
        
                                    </div>
                                </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
        
        
        </body>
        
        </html>`
        return html_content;
    }
    async getHtmlContent(data) {

        let paperSize = localStorage.getItem('paper_size');
        console.log('Paper size from settings:', paperSize);
        let htmlContent;

        // Respect paper size settings regardless of device type
        // Only use mobile format for thermal printing or when explicitly no paper size is set
        switch (paperSize) {
            case "A4":
                htmlContent = await this.getHtmlContentForWebA4(data);
                break;
            case "A5":
                htmlContent = await this.getHtmlContentForWebA5(data);
                break;
            case "58mm":
            case "80mm":
                // For thermal paper sizes, use mobile format
                htmlContent = await this.getHtmlContentForMobile(data);
                break;
            default:
                // Default to A4 for regular printing
                htmlContent = await this.getHtmlContentForWebA4(data);
                break;
        }

        return htmlContent;
    }
    async print(data, isMobile = false, filename = '') {
        console.log('Print called with isMobile:', isMobile);
        let paperType = localStorage.getItem('paper_type');
        let paperSize = localStorage.getItem('paper_size');

        console.log('Paper settings - Type:', paperType, 'Size:', paperSize);

        // Use thermal printing for thermal paper types (58mm, 80mm) or when explicitly set to Thermal
        if (this.util.isAndroid() && (paperType === 'Thermal' || paperSize === '58mm' || paperSize === '80mm')) {
            console.log('Using thermal printing');
            await this.printBillSlip(data);
        } else if (this.util.isCordova() && paperType !== 'Thermal') {
            // Use Capacitor printer plugin for regular printing on Cordova/Capacitor
            console.log('Using Capacitor printer plugin');
            const htmlContent = await this.getHtmlContent(data);
            await this.printDocument(htmlContent, filename);
        } else {
            // Fallback to browser printing
            console.log('Using browser printing');
            const htmlContent = await this.getHtmlContent(data);
            this.printHtmlDocument(htmlContent);
        }
    }
    async printBillSlip(data: any): Promise<void> {
        let paperSize = localStorage.getItem('paper_size');
        const maxCharsPerLine = this.getMaxCharsPerLine(paperSize);
        const divider = '*'.repeat(maxCharsPerLine);
        const equalLength = maxCharsPerLine / 2;
        const threeEqualLength = maxCharsPerLine / 3;
        const fourEqualsLength = maxCharsPerLine / 4;

        function padString(input, length) {
            // Convert input to a string to ensure .padEnd works correctly
            const str = String(input);
            if (str.length > length) {
                // Subtract 3 to accommodate the length of "..."
                return str.slice(0, length - 3) + '...';
            } else {
                // Pad the string if it's shorter than the desired length
                return str.padEnd(length, ' ');
            }
        }
        const encoder = new EscPosEncoder();
        encoder.initialize();

        encoder.align('center')
            .bold(true)
            .text(localStorage.getItem("company_name")).newline()
            .bold(true) // Reset bold for the address
            .text(localStorage.getItem("address")).newline()
            .bold(true) // Re-apply bold for the following lines
            .text("GST:" + localStorage.getItem("gst_no")).newline()
            .text("FSSAI:" + localStorage.getItem("fssai_no")).newline()
            .text("Phone No:" + localStorage.getItem("contact_no_left") + "," + localStorage.getItem("contact_no_right")).newline()
            .text(divider).newline()
            .align('center').text(this.getInvoiceTitle(data)).bold(true).newline()
            .align('left')
            .bold(true) // Keep items like Bill No. bold
            .text("Bill No:-" + data.id).newline()
            .text("Bill Date:-" + data.date).newline()
            .bold(false) // Reset bold for customer details
            .text(divider).newline()
            .text("Customer Details").newline()
            .bold(true) // Make customer name bold
            .text("Name:-" + data.name).newline()
            .bold(false) // Reset bold for the remaining details
            .text("Number:-" + data.phone_no).newline()
            .text("Place:-" + data.place).newline()
            .text("Place of supply:- Tamilnadu (33)").newline()
            .text("GST:-" + data.gst_no).newline()
            .text(divider).newline()
            .bold(true);

        // Table headers
        const headers = [
            { text: 'Item', width: threeEqualLength + (threeEqualLength / 2) + (threeEqualLength / 3) },
            { text: 'Box', width: threeEqualLength / 3 },
            { text: 'Pcs', width: threeEqualLength / 3 },
            { text: 'Amt', width: (threeEqualLength / 2) + (threeEqualLength / 3) },
            { text: 'Rate', width: fourEqualsLength },
            { text: 'MRP', width: fourEqualsLength },
            { text: 'Tax', width: fourEqualsLength },
            { text: 'TAmt', width: fourEqualsLength }, // Abbreviated "Tax Amt" to "TAmt"
            // Abbreviated "Amount" to "Amt"
        ];

        // Align headers
        encoder.align('left').text(headers.map(header => padString(header.text, header.width)).join('')).newline();

        // Divider
        encoder.align('left').text(divider).newline(); // Adjust based on the width of your receipt

        data.sales_invoice_items.forEach(item => {
            encoder.align('left').text(
                padString(item.product_name, headers[0].width) +
                padString(item.no, headers[1].width) +
                padString(item.weight, headers[2].width) +
                padString(item.line_total.toFixed(2), headers[3].width) +
                padString((item.rate - (item.rate * item.tax_rate) / (100 + item.tax_rate)).toFixed(2), headers[4].width) +
                padString(item.mrp, headers[5].width) +
                padString(item.tax_rate, headers[6].width) +
                padString(((item.line_total * item.tax_rate) / (100 + item.tax_rate)).toFixed(2), headers[7].width)

            ).newline();
        });

        let totalValue = this.getTotalValue(data)
        let totalWeight = totalValue.totalWeight,
            totalNo = totalValue.totalNo,
            totalAmount = totalValue.totalAmount;

        encoder.align('left').text(
            padString("Total", fourEqualsLength) +
            padString("Box" + totalNo, fourEqualsLength) +
            padString("Pcs" + totalWeight, fourEqualsLength) +
            padString(totalAmount, fourEqualsLength)
        ).newline();
        encoder.align('left').text(divider).newline(); // Adjust based on the width of your receipt
        let totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;
        let taxDetails = this.getTaxDetails(data).taxDetails;

        const col1Width = fourEqualsLength / 2; // Tax Type/Description
        const col2Width = fourEqualsLength + (fourEqualsLength / 2); // Taxable Amount
        const col3Width = fourEqualsLength; // Tax Amount
        const col4Width = fourEqualsLength; // Tax Amount

        encoder.align('center').text("Tax Details").newline();
        encoder.align('left').text(padString("Type", col1Width) + padString("Taxable", col2Width) + padString("CGST", col3Width) + padString("SGST", col4Width)).newline();
        // encoder.align('left').text(divider).newline(); // Divider line

        // Format and add each tax detail line
        taxDetails.forEach(e => {
            encoder.align('left').text(
                padString(e.value, col1Width) +
                padString(e.taxable_amount.toFixed(2), col2Width) +
                padString((e.tax_amount / 2).toFixed(2), col3Width) +
                padString((e.tax_amount / 2).toFixed(2), col4Width)
            ).newline();
        });
        encoder.align('left').text(divider).newline(); // Divider line
        encoder.align('left').text(padString("Subtotal", equalLength) + padString((totalAmount - totalTaxAmount).toFixed(2), equalLength)).newline();
        encoder.align('left').text(padString("Tax Total", equalLength) + padString(totalTaxAmount.toFixed(2), equalLength)).newline();
        encoder.align('left').text(padString("Gross Total", equalLength) + padString((data.gross_total || totalAmount).toFixed(2), equalLength)).newline();

        // Add rounding line - always show as it's mandatory to mention
        const roundingAmount = (data.rounding_enabled && data.rounding_adjustment) ? data.rounding_adjustment : 0;
        encoder.align('left').text(padString("Rounding Off", equalLength) + padString((roundingAmount > 0 ? '+' : '') + roundingAmount.toFixed(2), equalLength)).newline();

        encoder.align('left').text(padString("Net Amount", equalLength) + padString(data.bill_amount.toFixed(2), equalLength)).newline();

        encoder.align('left').text(divider).newline(); // Divider line

        encoder.align('center').text("Additional Details").newline();
        encoder.align('left').text(padString("Opening Balance", equalLength) + padString(this.centerText(data.previous_balance.toFixed(2), equalLength), equalLength)).newline();
        encoder.align('left').text(padString("Bill Value", equalLength) + padString(this.centerText(data.bill_amount.toFixed(2), equalLength), equalLength)).newline();
        encoder.align('left').text(padString("Received Amount", equalLength) + padString(this.centerText(data.received_amount.toFixed(2), equalLength), equalLength)).newline();
        encoder.align('left').text(padString("Closing Balance", equalLength) + padString(this.centerText(data.current_balance.toFixed(2), equalLength), equalLength)).newline();
        encoder.align('left').text(divider).newline(); // Divider line

        // Add staff information
        this.addThermalStaffInfo(encoder, data, divider);

        // Add barcode information - COMMENTED OUT (not working properly)
        // await this.addThermalBarcodeInfo(encoder, data, divider);

        // Add UPI payment information - COMMENTED OUT (not working properly)
        // await this.addThermalUpiInfo(encoder, data, divider);

        encoder.align('center') // Center the following text
            .text("Thank You for Your Order!") // Your Thank You message
            .newline() // Your Thank You message
            .newline().newline().newline();

        encoder.newline().cut('full');
        const resultByte = encoder.encode();

        this.bluetoothService.printToDevice(resultByte)

    }

    /**
     * Print collection receipt (payment receipt) with mode logic
     * Supports thermal, mobile, and A4 formats based on settings
     * @param data - Invoice data with payment information
     * @param paymentData - Specific payment data for the receipt
     * @param isMobile - Force mobile format (optional)
     * @param filename - Filename for saving (optional)
     */
    async printCollectionReceipt(data: any, paymentData?: any, isMobile = false, filename = '') {
        const receiptData = {
            ...data,
            payment: paymentData || {
                amount: data.received_amount || 0,
                mode_of_payment: 'Cash',
                balance: data.current_balance || 0,
                date: new Date().toLocaleDateString()
            }
        };

        console.log('Collection Receipt print called with isMobile:', isMobile);
        let paperType = localStorage.getItem('paper_type');
        let paperSize = localStorage.getItem('paper_size');

        console.log('Collection Receipt paper settings - Type:', paperType, 'Size:', paperSize);

        // Use thermal printing for thermal paper types (58mm, 80mm) or when explicitly set to Thermal
        if (this.util.isAndroid() && (paperType === 'Thermal' || paperSize === '58mm' || paperSize === '80mm')) {
            console.log('Using thermal printing for collection receipt');
            this.printCollectionReceiptThermal(receiptData);
        } else if (this.util.isCordova() && paperType !== 'Thermal') {
            // Use Capacitor printer plugin for regular printing on Cordova/Capacitor
            console.log('Using Capacitor printer plugin for collection receipt');
            const htmlContent = await this.getCollectionReceiptHtmlContent(receiptData);
            await this.printDocument(htmlContent, filename || 'Collection-Receipt');
        } else {
            // Fallback to browser printing
            console.log('Using browser printing for collection receipt');
            const htmlContent = await this.getCollectionReceiptHtmlContent(receiptData);
            this.printHtmlDocument(htmlContent);
        }
    }

    /**
     * Get collection receipt HTML content based on paper size settings
     * @param data - Receipt data
     * @returns HTML string for collection receipt
     */
    async getCollectionReceiptHtmlContent(data: any): Promise<string> {
        let paperSize = localStorage.getItem('paper_size');
        console.log('Collection receipt paper size from settings:', paperSize);

        // Determine format based on paper size
        if (paperSize === 'A4') {
            return this.getCollectionReceiptA4Html(data);
        } else if (paperSize === 'A5') {
            return this.getCollectionReceiptA5Html(data);
        } else {
            // Default to mobile/thermal style for other sizes
            return this.getCollectionReceiptHtml(data);
        }
    }

    /**
     * Generate HTML content for collection receipt (mobile/thermal style)
     * @param data - Receipt data
     * @returns HTML string for collection receipt
     */
    getCollectionReceiptHtml(data: any): string {
        const companyName = localStorage.getItem("company_name");
        const address = localStorage.getItem("address");
        const gstNo = localStorage.getItem('gst_no');
        const contactLeft = localStorage.getItem('contact_no_left');
        const contactRight = localStorage.getItem('contact_no_right');

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Collection Receipt - ${data.name}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        font-size: 14px;
                        line-height: 1.4;
                    }
                    .receipt-container {
                        max-width: 400px;
                        margin: 0 auto;
                        border: 2px solid #000;
                        padding: 20px;
                    }
                    .header {
                        text-align: center;
                        border-bottom: 2px solid #000;
                        padding-bottom: 15px;
                        margin-bottom: 20px;
                    }
                    .company-name {
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 5px;
                    }
                    .receipt-title {
                        font-size: 16px;
                        font-weight: bold;
                        margin: 15px 0;
                        text-decoration: underline;
                    }
                    .receipt-details {
                        margin: 20px 0;
                    }
                    .detail-row {
                        display: flex;
                        justify-content: space-between;
                        margin: 10px 0;
                        padding: 5px 0;
                        border-bottom: 1px dotted #ccc;
                    }
                    .detail-label {
                        font-weight: bold;
                        width: 60%;
                    }
                    .detail-value {
                        width: 40%;
                        text-align: right;
                    }
                    .amount-received {
                        font-size: 16px;
                        font-weight: bold;
                        background-color: #f0f0f0;
                        padding: 8px;
                        margin: 15px 0;
                    }
                    .signature-section {
                        margin-top: 40px;
                        border-top: 2px solid #000;
                        padding-top: 20px;
                    }
                    .signature-box {
                        border: 1px solid #000;
                        height: 60px;
                        margin: 10px 0;
                        position: relative;
                    }
                    .signature-label {
                        position: absolute;
                        bottom: -20px;
                        left: 0;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 30px;
                        font-size: 12px;
                        color: #666;
                    }
                    @media print {
                        body { margin: 0; }
                        .receipt-container { border: none; }
                    }
                </style>
            </head>
            <body>
                <div class="receipt-container">
                    <!-- Header -->
                    <div class="header">
                        <div class="company-name">${companyName}</div>
                        <div>${address}</div>
                        <div>GST NO: ${gstNo}</div>
                        <div>${contactLeft}, ${contactRight}</div>
                        <div class="receipt-title">COLLECTION RECEIPT</div>
                    </div>

                    <!-- Receipt Details -->
                    <div class="receipt-details">
                        <div class="detail-row">
                            <span class="detail-label">Receipt No:</span>
                            <span class="detail-value">CR-${data.id}-${Date.now().toString().slice(-6)}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Date:</span>
                            <span class="detail-value">${data.payment.date}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Invoice No:</span>
                            <span class="detail-value">${data.id}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Customer Name:</span>
                            <span class="detail-value">${data.name}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Customer Phone:</span>
                            <span class="detail-value">${data.phone_no}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Customer Address:</span>
                            <span class="detail-value">${data.place}</span>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="amount-received">
                        <div class="detail-row" style="border: none; margin: 0;">
                            <span class="detail-label">Amount Received:</span>
                            <span class="detail-value">${data.payment.amount > 0 ? '₹' + data.payment.amount.toFixed(2) : '₹___________'}</span>
                        </div>
                    </div>

                    <div class="receipt-details">
                        <div class="detail-row">
                            <span class="detail-label">Mode of Payment:</span>
                            <span class="detail-value">${data.payment.mode_of_payment}</span>
                        </div>

                        <div class="detail-row">
                            <span class="detail-label">Balance if any:</span>
                            <span class="detail-value">${data.payment.balance > 0 ? '₹' + data.payment.balance.toFixed(2) : '₹___________'}</span>
                        </div>
                    </div>

                    <!-- Signature Section -->
                    <div class="signature-section">
                        <div>
                            <strong>Signature of the Retailer:</strong>
                        </div>
                        <div class="signature-box">
                            <div class="signature-label">Name: _________________________</div>
                        </div>

                        <div style="margin-top: 40px;">
                            <strong>Customer Signature:</strong>
                        </div>
                        <div class="signature-box">
                            <div class="signature-label">Name: ${data.name}</div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="footer">
                        <p>Thank you for your payment!</p>
                        <p>This is a computer-generated receipt.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Print collection receipt to thermal printer
     * @param data - Receipt data
     */
    printCollectionReceiptThermal(data: any): void {
        let paperSize = localStorage.getItem('paper_size');
        const maxCharsPerLine = this.getMaxCharsPerLine(paperSize);
        const divider = '*'.repeat(maxCharsPerLine);
        const equalLength = maxCharsPerLine / 2;

        function padString(input, length) {
            const str = String(input);
            if (str.length > length) {
                return str.slice(0, length - 3) + '...';
            } else {
                return str.padEnd(length, ' ');
            }
        }

        const encoder = new EscPosEncoder();
        encoder.initialize();

        // Header
        encoder.align('center')
            .text(localStorage.getItem("company_name"))
            .newline()
            .text(localStorage.getItem("address"))
            .newline()
            .text(`GST NO: ${localStorage.getItem('gst_no')}`)
            .newline()
            .text(`${localStorage.getItem('contact_no_left')}, ${localStorage.getItem('contact_no_right')}`)
            .newline()
            .text(divider)
            .newline()
            .text("COLLECTION RECEIPT")
            .newline()
            .text(divider)
            .newline();

        // Receipt details
        const receiptNo = `CR-${data.id}-${Date.now().toString().slice(-6)}`;
        encoder.align('left')
            .text(padString("Receipt No:", equalLength) + padString(receiptNo, equalLength))
            .newline()
            .text(padString("Date:", equalLength) + padString(data.payment.date, equalLength))
            .newline()
            .text(padString("Invoice No:", equalLength) + padString(data.id.toString(), equalLength))
            .newline()
            .text(padString("Customer:", equalLength) + padString(data.name, equalLength))
            .newline()
            .text(padString("Phone:", equalLength) + padString(data.phone_no, equalLength))
            .newline()
            .text(padString("Address:", equalLength) + padString(data.place, equalLength))
            .newline()
            .text(divider)
            .newline();

        // Payment information
        encoder.align('center')
            .text("PAYMENT DETAILS")
            .newline()
            .text(divider)
            .newline();

        // Payment details - Handle zero amounts by leaving blank for manual entry
        const amountReceivedText = data.payment.amount > 0 ? `Rs.${data.payment.amount.toFixed(2)}` : "Rs.___________";
        const balanceText = data.payment.balance > 0 ? `Rs.${data.payment.balance.toFixed(2)}` : "Rs.___________";

        encoder.align('left')
            .text(padString("Amount Received:", equalLength) + padString(amountReceivedText, equalLength))
            .newline()
            .text(padString("Mode of Payment:", equalLength) + padString(data.payment.mode_of_payment, equalLength))
            .newline()
            .text(padString("Balance if any:", equalLength) + padString(balanceText, equalLength))
            .newline()
            .text(divider)
            .newline();

        // Signature section
        encoder.align('center')
            .text("SIGNATURES")
            .newline()
            .text(divider)
            .newline();

        encoder.align('left')
            .text("Retailer Signature:")
            .newline()
            .newline()
            .text("Name: _________________")
            .newline()
            .newline()
            .text("Customer Signature:")
            .newline()
            .newline()
            .text(`Name: ${data.name}`)
            .newline()
            .text(divider)
            .newline();

        // Footer
        encoder.align('center')
            .text("Thank you for your payment!")
            .newline()
            .text("This is a computer-generated receipt.")
            .newline()
            .newline()
            .newline();

        encoder.newline().cut('full');
        const resultByte = encoder.encode();

        this.bluetoothService.printToDevice(resultByte);
    }

    /**
     * Generate thermal-style HTML for collection receipt (for non-Android devices)
     * @param data - Receipt data
     * @returns HTML string formatted like thermal receipt
     */
    getCollectionReceiptThermalStyleHtml(data: any): string {
        const companyName = localStorage.getItem("company_name");
        const address = localStorage.getItem("address");
        const gstNo = localStorage.getItem('gst_no');
        const contactLeft = localStorage.getItem('contact_no_left');
        const contactRight = localStorage.getItem('contact_no_right');

        // Handle zero amounts by leaving blank for manual entry
        const amountReceivedText = data.payment.amount > 0 ? `Rs.${data.payment.amount.toFixed(2)}` : "Rs.___________";
        const balanceText = data.payment.balance > 0 ? `Rs.${data.payment.balance.toFixed(2)}` : "Rs.___________";
        const receiptNo = `CR-${data.id}-${Date.now().toString().slice(-6)}`;

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Collection Receipt - ${data.name}</title>
                <style>
                    body {
                        font-family: 'Courier New', monospace;
                        margin: 0;
                        padding: 20px;
                        font-size: 12px;
                        line-height: 1.2;
                        max-width: 300px;
                        margin: 0 auto;
                    }
                    .receipt-container {
                        border: 1px solid #000;
                        padding: 10px;
                    }
                    .center {
                        text-align: center;
                    }
                    .left {
                        text-align: left;
                    }
                    .divider {
                        border-top: 1px dashed #000;
                        margin: 8px 0;
                    }
                    .row {
                        display: flex;
                        justify-content: space-between;
                        margin: 2px 0;
                    }
                    .bold {
                        font-weight: bold;
                    }
                    .signature-section {
                        margin-top: 20px;
                        margin-bottom: 10px;
                    }
                    @media print {
                        body { margin: 0; padding: 10px; }
                        .receipt-container { border: none; }
                    }
                </style>
            </head>
            <body>
                <div class="receipt-container">
                    <!-- Header -->
                    <div class="center bold">
                        ${companyName}<br>
                        ${address}<br>
                        GST NO: ${gstNo}<br>
                        ${contactLeft}, ${contactRight}
                    </div>

                    <div class="divider"></div>

                    <div class="center bold">COLLECTION RECEIPT</div>

                    <div class="divider"></div>

                    <!-- Receipt Details -->
                    <div class="left">
                        <div class="row">
                            <span>Receipt No:</span>
                            <span>${receiptNo}</span>
                        </div>
                        <div class="row">
                            <span>Date:</span>
                            <span>${data.payment.date}</span>
                        </div>
                        <div class="row">
                            <span>Invoice No:</span>
                            <span>${data.id}</span>
                        </div>
                        <div class="row">
                            <span>Customer:</span>
                            <span>${data.name}</span>
                        </div>
                        <div class="row">
                            <span>Phone:</span>
                            <span>${data.phone_no}</span>
                        </div>
                        <div class="row">
                            <span>Address:</span>
                            <span>${data.place}</span>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <div class="center bold">PAYMENT DETAILS</div>

                    <div class="divider"></div>

                    <!-- Payment Information -->
                    <div class="left">
                        <div class="row">
                            <span>Amount Received:</span>
                            <span>${amountReceivedText}</span>
                        </div>
                        <div class="row">
                            <span>Mode of Payment:</span>
                            <span>${data.payment.mode_of_payment}</span>
                        </div>
                        <div class="row">
                            <span>Balance if any:</span>
                            <span>${balanceText}</span>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <div class="center bold">SIGNATURES</div>

                    <div class="divider"></div>

                    <!-- Signature Section -->
                    <div class="signature-section">
                        <div class="left">
                            Retailer Signature:<br><br>
                            Name: _________________<br><br>
                            Customer Signature:<br><br>
                            Name: ${data.name}
                        </div>
                    </div>

                    <div class="divider"></div>

                    <!-- Footer -->
                    <div class="center">
                        Thank you for your payment!<br>
                        This is a computer-generated receipt.
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Generate A4 collection receipt HTML (landscape orientation)
     * @param data - Receipt data
     * @returns HTML string for A4 collection receipt
     */
    getCollectionReceiptA4Html(data: any): string {
        const companyName = localStorage.getItem("company_name") || "Company Name";
        const companyAddress = localStorage.getItem("company_address") || "";
        const companyPhone = localStorage.getItem("company_phone") || "";
        const companyGstin = localStorage.getItem("company_gstin") || "";

        const amountReceivedText = data.payment.amount ? `₹${data.payment.amount.toFixed(2)}` : '₹0.00';
        const balanceText = data.payment.balance ? `₹${data.payment.balance.toFixed(2)}` : '₹0.00';

        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Collection Receipt</title>
                <style>
                    @page {
                        size: A4 landscape;
                        margin: 15mm;
                    }
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        font-size: 14px;
                        line-height: 1.4;
                        width: 100%;
                        max-width: 1000px;
                    }
                    .header {
                        text-align: center;
                        border-bottom: 2px solid #000;
                        padding-bottom: 10px;
                        margin-bottom: 15px;
                    }
                    .company-name {
                        font-size: 24px;
                        font-weight: bold;
                        margin: 0;
                    }
                    .company-details {
                        font-size: 12px;
                        margin: 5px 0;
                    }
                    .receipt-title {
                        font-size: 20px;
                        font-weight: bold;
                        margin: 10px 0;
                        text-decoration: underline;
                    }
                    .content {
                        display: flex;
                        justify-content: space-between;
                        margin: 20px 0;
                    }
                    .left-section, .right-section {
                        width: 48%;
                    }
                    .info-row {
                        display: flex;
                        justify-content: space-between;
                        margin: 8px 0;
                        padding: 5px 0;
                        border-bottom: 1px dotted #ccc;
                    }
                    .label {
                        font-weight: bold;
                        width: 40%;
                    }
                    .value {
                        width: 60%;
                        text-align: right;
                    }
                    .payment-section {
                        border: 2px solid #000;
                        padding: 15px;
                        margin: 20px 0;
                        background-color: #f9f9f9;
                    }
                    .payment-title {
                        font-size: 18px;
                        font-weight: bold;
                        text-align: center;
                        margin-bottom: 15px;
                    }
                    .signature-section {
                        display: flex;
                        justify-content: space-between;
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 1px solid #000;
                    }
                    .signature-box {
                        width: 45%;
                        text-align: center;
                    }
                    .signature-line {
                        border-bottom: 1px solid #000;
                        height: 50px;
                        margin-bottom: 10px;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 30px;
                        font-size: 12px;
                        font-style: italic;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 class="company-name">${companyName}</h1>
                    <div class="company-details">${companyAddress}</div>
                    <div class="company-details">Phone: ${companyPhone} | GSTIN: ${companyGstin}</div>
                    <div class="receipt-title">COLLECTION RECEIPT</div>
                </div>

                <div class="content">
                    <div class="left-section">
                        <div class="info-row">
                            <span class="label">Receipt No:</span>
                            <span class="value">${data.id}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Date:</span>
                            <span class="value">${data.payment.date}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Customer Name:</span>
                            <span class="value">${data.name}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Customer Code:</span>
                            <span class="value">${data.code || 'N/A'}</span>
                        </div>
                    </div>

                    <div class="right-section">
                        <div class="info-row">
                            <span class="label">Invoice No:</span>
                            <span class="value">${data.id}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Invoice Date:</span>
                            <span class="value">${data.date}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Invoice Amount:</span>
                            <span class="value">₹${data.bill_amount.toFixed(2)}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Previous Balance:</span>
                            <span class="value">₹${(data.bill_amount - (data.payment.amount || 0)).toFixed(2)}</span>
                        </div>
                    </div>
                </div>

                <div class="payment-section">
                    <div class="payment-title">PAYMENT DETAILS</div>
                    <div class="info-row">
                        <span class="label">Amount Received:</span>
                        <span class="value" style="font-size: 18px; font-weight: bold;">${amountReceivedText}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Mode of Payment:</span>
                        <span class="value">${data.payment.mode_of_payment}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Balance if any:</span>
                        <span class="value" style="font-size: 16px; font-weight: bold;">${balanceText}</span>
                    </div>
                </div>

                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div>Retailer Signature</div>
                        <div style="margin-top: 10px;">Name: _________________</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div>Customer Signature</div>
                        <div style="margin-top: 10px;">Name: ${data.name}</div>
                    </div>
                </div>

                <div class="footer">
                    <p>Thank you for your payment! This is a computer-generated receipt.</p>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Generate A5 collection receipt HTML
     * @param data - Receipt data
     * @returns HTML string for A5 collection receipt
     */
    getCollectionReceiptA5Html(data: any): string {
        const companyName = localStorage.getItem("company_name") || "Company Name";
        const companyAddress = localStorage.getItem("company_address") || "";
        const companyPhone = localStorage.getItem("company_phone") || "";
        const companyGstin = localStorage.getItem("company_gstin") || "";

        const amountReceivedText = data.payment.amount ? `₹${data.payment.amount.toFixed(2)}` : '₹0.00';
        const balanceText = data.payment.balance ? `₹${data.payment.balance.toFixed(2)}` : '₹0.00';

        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Collection Receipt</title>
                <style>
                    @page {
                        size: A5;
                        margin: 10mm;
                    }
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        font-size: 12px;
                        line-height: 1.3;
                    }
                    .header {
                        text-align: center;
                        border-bottom: 2px solid #000;
                        padding-bottom: 8px;
                        margin-bottom: 12px;
                    }
                    .company-name {
                        font-size: 18px;
                        font-weight: bold;
                        margin: 0;
                    }
                    .company-details {
                        font-size: 10px;
                        margin: 3px 0;
                    }
                    .receipt-title {
                        font-size: 16px;
                        font-weight: bold;
                        margin: 8px 0;
                        text-decoration: underline;
                    }
                    .info-row {
                        display: flex;
                        justify-content: space-between;
                        margin: 6px 0;
                        padding: 3px 0;
                        border-bottom: 1px dotted #ccc;
                    }
                    .label {
                        font-weight: bold;
                        width: 50%;
                    }
                    .value {
                        width: 50%;
                        text-align: right;
                    }
                    .payment-section {
                        border: 1px solid #000;
                        padding: 10px;
                        margin: 15px 0;
                        background-color: #f9f9f9;
                    }
                    .payment-title {
                        font-size: 14px;
                        font-weight: bold;
                        text-align: center;
                        margin-bottom: 10px;
                    }
                    .signature-section {
                        margin-top: 25px;
                        padding-top: 15px;
                        border-top: 1px solid #000;
                    }
                    .signature-box {
                        margin: 15px 0;
                        text-align: left;
                    }
                    .signature-line {
                        border-bottom: 1px solid #000;
                        height: 30px;
                        margin-bottom: 5px;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 20px;
                        font-size: 10px;
                        font-style: italic;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 class="company-name">${companyName}</h1>
                    <div class="company-details">${companyAddress}</div>
                    <div class="company-details">Phone: ${companyPhone} | GSTIN: ${companyGstin}</div>
                    <div class="receipt-title">COLLECTION RECEIPT</div>
                </div>

                <div class="info-row">
                    <span class="label">Receipt No:</span>
                    <span class="value">${data.id}</span>
                </div>
                <div class="info-row">
                    <span class="label">Date:</span>
                    <span class="value">${data.payment.date}</span>
                </div>
                <div class="info-row">
                    <span class="label">Customer Name:</span>
                    <span class="value">${data.name}</span>
                </div>
                <div class="info-row">
                    <span class="label">Customer Code:</span>
                    <span class="value">${data.code || 'N/A'}</span>
                </div>
                <div class="info-row">
                    <span class="label">Invoice No:</span>
                    <span class="value">${data.id}</span>
                </div>
                <div class="info-row">
                    <span class="label">Invoice Date:</span>
                    <span class="value">${data.date}</span>
                </div>
                <div class="info-row">
                    <span class="label">Invoice Amount:</span>
                    <span class="value">₹${data.bill_amount.toFixed(2)}</span>
                </div>

                <div class="payment-section">
                    <div class="payment-title">PAYMENT DETAILS</div>
                    <div class="info-row">
                        <span class="label">Amount Received:</span>
                        <span class="value" style="font-size: 14px; font-weight: bold;">${amountReceivedText}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Mode of Payment:</span>
                        <span class="value">${data.payment.mode_of_payment}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Balance if any:</span>
                        <span class="value" style="font-size: 12px; font-weight: bold;">${balanceText}</span>
                    </div>
                </div>

                <div class="signature-section">
                    <div class="signature-box">
                        <div>Retailer Signature:</div>
                        <div class="signature-line"></div>
                        <div>Name: _________________</div>
                    </div>
                    <div class="signature-box">
                        <div>Customer Signature:</div>
                        <div class="signature-line"></div>
                        <div>Name: ${data.name}</div>
                    </div>
                </div>

                <div class="footer">
                    <p>Thank you for your payment!<br>This is a computer-generated receipt.</p>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Generate UPI payment URL for QR code
     * @param data - Invoice data
     * @returns UPI payment URL string
     */
    generateUpiPaymentUrl(data: any): string {
        const merchantName = localStorage.getItem("company_name") || "Merchant";
        const merchantUpiId = localStorage.getItem("upi_id");

        // Return empty string if UPI ID is not configured
        if (!merchantUpiId) {
            return '';
        }

        const amount = data.current_balance || data.bill_amount || 0;
        const transactionNote = `Payment for Invoice ${data.id}`;

        // UPI URL format: upi://pay?pa=<UPI_ID>&pn=<PAYEE_NAME>&am=<AMOUNT>&tn=<TRANSACTION_NOTE>&cu=INR
        const upiUrl = `upi://pay?pa=${encodeURIComponent(merchantUpiId)}&pn=${encodeURIComponent(merchantName)}&am=${amount}&tn=${encodeURIComponent(transactionNote)}&cu=INR`;

        return upiUrl;
    }

    /**
     * Generate QR code data URL for UPI payment
     * @param data - Invoice data
     * @param size - QR code size (default: 150x150 for compact thermal printing)
     * @returns Promise<string> - Data URL of QR code image
     */
    async generateUpiQrCode(data: any, size: string = '150x150'): Promise<string> {
        const upiUrl = this.generateUpiPaymentUrl(data);

        // Use QR Server API to generate compact QR code for thermal printing
        const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}&data=${encodeURIComponent(upiUrl)}`;

        return qrApiUrl;
    }

    /**
     * Get UPI QR code HTML for inclusion in bills
     * @param data - Invoice data
     * @returns HTML string with QR code
     */
    async getUpiQrCodeHtml(data: any): Promise<string> {
        const merchantUpiId = localStorage.getItem("upi_id");

        // Return empty string if UPI ID is not configured
        if (!merchantUpiId) {
            return '';
        }

        const qrCodeUrl = await this.generateUpiQrCode(data);
        const merchantName = localStorage.getItem("company_name") || "Merchant";
        const amount = data.current_balance || data.bill_amount || 0;

        return `
            <div style="text-align: center; margin: 10px 0; padding: 8px; border: 1px solid #000;">
                <h4 style="margin: 0 0 5px 0; font-size: 14px; font-weight: bold;">Pay via UPI</h4>
                <img src="${qrCodeUrl}" alt="UPI QR Code" style="width: 100px; height: 100px; margin: 5px 0;" />
                <div style="font-size: 10px; margin-top: 5px;">
                    <p style="margin: 1px 0;"><strong>₹${amount.toFixed(2)}</strong></p>
                    <p style="margin: 1px 0; font-size: 9px;">${merchantUpiId}</p>
                    <p style="margin: 2px 0; font-size: 9px; color: #666;">Scan with any UPI app</p>
                </div>
            </div>
        `;
    }
    async printDocument(htmlContent: string, filename: string = '') {
        try {
            // Use Capacitor printer plugin for native printing
            await Printer.print({
                content: htmlContent,
                name: filename || 'Invoice',
                orientation: 'portrait'
            });
            console.log('Print successful');
        } catch (error) {
            console.error('Print failed:', error);
            // Fallback to browser-based printing
            this.printHtmlDocument(htmlContent);
        }
    }
    printHtmlDocument(htmlContent) {

        let script = document.createElement("script");
        var win = window.open(
            "",
            "Title",
            "toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=1280,height=780,top=" +
            768 +
            ",left=" +
            1024
        );
        win.document.body.innerHTML = htmlContent

        setTimeout(() => {
            win.print();
            win.close();
        }, 2000);
    }

    getInvoiceTitle(data: any): string {
        if (data.delivery_challan) {
          return 'Delivery Challan';
        } else if (data.invoice_status === 'order') {
          return 'Sales Order';
        } else {
          return 'Tax Invoice';
        }
      }

    // Enhanced Report Printing Methods
    generateEnhancedReportHTML(title: string, dateRange: string, tableContent: string, additionalInfo?: string): string {
        const companyName = localStorage.getItem("company_name") || "Company Name";
        const currentDate = new Date().toLocaleDateString('en-IN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>${title}</title>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        color: #333;
                        line-height: 1.6;
                        background-color: #fff;
                    }

                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 3px solid #007bff;
                        padding-bottom: 20px;
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border-radius: 8px;
                        padding: 20px;
                    }

                    .company-name {
                        font-size: 28px;
                        font-weight: 700;
                        color: #007bff;
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }

                    .report-title {
                        font-size: 20px;
                        font-weight: 600;
                        color: #495057;
                        margin: 8px 0;
                    }

                    .date-info {
                        font-size: 14px;
                        color: #6c757d;
                        margin: 4px 0;
                    }

                    .additional-info {
                        font-size: 16px;
                        color: #28a745;
                        font-weight: 500;
                        margin: 8px 0;
                    }

                    .content-wrapper {
                        margin-top: 20px;
                    }

                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                        font-size: 13px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        overflow: hidden;
                    }

                    th {
                        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                        color: white;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        padding: 12px 8px;
                        text-align: center;
                        border: none;
                        font-size: 12px;
                    }

                    td {
                        padding: 10px 8px;
                        border-bottom: 1px solid #dee2e6;
                        vertical-align: middle;
                    }

                    tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }

                    tr:hover {
                        background-color: #e3f2fd;
                    }

                    .total-row {
                        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
                        color: white !important;
                        font-weight: 700;
                        font-size: 14px;
                    }

                    .total-row td {
                        border-bottom: none;
                        padding: 12px 8px;
                    }

                    .amount-cell {
                        text-align: right;
                        font-weight: 600;
                        color: #007bff;
                    }

                    .number-cell {
                        text-align: center;
                        font-weight: 500;
                    }

                    .name-cell {
                        font-weight: 600;
                        color: #495057;
                    }

                    .debit-amount {
                        color: #dc3545;
                        font-weight: 600;
                    }

                    .credit-amount {
                        color: #28a745;
                        font-weight: 600;
                    }

                    .remarks-col {
                        max-width: 200px;
                        word-wrap: break-word;
                        font-size: 12px;
                    }

                    .footer {
                        margin-top: 30px;
                        text-align: center;
                        font-size: 12px;
                        color: #6c757d;
                        border-top: 1px solid #dee2e6;
                        padding-top: 15px;
                    }

                    @media print {
                        body {
                            margin: 0;
                            padding: 15px;
                            font-size: 12px;
                        }
                        .header {
                            page-break-after: avoid;
                            background: #f8f9fa !important;
                            -webkit-print-color-adjust: exact;
                        }
                        table {
                            page-break-inside: avoid;
                            font-size: 11px;
                        }
                        th {
                            background: #007bff !important;
                            color: white !important;
                            -webkit-print-color-adjust: exact;
                        }
                        .total-row {
                            background: #28a745 !important;
                            color: white !important;
                            -webkit-print-color-adjust: exact;
                        }
                        tr:nth-child(even) {
                            background-color: #f8f9fa !important;
                            -webkit-print-color-adjust: exact;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 class="company-name">${companyName}</h1>
                    <h2 class="report-title">${title}</h2>
                    ${dateRange ? `<p class="date-info">Date Range: ${dateRange}</p>` : ''}
                    ${additionalInfo ? `<p class="additional-info">${additionalInfo}</p>` : ''}
                    <p class="date-info">Generated on: ${currentDate}</p>
                </div>

                <div class="content-wrapper">
                    ${tableContent}
                </div>

                <div class="footer">
                    <p>This is a computer-generated report from ${companyName}</p>
                </div>
            </body>
            </html>
        `;
    }

    async printEnhancedReport(htmlContent: string, filename: string) {
        this.util.isCordova() ?
            await this.printDocument(htmlContent, filename) :
            this.printHtmlDocument(htmlContent);
    }

    /**
     * Generate barcode for invoice
     * @param data - Invoice data
     * @param format - Barcode format (code128, code39, ean13, etc.)
     * @returns Promise<string> - URL of barcode image
     */
    async generateInvoiceBarcode(data: any, format: string = 'code128'): Promise<string> {
        // Create barcode data - using invoice ID with company prefix
        const companyCode = localStorage.getItem("company_code") || "INV";
        const barcodeData = `${companyCode}${String(data.id).padStart(6, '0')}`;

        // Use barcode API to generate barcode
        const barcodeApiUrl = `https://barcode.tec-it.com/barcode.ashx?data=${encodeURIComponent(barcodeData)}&code=${format.toUpperCase()}&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0`;

        return barcodeApiUrl;
    }

    /**
     * Get barcode HTML for inclusion in bills
     * @param data - Invoice data
     * @returns HTML string with barcode
     */
    async getInvoiceBarcodeHtml(data: any): Promise<string> {
        const barcodeUrl = await this.generateInvoiceBarcode(data);
        const companyCode = localStorage.getItem("company_code") || "INV";
        const barcodeData = `${companyCode}${String(data.id).padStart(6, '0')}`;

        return `
            <div style="text-align: center; margin: 8px 0; padding: 5px; border: 1px solid #ccc;">
                <img src="${barcodeUrl}" alt="Invoice Barcode" style="height: 40px; margin: 3px 0;" />
                <div style="font-size: 9px; margin-top: 2px; font-family: monospace;">
                    ${barcodeData}
                </div>
            </div>
        `;
    }

    /**
     * Set company code for barcode generation
     * @param companyCode - Company code prefix for barcodes
     */
    setCompanyCode(companyCode: string): void {
        localStorage.setItem("company_code", companyCode);
    }

    /**
     * Get company code for barcode generation
     * @returns Company code or null if not set
     */
    getCompanyCode(): string | null {
        return localStorage.getItem("company_code");
    }

    /**
     * Test the Capacitor printer plugin with a simple HTML document
     * This method can be called from the browser console for testing
     */
    async testCapacitorPrinter(): Promise<void> {
        const testHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Capacitor Printer Test</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    h1 { color: #333; }
                    .test-content { margin: 20px 0; }
                </style>
            </head>
            <body>
                <h1>Capacitor Printer Plugin Test</h1>
                <div class="test-content">
                    <p><strong>Test Date:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Plugin:</strong> @bcyesil/capacitor-plugin-printer</p>
                    <p><strong>Status:</strong> Successfully integrated with KingBill app</p>
                </div>
                <hr>
                <p>This is a test document to verify that the Capacitor printer plugin is working correctly.</p>
                <p>If you can see this printed document, the integration was successful!</p>
            </body>
            </html>
        `;

        try {
            console.log('🖨️ Testing Capacitor printer plugin...');
            await this.printDocument(testHtml, 'Capacitor-Printer-Test');
            console.log('✅ Capacitor printer test completed successfully');
        } catch (error) {
            console.error('❌ Capacitor printer test failed:', error);
        }
    }

    /**
     * Cross-platform print method - automatically selects best printing method
     */
    async printInvoiceCrossPlatform(data: InvoiceData, format: 'thermal' | 'a4' | 'a5' | 'mobile' = 'thermal'): Promise<void> {
        try {
            if (this.platform.is('ios')) {
                // iOS: Use AirPrint for all formats
                await this.printViaAirPrint(data, format);
            } else if (this.platform.is('android')) {
                // Android: Use Bluetooth for thermal, AirPrint for others
                if (format === 'thermal') {
                    await this.printViaBluetooth(data);
                } else {
                    await this.printViaAirPrint(data, format);
                }
            } else {
                // Web: Use browser printing
                await this.printViaAirPrint(data, format);
            }
        } catch (error) {
            console.error('Cross-platform printing failed:', error);
            throw error;
        }
    }

    /**
     * Print via AirPrint (iOS) or Capacitor Printer (Android/Web)
     */
    private async printViaAirPrint(data: InvoiceData, format: string): Promise<void> {
        let htmlContent: string;

        switch (format) {
            case 'a4':
                htmlContent = await this.getHtmlContentForWebA4(data);
                break;
            case 'a5':
                htmlContent = await this.getHtmlContentForWebA5(data);
                break;
            case 'mobile':
            case 'thermal':
                htmlContent = await this.getHtmlContentForMobile(data);
                break;
            default:
                htmlContent = await this.getHtmlContentForWebA5(data);
        }

        await Printer.print({
            content: htmlContent,
            orientation: format === 'a4' ? 'landscape' : 'portrait'
        });
    }

    /**
     * Print via Bluetooth (Android thermal printers)
     */
    private async printViaBluetooth(data: InvoiceData): Promise<void> {
        // Check if device is connected
        const selectedDevice = this.bluetoothService.getSelectedDevice();
        if (!selectedDevice) {
            throw new Error('No Bluetooth printer selected. Please select a printer first.');
        }

        // Generate thermal print data
        const printData = await this.generateThermalPrintData(data);

        // Send to printer
        this.bluetoothService.printToDevice(printData);
    }

    /**
     * Generate thermal print data for cross-platform use
     */
    private async generateThermalPrintData(data: InvoiceData): Promise<Uint8Array> {
        const encoder = new EscPosEncoder();
        const paperWidth = 58; // mm
        const maxCharsPerLine = this.getMaxCharsPerLine(paperWidth);
        const divider = '='.repeat(maxCharsPerLine);

        // Use existing thermal printing logic
        encoder
            .align('center')
            .size('normal')
            .text(localStorage.getItem("company_name") || "Company Name")
            .newline()
            .text(localStorage.getItem("address") || "Address")
            .newline()
            .text(`GST NO: ${localStorage.getItem('gst_no') || 'N/A'}`)
            .newline()
            .text(`FSSAI NO: ${localStorage.getItem('fssai_no') || 'N/A'}`)
            .newline()
            .text(`${localStorage.getItem('contact_no_left') || ''}, ${localStorage.getItem('contact_no_right') || ''}`)
            .newline()
            .text(divider)
            .newline()
            .text(this.getInvoiceTitle(data))
            .newline()
            .text(divider)
            .newline();

        // Customer details
        encoder
            .align('left')
            .text(`Bill No: ${data.id}`)
            .align('right')
            .text(`Date: ${data.date}`)
            .newline()
            .align('left')
            .text(`Name: ${data.name}`)
            .newline()
            .text(`Phone: ${data.phone_no}`)
            .newline()
            .text(`Place: ${data.place}`)
            .newline();

        if (data.gst_no) {
            encoder.align('left').text(`GST: ${data.gst_no}`).newline();
        }

        encoder.align('left').text(divider).newline();

        // Items
        encoder
            .align('left').text('Item'.padEnd(20) + 'Qty'.padStart(5) + 'Pcs'.padStart(5) + 'Amt'.padStart(8))
            .newline()
            .align('left').text(divider)
            .newline();

        let totalQty = 0;
        let totalPcs = 0;
        let totalAmount = 0;

        data.sales_invoice_items.forEach(item => {
            const itemName = item.product_name.length > 20 ?
                item.product_name.substring(0, 17) + '...' :
                item.product_name;

            encoder
                .align('left').text(itemName.padEnd(20) +
                      item.no.toString().padStart(5) +
                      item.weight.toString().padStart(5) +
                      item.line_total.toFixed(2).padStart(8))
                .newline();

            if (item.hsn_code) {
                encoder.align('left').text(`  HSN: ${item.hsn_code}`).newline();
            }

            totalQty += item.no;
            totalPcs += item.weight;
            totalAmount += item.line_total;
        });

        // Totals and summary
        encoder
            .align('left').text(divider)
            .newline()
            .align('left').text('Total'.padEnd(20) +
                  totalQty.toString().padStart(5) +
                  totalPcs.toString().padStart(5) +
                  totalAmount.toFixed(2).padStart(8))
            .newline()
            .align('left').text(divider)
            .newline();

        // Add tax details, bill summary, staff info, etc.
        const taxDetails = this.getTaxDetails(data);

        encoder
            .align('left').text(`Subtotal: ${(totalAmount - taxDetails.totalTaxAmount).toFixed(2)}`)
            .newline()
            .align('left').text(`Tax Total: ${taxDetails.totalTaxAmount.toFixed(2)}`)
            .newline()
            .align('left').text(`Net Amount: ${data.bill_amount.toFixed(2)}`)
            .newline()
            .align('left').text(divider)
            .newline()
            .align('left').text(`Previous Balance: ${data.previous_balance.toFixed(2)}`)
            .newline()
            .align('left').text(`Received: ${data.received_amount.toFixed(2)}`)
            .newline()
            .align('left').text(`Balance: ${data.current_balance.toFixed(2)}`)
            .newline()
            .align('left').text(divider)
            .newline();

        // Staff info
        this.addThermalStaffInfo(encoder, data, divider);

        // Footer
        encoder
            .align('center')
            .text('Thank You')
            .newline()
            .newline()
            .cut();

        return encoder.encode();
    }

    /**
     * Check if platform supports Bluetooth printing
     */
    isBluetoothPrintingSupported(): boolean {
        return this.platform.is('android') || (this.platform.is('ios') && 'bluetooth' in navigator);
    }

    /**
     * Check if platform supports AirPrint
     */
    isAirPrintSupported(): boolean {
        return this.platform.is('ios') || this.platform.is('android');
    }

}
