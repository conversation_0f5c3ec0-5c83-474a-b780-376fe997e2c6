import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class PurchaseInvoiceService {
  
  constructor(private http: HttpClient) {}

  deletePurchaseInvoice(data) {
    return this.http
      .delete(`${environment.apiUrl}/purchase_invoice/?purchase_invoice_id=${data}`)
      .toPromise();
  }
  getPurchaseInvoice(date){
    return this.http
      .get(`${environment.apiUrl}/purchase_invoice/?list=${date}`)
      .toPromise();
  }
  getPurchaseInvoiceById(id){
    return this.http
      .get(`${environment.apiUrl}/purchase_invoice/?invoice_id=${id}`)
      .toPromise();
  }
  editPurchaseInvoice(data) {
    return this.http
      .put(`${environment.apiUrl}/purchase_invoice/`, data)
      .toPromise();
  }
  createPurchaseInvoice(){
    return this.http
    .get(`${environment.apiUrl}/purchase_invoice/?create_invoice=true`)
    .toPromise();
  }
  savePurchaseInvoice(data) {
    return this.http
      .post(`${environment.apiUrl}/purchase_invoice/`, data)
      .toPromise();
  }
  getInvoiceForSelectedSupplier(id) {
    return this.http
      .get(`${environment.apiUrl}/line_account_data/?purchase_bill=${id}`)
      .toPromise();
  }
}
