import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { AlertController } from '@ionic/angular';
import { PrintServiceService, InvoiceData } from './print-service.service';
import { CrossPlatformBluetoothService } from './cross-platform-bluetooth.service';

export interface PrintManagerOptions {
  format: 'thermal' | 'a4' | 'a5' | 'mobile';
  showDialog?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class PrintManagerService {

  constructor(
    private platform: Platform,
    private alertController: AlertController,
    private printService: PrintServiceService,
    private bluetoothService: CrossPlatformBluetoothService
  ) {}

  /**
   * Main print method - handles platform detection and method selection
   */
  async printInvoice(data: InvoiceData, options: PrintManagerOptions = { format: 'thermal' }): Promise<void> {
    try {
      if (options.showDialog) {
        await this.showPrintOptionsDialog(data);
      } else {
        await this.printService.printInvoiceCrossPlatform(data, options.format);
      }
    } catch (error) {
      await this.showErrorDialog(`Print failed: ${error}`);
    }
  }

  /**
   * Show print options dialog with platform-specific options
   */
  async showPrintOptionsDialog(data: InvoiceData): Promise<void> {
    const isIOS = this.platform.is('ios');
    const isAndroid = this.platform.is('android');
    
    const inputs: any[] = [
      {
        name: 'format',
        type: 'radio',
        label: isIOS ? 'Receipt (AirPrint)' : 'Thermal Receipt (Bluetooth)',
        value: 'thermal',
        checked: true
      },
      {
        name: 'format',
        type: 'radio',
        label: isIOS ? 'A5 Format (AirPrint)' : 'A5 Format (Standard Print)',
        value: 'a5'
      },
      {
        name: 'format',
        type: 'radio',
        label: isIOS ? 'A4 Format (AirPrint)' : 'A4 Format (Standard Print)',
        value: 'a4'
      }
    ];

    // Add Bluetooth option for iOS if supported
    if (isIOS && this.bluetoothService.isBluetoothSupported()) {
      inputs.push({
        name: 'format',
        type: 'radio',
        label: 'Thermal Receipt (Bluetooth)',
        value: 'thermal-bluetooth'
      });
    }

    const alert = await this.alertController.create({
      header: 'Print Options',
      message: `Platform: ${this.getPlatformName()}`,
      inputs: inputs,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Setup Printer',
          handler: () => {
            this.setupPrinter();
          }
        },
        {
          text: 'Print',
          handler: async (format) => {
            if (format) {
              await this.handlePrintSelection(data, format);
            }
          }
        }
      ]
    });

    await alert.present();
  }

  /**
   * Handle print selection from dialog
   */
  private async handlePrintSelection(data: InvoiceData, format: string): Promise<void> {
    try {
      if (format === 'thermal-bluetooth') {
        // Force Bluetooth printing for iOS
        await this.printViaBluetooth(data);
      } else {
        await this.printService.printInvoiceCrossPlatform(data, format as any);
      }
      
      await this.showSuccessDialog('Print job sent successfully!');
    } catch (error) {
      await this.showErrorDialog(`Print failed: ${error}`);
    }
  }

  /**
   * Force Bluetooth printing (for iOS Web Bluetooth)
   */
  private async printViaBluetooth(data: InvoiceData): Promise<void> {
    if (!this.bluetoothService.isConnected()) {
      await this.bluetoothService.showDeviceSelection();
      
      // Wait for connection
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (!this.bluetoothService.isConnected()) {
        throw new Error('No Bluetooth device connected');
      }
    }

    // Generate thermal print data
    const printData = await (this.printService as any).generateThermalPrintData(data);
    
    // Send to printer
    const result = await this.bluetoothService.printData(printData);
    
    if (!result.success) {
      throw new Error(result.message);
    }
  }

  /**
   * Setup printer (Bluetooth device selection)
   */
  async setupPrinter(): Promise<void> {
    try {
      await this.bluetoothService.showDeviceSelection();
      
      const device = this.bluetoothService.getSelectedDevice();
      if (device) {
        await this.showSuccessDialog(`Connected to: ${device.name}`);
      }
    } catch (error) {
      await this.showErrorDialog(`Setup failed: ${error}`);
    }
  }

  /**
   * Get available print methods for current platform
   */
  getAvailablePrintMethods(): string[] {
    const methods: string[] = [];
    
    if (this.platform.is('ios')) {
      methods.push('AirPrint');
      if (this.bluetoothService.isBluetoothSupported()) {
        methods.push('Web Bluetooth');
      }
    } else if (this.platform.is('android')) {
      methods.push('Bluetooth Serial');
      methods.push('Standard Print');
    } else {
      methods.push('Browser Print');
    }
    
    return methods;
  }

  /**
   * Check printer status
   */
  async checkPrinterStatus(): Promise<string> {
    const device = this.bluetoothService.getSelectedDevice();
    const isConnected = this.bluetoothService.isConnected();
    
    if (device && isConnected) {
      return `Connected to: ${device.name}`;
    } else if (device) {
      return `Device selected: ${device.name} (Not connected)`;
    } else {
      return 'No printer selected';
    }
  }

  /**
   * Get platform name for display
   */
  private getPlatformName(): string {
    if (this.platform.is('ios')) return 'iOS';
    if (this.platform.is('android')) return 'Android';
    if (this.platform.is('desktop')) return 'Desktop';
    return 'Web';
  }

  /**
   * Show success dialog
   */
  private async showSuccessDialog(message: string): Promise<void> {
    const alert = await this.alertController.create({
      header: 'Success',
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  /**
   * Show error dialog
   */
  private async showErrorDialog(message: string): Promise<void> {
    const alert = await this.alertController.create({
      header: 'Error',
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  /**
   * Test print functionality
   */
  async testPrint(): Promise<void> {
    const testData: InvoiceData = {
      id: 1,
      name: 'Test Customer',
      date: new Date().toLocaleDateString(),
      phone_no: '1234567890',
      place: 'Test Location',
      gst_no: 'TEST123456789',
      sales_invoice_items: [
        {
          product_name: 'Test Product',
          no: 1,
          weight: 2,
          rate: 100,
          line_total: 200,
          mrp: 120,
          tax_rate: 18,
          hsn_code: '1234'
        }
      ],
      bill_amount: 200,
      previous_balance: 0,
      received_amount: 200,
      current_balance: 0,
      rounding_enabled: true,
      rounding_adjustment: 0,
      gross_total: 200
    };

    await this.printInvoice(testData, { format: 'thermal', showDialog: true });
  }
}
