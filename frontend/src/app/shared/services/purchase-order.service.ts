import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface PurchaseOrder {
  id?: number;
  po_number?: string;
  supplier_id: number;
  supplier_name?: string;
  status: string;
  auto_generated: boolean;
  created_date?: string;
  expected_delivery_date?: string;
  approved_date?: string;
  sent_date?: string;
  total_amount: number;
  tax_amount: number;
  discount_amount: number;
  net_amount: number;
  remarks?: string;
  terms_and_conditions?: string;
  approved_by?: number;
  approved_by_name?: string;
  items?: PurchaseOrderItem[];
  items_count?: number;
  total_items_quantity?: number;
}

export interface PurchaseOrderItem {
  id?: number;
  product_id: number;
  product_name?: string;
  product_short_code?: string;
  ordered_quantity: number;
  box_quantity?: number;
  pieces_quantity?: number;
  received_quantity?: number;
  pending_quantity?: number;
  unit_rate: number;
  line_total?: number;
  tax_rate: number;
  tax_amount?: number;
  forecast_quantity?: number;
  current_stock?: number;
  min_stock_threshold?: number;
  reorder_reason?: string;
}

export interface PurchaseOrderDraft {
  id?: number;
  supplier_id: number;
  supplier_name?: string;
  created_on?: string;
  status: string;
  auto_generated: boolean;
  forecast_based: boolean;
  total_estimated_amount: number;
  items?: PurchaseOrderDraftItem[];
  items_count?: number;
}

export interface PurchaseOrderDraftItem {
  id?: number;
  product_id: number;
  product_name?: string;
  product_short_code?: string;
  quantity: number;
  estimated_rate: number;
  estimated_total: number;
  forecast_quantity?: number;
  current_stock: number;
  min_stock_threshold: number;
  shortage_quantity: number;
  reorder_reason?: string;
  auto_generated: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class PurchaseOrderService {
  private apiUrl = `${environment.apiUrl}/purchase_order/`;
  private draftApiUrl = `${environment.apiUrl}/purchase_order_draft/`;

  constructor(private http: HttpClient) { }

  // Purchase Order CRUD operations
  getPurchaseOrders(filters?: {
    status?: string;
    supplier_id?: number;
    auto_generated?: boolean;
  }): Observable<any> {
    let params = new HttpParams();
    
    if (filters) {
      if (filters.status) {
        params = params.set('status', filters.status);
      }
      if (filters.supplier_id) {
        params = params.set('supplier_id', filters.supplier_id.toString());
      }
      if (filters.auto_generated !== undefined) {
        params = params.set('auto_generated', filters.auto_generated.toString());
      }
    }

    return this.http.get(this.apiUrl, { params });
  }

  getPurchaseOrder(poId: number): Observable<any> {
    const params = new HttpParams().set('po_id', poId.toString());
    return this.http.get(this.apiUrl, { params });
  }

  createPurchaseOrder(poData: {
    supplier_id: number;
    expected_delivery_date?: string;
    remarks?: string;
    terms_and_conditions?: string;
    auto_generated?: boolean;
    items: PurchaseOrderItem[];
  }): Observable<any> {
    return this.http.post(this.apiUrl, poData);
  }

  updatePurchaseOrder(poData: PurchaseOrder): Observable<any> {
    return this.http.put(this.apiUrl, poData);
  }

  deletePurchaseOrder(poId: number): Observable<any> {
    return this.http.delete(this.apiUrl, { body: { id: poId } });
  }

  approvePurchaseOrder(poId: number): Observable<any> {
    return this.http.put(this.apiUrl, { 
      id: poId, 
      status: 'approved' 
    });
  }

  // Purchase Order Draft operations
  getPurchaseOrderDrafts(): Observable<any> {
    return this.http.get(this.draftApiUrl);
  }

  getPurchaseOrderDraft(draftId: number): Observable<any> {
    const params = new HttpParams().set('draft_id', draftId.toString());
    return this.http.get(this.draftApiUrl, { params });
  }

  convertDraftToPurchaseOrder(draftId: number, expectedDeliveryDate?: string): Observable<any> {
    return this.http.post(this.draftApiUrl, {
      draft_id: draftId,
      expected_delivery_date: expectedDeliveryDate
    });
  }

  deletePurchaseOrderDraft(draftId: number): Observable<any> {
    return this.http.delete(this.draftApiUrl, { body: { id: draftId } });
  }

  // Utility methods
  getStatusColor(status: string): string {
    const statusColors = {
      'draft': 'medium',
      'pending_approval': 'warning',
      'approved': 'success',
      'sent_to_supplier': 'primary',
      'partially_received': 'tertiary',
      'fully_received': 'success',
      'cancelled': 'danger'
    };
    return statusColors[status] || 'medium';
  }

  getStatusLabel(status: string): string {
    const statusLabels = {
      'draft': 'Draft',
      'pending_approval': 'Pending Approval',
      'approved': 'Approved',
      'sent_to_supplier': 'Sent to Supplier',
      'partially_received': 'Partially Received',
      'fully_received': 'Fully Received',
      'cancelled': 'Cancelled'
    };
    return statusLabels[status] || status;
  }

  calculatePOTotals(items: PurchaseOrderItem[]): {
    totalAmount: number;
    taxAmount: number;
    netAmount: number;
  } {
    let totalAmount = 0;
    let taxAmount = 0;

    items.forEach(item => {
      // Use the correct formula: line_total = (box * unit_contains * rate) + (pieces * rate)
      // For now, we'll use ordered_quantity as fallback if box/pieces are not available
      let lineTotal = 0;
      if (item.box_quantity !== undefined || item.pieces_quantity !== undefined) {
        // This calculation will be properly implemented when we have access to product.unit_contains
        // For now, use the ordered_quantity as fallback
        lineTotal = item.ordered_quantity * item.unit_rate;
      } else {
        lineTotal = item.ordered_quantity * item.unit_rate;
      }

      const lineTax = lineTotal * (item.tax_rate / 100);

      totalAmount += lineTotal;
      taxAmount += lineTax;
    });

    return {
      totalAmount,
      taxAmount,
      netAmount: totalAmount + taxAmount
    };
  }

  // Get products filtered by supplier brands
  getProductsBySupplier(supplierId: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/supplier_products/?supplier_id=${supplierId}`);
  }
}
