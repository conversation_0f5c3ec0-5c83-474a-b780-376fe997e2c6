import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { AlertController } from '@ionic/angular';
import { PrintServiceService, InvoiceData } from './print-service.service';
import { CrossPlatformBluetoothService } from './cross-platform-bluetooth.service';
import { Printer } from '@bcyesil/capacitor-plugin-printer';
import EscPosEncoder from 'esc-pos-encoder-ionic';

export interface PrintOptions {
  format: 'thermal' | 'a4' | 'a5' | 'mobile';
  method: 'bluetooth' | 'airprint' | 'network';
}

export interface PrintResult {
  success: boolean;
  message: string;
  method: string;
}

@Injectable({
  providedIn: 'root'
})
export class CrossPlatformPrintService {
  
  constructor(
    private platform: Platform,
    private alertController: AlertController,
    private printService: PrintServiceService,
    private bluetoothService: CrossPlatformBluetoothService
  ) {}

  /**
   * Get available print methods for current platform
   */
  getAvailablePrintMethods(): string[] {
    const methods: string[] = [];
    
    if (this.platform.is('ios')) {
      methods.push('airprint');
      if (this.bluetoothService.isBluetoothSupported()) {
        methods.push('bluetooth');
      }
    } else if (this.platform.is('android')) {
      methods.push('bluetooth');
      // Android can also use standard printing
      methods.push('airprint');
    }
    
    return methods;
  }

  /**
   * Print invoice with automatic method selection
   */
  async printInvoice(data: InvoiceData, options?: Partial<PrintOptions>): Promise<PrintResult> {
    const defaultOptions: PrintOptions = {
      format: 'thermal',
      method: this.getDefaultPrintMethod()
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    try {
      switch (finalOptions.method) {
        case 'bluetooth':
          return await this.printViaBluetooth(data, finalOptions.format);
        case 'airprint':
          return await this.printViaAirPrint(data, finalOptions.format);
        default:
          throw new Error(`Unsupported print method: ${finalOptions.method}`);
      }
    } catch (error) {
      return {
        success: false,
        message: `Print failed: ${error}`,
        method: finalOptions.method
      };
    }
  }

  /**
   * Print via Bluetooth (thermal printers)
   */
  private async printViaBluetooth(data: InvoiceData, format: string): Promise<PrintResult> {
    try {
      // Check if device is connected
      if (!this.bluetoothService.isConnected()) {
        await this.bluetoothService.showDeviceSelection();
        
        // Wait a moment for connection
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (!this.bluetoothService.isConnected()) {
          throw new Error('No Bluetooth device connected');
        }
      }

      // Generate thermal print data
      const printData = await this.generateThermalPrintData(data);
      
      // Send to printer
      const result = await this.bluetoothService.printData(printData);
      
      return {
        success: result.success,
        message: result.message,
        method: 'bluetooth'
      };
    } catch (error) {
      throw new Error(`Bluetooth printing failed: ${error}`);
    }
  }

  /**
   * Print via AirPrint (iOS) or standard printing (Android)
   */
  private async printViaAirPrint(data: InvoiceData, format: string): Promise<PrintResult> {
    try {
      let htmlContent: string;
      
      switch (format) {
        case 'a4':
          htmlContent = await this.printService.getHtmlContentForWebA4(data);
          break;
        case 'a5':
          htmlContent = await this.printService.getHtmlContentForWebA5(data);
          break;
        case 'mobile':
        case 'thermal':
          htmlContent = await this.printService.getHtmlContentForMobile(data);
          break;
        default:
          htmlContent = await this.printService.getHtmlContentForWebA5(data);
      }

      // Use Capacitor Printer plugin
      await Printer.print({
        content: htmlContent,
        orientation: format === 'a4' ? 'landscape' : 'portrait'
      });

      return {
        success: true,
        message: 'Print job sent successfully',
        method: 'airprint'
      };
    } catch (error) {
      throw new Error(`AirPrint failed: ${error}`);
    }
  }

  /**
   * Generate thermal print data using ESC/POS commands
   */
  private async generateThermalPrintData(data: InvoiceData): Promise<Uint8Array> {
    const encoder = new EscPosEncoder();
    const paperWidth = 58; // mm
    const maxCharsPerLine = this.printService.getMaxCharsPerLine(paperWidth);
    const divider = '='.repeat(maxCharsPerLine);

    // Header
    encoder
      .align('center')
      .size('normal')
      .text(localStorage.getItem("company_name") || "Company Name")
      .newline()
      .text(localStorage.getItem("address") || "Address")
      .newline()
      .text(`GST NO: ${localStorage.getItem('gst_no') || 'N/A'}`)
      .newline()
      .text(`FSSAI NO: ${localStorage.getItem('fssai_no') || 'N/A'}`)
      .newline()
      .text(`${localStorage.getItem('contact_no_left') || ''}, ${localStorage.getItem('contact_no_right') || ''}`)
      .newline()
      .text(divider)
      .newline();

    // Invoice title
    encoder
      .align('center')
      .text(this.printService.getInvoiceTitle(data))
      .newline()
      .text(divider)
      .newline();

    // Customer details
    encoder
      .align('left')
      .text(`Bill No: ${data.id}`)
      .align('right')
      .text(`Date: ${data.date}`)
      .newline()
      .align('center')
      .text('Customer Details')
      .newline()
      .align('left')
      .text(`Name: ${data.name}`)
      .newline()
      .text(`Phone: ${data.phone_no}`)
      .newline()
      .text(`Place: ${data.place}`)
      .newline();

    if (data.gst_no) {
      encoder.text(`GST: ${data.gst_no}`, 0).newline();
    }

    encoder.text(divider, 0).newline();

    // Items header
    encoder
      .align('left')
      .text('Item'.padEnd(20) + 'Qty'.padStart(5) + 'Pcs'.padStart(5) + 'Amt'.padStart(8))
      .newline()
      .text(divider)
      .newline();

    // Items
    let totalQty = 0;
    let totalPcs = 0;
    let totalAmount = 0;

    data.sales_invoice_items.forEach(item => {
      const itemName = item.product_name.length > 20 ? 
        item.product_name.substring(0, 17) + '...' : 
        item.product_name;
      
      encoder
        .text(itemName.padEnd(20) +
              item.no.toString().padStart(5) +
              item.weight.toString().padStart(5) +
              item.line_total.toFixed(2).padStart(8), 0)
        .newline();

      if (item.hsn_code) {
        encoder.text(`  HSN: ${item.hsn_code}`, 0).newline();
      }

      totalQty += item.no;
      totalPcs += item.weight;
      totalAmount += item.line_total;
    });

    // Totals
    encoder
      .text(divider, 0)
      .newline()
      .text('Total'.padEnd(20) +
            totalQty.toString().padStart(5) +
            totalPcs.toString().padStart(5) +
            totalAmount.toFixed(2).padStart(8), 0)
      .newline()
      .text(divider, 0)
      .newline();

    // Tax details
    const taxDetails = this.printService.getTaxDetails(data);
    if (taxDetails.taxDetails.length > 0) {
      encoder
        .align('center')
        .text('Tax Details')
        .newline()
        .align('left');

      taxDetails.taxDetails.forEach(tax => {
        encoder
          .text(`${tax.value}% Tax: ${tax.tax_amount.toFixed(2)}`, 0)
          .newline();
      });

      encoder.text(divider, 0).newline();
    }

    // Bill summary
    encoder
      .text(`Subtotal: ${(totalAmount - taxDetails.totalTaxAmount).toFixed(2)}`, 0)
      .newline()
      .text(`Tax Total: ${taxDetails.totalTaxAmount.toFixed(2)}`, 0)
      .newline()
      .text(`Gross Total: ${(data.gross_total || totalAmount).toFixed(2)}`, 0)
      .newline();

    if (data.rounding_enabled && data.rounding_adjustment) {
      encoder.text(`Rounding: ${data.rounding_adjustment > 0 ? '+' : ''}${data.rounding_adjustment.toFixed(2)}`, 0).newline();
    }

    encoder
      .size('double')
      .text(`Net Amount: ${data.bill_amount.toFixed(2)}`, 0)
      .size('normal')
      .newline()
      .text(divider, 0)
      .newline();

    // Balance details
    encoder
      .text(`Previous Balance: ${data.previous_balance.toFixed(2)}`, 0)
      .newline()
      .text(`Bill Amount: ${data.bill_amount.toFixed(2)}`, 0)
      .newline()
      .text(`Received: ${data.received_amount.toFixed(2)}`, 0)
      .newline()
      .text(`Balance: ${data.current_balance.toFixed(2)}`, 0)
      .newline()
      .text(divider, 0)
      .newline();

    // Staff info
    this.printService.addThermalStaffInfo(encoder, data, divider);

    // Footer
    encoder
      .align('center')
      .text('Thank You')
      .newline()
      .newline()
      .newline()
      .cut();

    return encoder.encode();
  }

  /**
   * Get default print method for current platform
   */
  private getDefaultPrintMethod(): 'bluetooth' | 'airprint' {
    if (this.platform.is('ios')) {
      return 'airprint';
    } else {
      return 'bluetooth';
    }
  }

  /**
   * Show print options dialog
   */
  async showPrintOptions(data: InvoiceData): Promise<void> {
    const availableMethods = this.getAvailablePrintMethods();
    
    const alert = await this.alertController.create({
      header: 'Print Options',
      inputs: [
        {
          name: 'format',
          type: 'radio',
          label: 'Thermal Receipt',
          value: 'thermal',
          checked: true
        },
        {
          name: 'format',
          type: 'radio',
          label: 'A5 Format',
          value: 'a5'
        },
        {
          name: 'format',
          type: 'radio',
          label: 'A4 Format',
          value: 'a4'
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Print',
          handler: async (format) => {
            if (format) {
              const result = await this.printInvoice(data, { format });
              
              const resultAlert = await this.alertController.create({
                header: result.success ? 'Success' : 'Error',
                message: result.message,
                buttons: ['OK']
              });
              await resultAlert.present();
            }
          }
        }
      ]
    });

    await alert.present();
  }

  /**
   * Setup Bluetooth printer
   */
  async setupBluetoothPrinter(): Promise<void> {
    await this.bluetoothService.showDeviceSelection();
  }
}
