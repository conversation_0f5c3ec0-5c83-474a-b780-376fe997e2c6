import { Injectable } from '@angular/core';
import { LoadingController } from '@ionic/angular';

@Injectable({
  providedIn: 'root',
})
export class IonLoaderService {
  private loading: any = null;
  private isLoading: boolean = false;
  private loaderTimeout: any = null;
  private readonly LOADER_TIMEOUT = 30000; // 30 seconds timeout

  constructor(public loadingController: LoadingController) {}

  // Simple loader with improved error handling
  async startLoader(message: string = 'Please wait!'): Promise<void> {
    console.log('IonLoaderService startLoader - Starting loader with message:', message);
    try {
      // Dismiss any existing loader first
      await this.dismissLoader();

      this.isLoading = true;
      this.loading = await this.loadingController.create({
        message: message,
        spinner: 'crescent',
        translucent: true,
        backdropDismiss: false
      });

      await this.loading.present();
      console.log('IonLoaderService startLoader - Loader presented successfully');
      
      // Set timeout to automatically dismiss loader if it runs too long
      this.loaderTimeout = setTimeout(async () => {
        console.warn('IonLoaderService startLoader - Loader timeout reached, force dismissing');
        await this.forceCleanup();
      }, this.LOADER_TIMEOUT);
      
    } catch (error) {
      console.error('IonLoaderService startLoader - Error starting loader:', error);
      this.isLoading = false;
      this.loading = null;
    }
  }

  // Dismiss loader with improved error handling
  async dismissLoader(): Promise<void> {
    console.log('IonLoaderService dismissLoader - Attempting to dismiss loader');
    
    // Clear timeout if it exists
    if (this.loaderTimeout) {
      clearTimeout(this.loaderTimeout);
      this.loaderTimeout = null;
      console.log('IonLoaderService dismissLoader - Loader timeout cleared');
    }
    
    if (this.loading && this.isLoading) {
      try {
        await this.loading.dismiss();
        console.log('IonLoaderService dismissLoader - Loader dismissed successfully');
      } catch (err) {
        console.error('IonLoaderService dismissLoader - Error dismissing loader:', err);
        // Force close loader if dismiss fails
        try {
          const topLoader = await this.loadingController.getTop();
          if (topLoader) {
            await topLoader.dismiss();
            console.log('IonLoaderService dismissLoader - Force dismissed top loader');
          }
        } catch (secondaryErr) {
          console.error('IonLoaderService dismissLoader - Error force dismissing loader:', secondaryErr);
        }
      } finally {
        this.loading = null;
        this.isLoading = false;
        console.log('IonLoaderService dismissLoader - Loader state reset');
      }
    } else {
      console.log('IonLoaderService dismissLoader - No active loader to dismiss');
    }
  }

  // Check if loader is currently active
  get isLoaderActive(): boolean {
    return this.isLoading && this.loading !== null;
  }

  // Check if there are any loaders at the Ionic level
  async hasActiveLoaders(): Promise<boolean> {
    try {
      const topLoader = await this.loadingController.getTop();
      return topLoader !== null;
    } catch (error) {
      console.error('Error checking for active loaders:', error);
      return false;
    }
  }

  // Force dismiss all loaders - emergency cleanup
  async forceCleanup(): Promise<void> {
    console.log('IonLoaderService forceCleanup - Force cleaning up all loaders');
    
    // Clear timeout if it exists
    if (this.loaderTimeout) {
      clearTimeout(this.loaderTimeout);
      this.loaderTimeout = null;
      console.log('IonLoaderService forceCleanup - Loader timeout cleared');
    }
    
    try {
      // Dismiss any loaders at the Ionic level
      const topLoader = await this.loadingController.getTop();
      if (topLoader) {
        await topLoader.dismiss();
        console.log('IonLoaderService forceCleanup - Force dismissed top loader');
      }
      
      // Also try to dismiss our own loader if it exists
      if (this.loading && this.isLoading) {
        try {
          await this.loading.dismiss();
          console.log('IonLoaderService forceCleanup - Dismissed own loader');
        } catch (dismissError) {
          console.error('IonLoaderService forceCleanup - Error dismissing own loader:', dismissError);
        }
      }
    } catch (error) {
      console.error('IonLoaderService forceCleanup - Error in force cleanup:', error);
    } finally {
      this.loading = null;
      this.isLoading = false;
      console.log('IonLoaderService forceCleanup - Loader state reset');
    }
  }

  // Convenient method to run async operations with loader
  async withLoader<T>(
    asyncOperation: () => Promise<T>, 
    message: string = 'Please wait!'
  ): Promise<T> {
    console.log('IonLoaderService withLoader - Starting with message:', message);
    try {
      await this.startLoader(message);
      console.log('IonLoaderService withLoader - Loader started, running async operation');
      const result = await asyncOperation();
      console.log('IonLoaderService withLoader - Async operation completed successfully');
      return result;
    } catch (error) {
      console.error('IonLoaderService withLoader - Async operation failed:', error);
      throw error;
    } finally {
      console.log('IonLoaderService withLoader - Finally block, dismissing loader');
      try {
        await this.dismissLoader();
        console.log('IonLoaderService withLoader - Loader dismissed in finally block');
      } catch (dismissError) {
        console.error('IonLoaderService withLoader - Error dismissing loader in finally block:', dismissError);
        // Force cleanup if normal dismiss fails
        await this.forceCleanup();
      }
    }
  }
}
