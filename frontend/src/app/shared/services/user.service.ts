import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {  Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
    providedIn: 'root'
})
export class UserApiService {
    
    private apiUrl = environment.apiUrl;

    constructor(private http: HttpClient) { }

    getMetadata(): Observable<any> {
        return this.http.get(`${this.apiUrl}/metadata/`);
    }
    updateMetadata(metadata: any): Observable<any> {
        return this.http.post(`${this.apiUrl}/metadata/`, metadata);
    }
    getClosingStock(from_date: any,  to_date: any) {
        return this.http.get(`${this.apiUrl}/closing_stock/?from_date=${from_date}&to_date=${to_date}`).toPromise();
    }



}