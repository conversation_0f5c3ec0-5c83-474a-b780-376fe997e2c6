import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

// API Response interface
interface ApiResponse {
  success: boolean;
  message?: string;
  data?: any;
}

// Product Summary interface
export interface ProductSummary {
  total_products: number;
  active_products: number;
  inactive_products: number;
  generated_at: string;
}

// Buyer Summary interface
export interface BuyerSummary {
  total_buyers: number;
  active_buyers: number;
  inactive_buyers: number;
  total_receivables: number;
  generated_at: string;
}

@Injectable({
  providedIn: 'root'
})
export class SummaryService {

  constructor(private http: HttpClient) { }

  // Get global product summary (independent of filters)
  async getProductSummary(): Promise<ProductSummary | null> {
    try {
      const response = await this.http.get(`${environment.apiUrl}/product-summary/`).toPromise() as ApiResponse;
      if (response && response.success) {
        return response.data as ProductSummary;
      }
      return null;
    } catch (error) {
      console.error('Error loading product summary:', error);
      return null;
    }
  }

  // Get global buyer summary (independent of filters)
  async getBuyerSummary(): Promise<BuyerSummary | null> {
    try {
      const response = await this.http.get(`${environment.apiUrl}/buyer-summary/`).toPromise() as ApiResponse;
      if (response && response.success) {
        return response.data as BuyerSummary;
      }
      return null;
    } catch (error) {
      console.error('Error loading buyer summary:', error);
      return null;
    }
  }

  // Get combined summary data for dashboard
  async getCombinedSummary(): Promise<{
    products: ProductSummary | null;
    buyers: BuyerSummary | null;
  }> {
    try {
      const [productSummary, buyerSummary] = await Promise.all([
        this.getProductSummary(),
        this.getBuyerSummary()
      ]);

      return {
        products: productSummary,
        buyers: buyerSummary
      };
    } catch (error) {
      console.error('Error loading combined summary:', error);
      return {
        products: null,
        buyers: null
      };
    }
  }
}
