import { Injectable } from '@angular/core';

import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SuppliersService {

  constructor(private http: HttpClient) { }
  getSupplier() {
    return this.http
      .get(`${environment.apiUrl}/suplier/`)
      .toPromise();
  }
  saveSupplier(data) {
    return this.http
      .post(`${environment.apiUrl}/suplier/`, data)
      .toPromise();
  }
  editSupplier(data){
    return this.http.put(`${environment.apiUrl}/suplier/`, data)
    .toPromise();
  }
  editSupplierStatus(data){
    return this.http.put(`${environment.apiUrl}/suplier/`, data)
    .toPromise();
  }
  deleteSupplier(data){
    return this.http.delete(`${environment.apiUrl}/suplier/`, {body:data})
    .toPromise();
  }
}
