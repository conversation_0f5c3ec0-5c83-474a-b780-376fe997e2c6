// Payment Receipt Modal Styles

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--ion-color-primary);
  margin: 16px 0 12px 0;

  .section-icon {
    margin-right: 8px;
    font-size: 1.2rem;
  }
}

// Invoice Information Section
.invoice-info-section {
  margin-bottom: 24px;

  .invoice-info-card {
    margin: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    ion-card-content {
      padding: 16px;
    }
  }

  .info-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;

    .info-label {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      margin-bottom: 4px;
    }

    .info-value {
      font-size: 0.95rem;
      font-weight: 500;
      color: var(--ion-color-dark);

      &.amount {
        font-weight: 600;
        color: var(--ion-color-primary);
      }

      &.outstanding {
        color: var(--ion-color-warning);
      }
    }

    .info-note {
      font-size: 0.75rem;
      color: var(--ion-color-medium);
      font-style: italic;
      margin-top: 2px;
    }
  }
}

// Payment Form Section
.payment-form-section {
  margin-bottom: 24px;

  .form-item {
    margin-bottom: 16px;
    --border-radius: 8px;
    --background: var(--ion-color-light);

    ion-label {
      font-weight: 500;
      margin-bottom: 8px;
    }

    ion-input, ion-textarea {
      --padding-start: 12px;
      --padding-end: 12px;
    }

    .max-amount-btn {
      --color: var(--ion-color-primary);
      margin-right: 8px;
    }

    .payment-mode-btn {
      margin-top: 8px;
      --border-radius: 8px;
      --border-color: var(--ion-color-medium);
      --color: var(--ion-color-dark);
      text-align: left;
      justify-content: space-between;

      &:hover {
        --background: var(--ion-color-light);
      }
    }
  }
}

// Action Buttons
.action-buttons {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid var(--ion-color-light);

  .cancel-btn {
    --border-color: var(--ion-color-medium);
    --color: var(--ion-color-medium);
    --border-radius: 8px;
  }

  .submit-btn {
    --background: var(--ion-color-primary);
    --border-radius: 8px;
    font-weight: 600;

    &:disabled {
      --background: var(--ion-color-light);
      --color: var(--ion-color-medium);
    }
  }
}

// Modal Header
ion-header {
  ion-toolbar {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primary-contrast, white);

    ion-title {
      font-weight: 600;
    }

    ion-button {
      --color: var(--ion-color-primary-contrast, white);
    }
  }
}

// Content Styling
ion-content {
  --background: var(--ion-color-light-tint);
}

// Responsive Design
@media (max-width: 768px) {
  .info-item {
    .info-label {
      font-size: 0.8rem;
    }

    .info-value {
      font-size: 0.9rem;
    }
  }

  .section-title {
    font-size: 1rem;
  }
}

// Form Validation Styles
.form-item {
  &.ng-invalid.ng-touched {
    --border-color: var(--ion-color-danger);
    
    ion-input, ion-textarea {
      --color: var(--ion-color-danger);
    }
  }
}

// Amount Input Styling
ion-input[type="number"] {
  font-weight: 600;
  font-size: 1.1rem;
}

// Payment Mode Button Active State
.payment-mode-btn.selected {
  --background: var(--ion-color-primary-tint);
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
}
