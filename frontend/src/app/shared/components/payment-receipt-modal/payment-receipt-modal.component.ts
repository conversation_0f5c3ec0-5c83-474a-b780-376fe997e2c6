import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { AlertController } from '@ionic/angular';
import { PaymentReceiptService } from '../../services/payment-receipt.service';
import { ToastService } from '../../services/toast.service';
import { IonLoaderService } from '../../services/ion-loader.service';

@Component({
  selector: 'app-payment-receipt-modal',
  templateUrl: './payment-receipt-modal.component.html',
  styleUrls: ['./payment-receipt-modal.component.scss'],
})
export class PaymentReceiptModalComponent implements OnInit {
  @Input() isOpen: boolean = false;
  @Input() invoiceData: any = null;
  @Output() modalClosed = new EventEmitter<boolean>();
  @Output() paymentCreated = new EventEmitter<any>();

  // Form data
  paymentForm = {
    amount: null,
    modeOfPayment: '',
    referenceNumber: '',
    notes: '',
    paymentDate: new Date().toISOString()
  };

  // Payment modes from metadata
  modeOfPayments: any[] = [];
  outstandingBalance: number = 0;

  constructor(
    private alertController: AlertController,
    private paymentReceiptService: PaymentReceiptService,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService
  ) { }

  ngOnInit() {
    this.loadPaymentModes();
  }

  ngOnChanges() {
    if (this.invoiceData) {
      this.calculateOutstandingBalance();
    }
  }

  /**
   * Load payment modes from localStorage metadata
   */
  loadPaymentModes() {
    try {
      const metadata = JSON.parse(localStorage.getItem('metadata') || '{}');
      this.modeOfPayments = metadata.modeOfPayment || [];
    } catch (error) {
      console.error('Error loading payment modes:', error);
      this.modeOfPayments = [];
    }
  }

  /**
   * Calculate outstanding balance for the invoice
   */
  calculateOutstandingBalance() {
    if (this.invoiceData) {
      const billAmount = this.invoiceData.bill_amount || 0;
      const receivedAmount = this.invoiceData.received_amount || 0;
      this.outstandingBalance = billAmount - receivedAmount;
    }
  }

  /**
   * Open payment mode selection alert
   */
  async selectPaymentMode() {
    const options = this.modeOfPayments.map(mode => ({
      name: mode.slug,
      type: 'radio' as const,
      label: mode.name,
      value: mode.slug,
      checked: this.paymentForm.modeOfPayment === mode.slug
    }));

    const alert = await this.alertController.create({
      header: 'Select Payment Mode',
      inputs: options,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'OK',
          handler: (selectedMode) => {
            if (selectedMode) {
              this.paymentForm.modeOfPayment = selectedMode;
            }
          }
        }
      ]
    });

    await alert.present();
  }

  /**
   * Get selected payment mode name for display
   */
  getSelectedPaymentModeName(): string {
    if (!this.paymentForm.modeOfPayment) return 'Select Payment Mode';
    const selectedMode = this.modeOfPayments.find(mode => mode.slug === this.paymentForm.modeOfPayment);
    return selectedMode ? selectedMode.name : 'Select Payment Mode';
  }

  /**
   * Validate form data
   */
  validateForm(): { isValid: boolean, message?: string } {
    if (!this.paymentForm.amount || this.paymentForm.amount <= 0) {
      return { isValid: false, message: 'Please enter a valid payment amount' };
    }

    if (!this.paymentForm.modeOfPayment) {
      return { isValid: false, message: 'Please select a payment mode' };
    }

    // Validate payment amount against outstanding balance
    return this.paymentReceiptService.validatePaymentAmount(
      this.paymentForm.amount, 
      this.outstandingBalance
    );
  }

  /**
   * Submit payment receipt
   */
  async submitPayment() {
    const validation = this.validateForm();
    if (!validation.isValid) {
      this.toast.toastServices(validation.message, 'danger', 'top');
      return;
    }

    await this.ionLoaderService.startLoader();

    try {
      const paymentData = this.paymentReceiptService.formatPaymentData({
        invoiceId: this.invoiceData.id,
        amount: this.paymentForm.amount,
        modeOfPayment: this.paymentForm.modeOfPayment,
        referenceNumber: this.paymentForm.referenceNumber,
        notes: this.paymentForm.notes
      });

      const response = await this.paymentReceiptService.createPaymentReceipt(paymentData);

      if (response.success) {
        this.toast.toastServices('Payment receipt created successfully', 'success', 'top');
        this.paymentCreated.emit(paymentData);
        this.closeModal();
      } else {
        this.toast.toastServices(response.message || 'Failed to create payment receipt', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error creating payment receipt:', error);
      this.toast.toastServices('Error creating payment receipt', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  /**
   * Close modal and reset form
   */
  closeModal() {
    this.isOpen = false;
    this.resetForm();
    this.modalClosed.emit(false);
  }

  /**
   * Reset form data
   */
  resetForm() {
    this.paymentForm = {
      amount: null,
      modeOfPayment: '',
      referenceNumber: '',
      notes: '',
      paymentDate: new Date().toISOString()
    };
  }

  /**
   * Handle number input validation
   */
  numberOnlyValidation(event: any) {
    const pattern = /[0-9\.\-]/;
    const inputChar = String.fromCharCode(event.charCode);
    if (!pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  /**
   * Set amount to outstanding balance (suggested amount)
   * Users can still enter higher amounts for advance payments
   */
  setMaxAmount() {
    this.paymentForm.amount = this.outstandingBalance;
  }
}
