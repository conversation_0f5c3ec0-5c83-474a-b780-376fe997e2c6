// Ledger Create Modal Component Styles
.ledger-create-modal {
  // Transaction Type Section
  .transaction-type-section {
    margin-bottom: 24px;

    .transaction-segment {
      --background: #f8f9fa;
      --border-radius: 12px;
      --color: #6c757d;
      --color-checked: var(--ion-color-primary);
      --indicator-color: var(--ion-color-primary);
      --indicator-height: 3px;
      --indicator-transition: all 0.3s ease;

      ion-segment-button {
        --background-checked: var(--ion-color-primary);
        --color-checked: white;
        --indicator-color: transparent;
        font-weight: 500;
        font-size: 14px;
        text-transform: none;
        letter-spacing: 0.25px;
        min-height: 48px;

        ion-icon {
          font-size: 18px;
          margin-right: 8px;
        }

        ion-label {
          font-size: 13px;
          font-weight: 500;
        }

        &:hover {
          --background: rgba(var(--ion-color-primary-rgb), 0.1);
        }
      }
    }
  }

  // Party Selection Section
  .party-selection-section {
    margin-bottom: 24px;

    .section-header {
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #495057;

        .section-icon {
          font-size: 18px;
          color: var(--ion-color-primary);
        }
      }
    }

    .party-options {
      .form-item {
        --padding-start: 0;
        --padding-end: 0;
        --inner-padding-end: 0;
        --background: transparent;
        --border-color: #e9ecef;
        --border-radius: 8px;
        --border-width: 1px;
        --border-style: solid;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        ion-label {
          font-size: 14px;
          font-weight: 500;
          color: #6c757d;
          margin-bottom: 8px;

          ion-text {
            margin-left: 4px;
          }
        }

        .party-select {
          width: 100%;
          border-radius: 8px;
          border: 1px solid #e9ecef;
          background: white;
          transition: all 0.2s ease;

          &:focus-within {
            border-color: var(--ion-color-primary);
            box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.1);
          }
        }
      }
    }
  }

  // Payment Mode Section
  .payment-mode-section {
    margin-bottom: 24px;

    .section-header {
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #495057;

        .section-icon {
          font-size: 18px;
          color: var(--ion-color-primary);
        }
      }
    }

    .form-item {
      --padding-start: 0;
      --padding-end: 0;
      --inner-padding-end: 0;
      --background: transparent;
      --border-color: #e9ecef;
      --border-radius: 8px;
      --border-width: 1px;
      --border-style: solid;

      ion-label {
        font-size: 14px;
        font-weight: 500;
        color: #6c757d;
        margin-bottom: 8px;

        ion-text {
          margin-left: 4px;
        }
      }

      .payment-select {
        width: 100%;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        background: white;
        transition: all 0.2s ease;

        &:focus-within {
          border-color: var(--ion-color-primary);
          box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.1);
        }
      }
    }
  }

  // Image Preview Section
  .image-preview-section {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .section-header {
      margin-bottom: 16px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #495057;

        .section-icon {
          font-size: 18px;
          color: var(--ion-color-primary);
        }
      }
    }

    .image-preview {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      background: #f8f9fa;
      border: 2px dashed #dee2e6;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 200px;

      .preview-image {
        width: 100%;
        height: auto;
        max-height: 300px;
        object-fit: cover;
        display: block;
      }

      .remove-preview-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        --background: rgba(0, 0, 0, 0.7);
        --color: white;
        --border-radius: 50%;
        --width: 32px;
        --height: 32px;
        --padding: 0;

        ion-icon {
          font-size: 16px;
        }

        &:hover {
          --background: rgba(220, 53, 69, 0.9);
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .ledger-create-modal {
    .transaction-type-section {
      margin-bottom: 20px;

      .transaction-segment {
        ion-segment-button {
          min-height: 44px;

          ion-icon {
            font-size: 16px;
            margin-right: 6px;
          }

          ion-label {
            font-size: 12px;
          }
        }
      }
    }

    .party-selection-section,
    .payment-mode-section {
      margin-bottom: 20px;

      .section-header {
        margin-bottom: 12px;

        .section-title {
          font-size: 15px;

          .section-icon {
            font-size: 16px;
          }
        }
      }

      .party-options .form-item,
      .form-item {
        margin-bottom: 12px;

        ion-label {
          font-size: 13px;
        }
      }
    }

    .image-preview-section {
      margin-top: 20px;
      padding-top: 16px;

      .section-header {
        margin-bottom: 12px;

        .section-title {
          font-size: 15px;

          .section-icon {
            font-size: 16px;
          }
        }
      }

      .image-preview {
        min-height: 160px;

        .preview-image {
          max-height: 250px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ledger-create-modal {
    .transaction-type-section {
      margin-bottom: 16px;

      .transaction-segment {
        ion-segment-button {
          min-height: 40px;

          ion-icon {
            font-size: 14px;
            margin-right: 4px;
          }

          ion-label {
            font-size: 11px;
          }
        }
      }
    }

    .party-selection-section,
    .payment-mode-section {
      margin-bottom: 16px;

      .section-header {
        margin-bottom: 10px;

        .section-title {
          font-size: 14px;

          .section-icon {
            font-size: 15px;
          }
        }
      }

      .party-options .form-item,
      .form-item {
        margin-bottom: 10px;

        ion-label {
          font-size: 12px;
        }
      }
    }

    .image-preview-section {
      margin-top: 16px;
      padding-top: 12px;

      .section-header {
        margin-bottom: 10px;

        .section-title {
          font-size: 14px;

          .section-icon {
            font-size: 15px;
          }
        }
      }

      .image-preview {
        min-height: 140px;

        .preview-image {
          max-height: 200px;
        }

        .remove-preview-btn {
          --width: 28px;
          --height: 28px;

          ion-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Accessibility improvements
.ledger-create-modal {
  // Focus management
  .transaction-segment ion-segment-button:focus-within,
  .party-select:focus-within,
  .payment-select:focus-within {
    outline: 2px solid var(--ion-color-primary);
    outline-offset: 2px;
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    .transaction-segment {
      --background: #ffffff;
      border: 2px solid #000000;
    }

    .party-select,
    .payment-select {
      border: 2px solid #000000;
    }

    .image-preview {
      border: 2px solid #000000;
    }
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    .transaction-segment {
      --indicator-transition: none;
    }

    .party-select,
    .payment-select {
      transition: none;
    }
  }
} 