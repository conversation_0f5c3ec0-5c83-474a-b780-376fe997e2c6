<ion-header>
  <ion-toolbar>
    <ion-title>
      <div class="modal-title">
        <ion-icon name="storefront-outline" class="title-icon"></ion-icon>
        Unbilled Shops
      </div>
      <div class="modal-subtitle">{{routeName}} - {{date}}</div>
    </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="modal-content">
  <!-- Search Bar -->
  <div class="search-section">
    <ion-searchbar
      [(ngModel)]="searchTerm"
      (ionInput)="onSearchChange($event)"
      placeholder="Search shops by name, place, or phone..."
      debounce="300"
      show-clear-button="focus">
    </ion-searchbar>
  </div>

  <!-- Summary Section -->
  <div class="summary-section" *ngIf="filteredShops.length > 0">
    <ion-card class="summary-card">
      <ion-card-content>
        <div class="summary-stats">
          <div class="summary-item">
            <ion-icon name="storefront-outline" class="summary-icon"></ion-icon>
            <div class="summary-info">
              <span class="summary-label">Shops Pending</span>
              <span class="summary-value">{{getFilteredShopsCount()}}</span>
            </div>
          </div>
          
          <div class="summary-item" *ngIf="hasShopsWithExpectedAmount()">
            <ion-icon name="cash-outline" class="summary-icon"></ion-icon>
            <div class="summary-info">
              <span class="summary-label">Expected Amount</span>
              <span class="summary-value">{{formatAmount(getTotalExpectedAmount())}}</span>
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Shops List -->
  <div class="shops-section" *ngIf="filteredShops.length > 0">
    <ion-list>
      <ion-item 
        *ngFor="let shop of filteredShops; trackBy: trackByShopId" 
        class="shop-item"
        button
        (click)="onShopClick(shop)">
        
        <div class="shop-content">
          <div class="shop-header">
            <div class="shop-main-info">
              <h3 class="shop-name">{{shop.name}}</h3>
              <ion-chip [color]="getShopStatusColor(shop)" size="small">
                <ion-label>{{getLastBilledText(shop)}}</ion-label>
              </ion-chip>
            </div>
          </div>
          
          <div class="shop-details">
            <div class="detail-row" *ngIf="shop.place">
              <ion-icon name="location-outline" class="detail-icon"></ion-icon>
              <span class="detail-text">{{shop.place}}</span>
            </div>
            
            <div class="detail-row" *ngIf="shop.phone_no">
              <ion-icon name="call-outline" class="detail-icon"></ion-icon>
              <span class="detail-text">{{shop.phone_no}}</span>
            </div>
            
            <div class="detail-row" *ngIf="shop.expected_amount">
              <ion-icon name="cash-outline" class="detail-icon"></ion-icon>
              <span class="detail-text">{{formatAmount(shop.expected_amount)}}</span>
            </div>
          </div>
        </div>

        <div class="shop-actions" slot="end">
          <ion-button
            fill="clear"
            size="small"
            (click)="onCallShop($event, shop)"
            *ngIf="shop.phone_no"
            class="action-button call-button"
            title="Call Shop">
            <ion-icon name="call-outline" slot="icon-only"></ion-icon>
          </ion-button>

          <ion-button
            fill="clear"
            size="small"
            (click)="onCreateInvoice($event, shop)"
            class="action-button invoice-button"
            title="Create Invoice">
            <ion-icon name="add-outline" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </ion-item>
    </ion-list>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="filteredShops.length === 0 && searchTerm">
    <ion-icon name="search-outline" class="empty-icon"></ion-icon>
    <h4>No shops found</h4>
    <p>No shops match your search criteria</p>
  </div>

  <div class="empty-state" *ngIf="shops.length === 0">
    <ion-icon name="checkmark-circle-outline" class="empty-icon success"></ion-icon>
    <h4>All Shops Billed!</h4>
    <p>All shops in this route have been billed for {{date}}</p>
  </div>
</ion-content>

<!-- Footer Actions -->
<ion-footer *ngIf="filteredShops.length > 0">
  <ion-toolbar>
    <div class="footer-actions">
      <ion-button 
        fill="outline" 
        expand="block"
        (click)="viewAllInSalesBill()">
        <ion-icon name="list-outline" slot="start"></ion-icon>
        View All in Sales Bill
      </ion-button>
    </div>
  </ion-toolbar>
</ion-footer>
