.scanner-content {
  --background: #000;
}

/* Permission Container */
.permission-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.permission-card {
  max-width: 400px;
  width: 100%;
}

.permission-content {
  text-align: center;
  padding: 20px;
}

.permission-icon {
  font-size: 64px;
  color: var(--ion-color-primary);
  margin-bottom: 20px;
}

/* Scanner Container */
.scanner-container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Video Container */
.video-container {
  position: relative;
  flex: 1;
  background: #000;
  overflow: hidden;
}

.scanner-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scanner-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Scanner Overlay */
.scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.scanner-frame {
  position: relative;
  width: 280px;
  height: 180px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 8px;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid var(--ion-color-primary);
}

.corner.top-left {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.corner.top-right {
  top: -3px;
  right: -3px;
  border-left: none;
  border-bottom: none;
}

.corner.bottom-left {
  bottom: -3px;
  left: -3px;
  border-right: none;
  border-top: none;
}

.corner.bottom-right {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

/* Scanner Status */
.scanner-status {
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  color: white;
}

.status-content {
  text-align: center;
  position: relative;
}

.scan-icon {
  font-size: 32px;
  color: var(--ion-color-primary);
  margin-bottom: 10px;
}

.scan-icon.scanning {
  animation: pulse 2s infinite;
}

.scanning-text {
  margin: 0;
  font-size: 16px;
  color: #fff;
}

/* Scanning Animation */
.scanning-line {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--ion-color-primary), transparent);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% {
    transform: translateX(-50%) translateY(-100px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(100px);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Action Buttons */
.action-buttons {
  padding: 20px;
  background: var(--ion-color-light);
  gap: 12px;
  display: flex;
  flex-direction: column;
}

.manual-button {
  --color: var(--ion-color-primary);
  --border-color: var(--ion-color-primary);
}

.cancel-button {
  --color: var(--ion-color-medium);
}

/* Instructions */
.instructions {
  padding: 16px;
  background: var(--ion-color-light);
}

.instructions-card {
  margin: 0;
}

.instructions-card h3 {
  margin-top: 0;
  color: var(--ion-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.instructions-card ul {
  margin: 12px 0 0 0;
  padding-left: 20px;
}

.instructions-card li {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--ion-color-medium-shade);
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .scanner-frame {
    width: 240px;
    height: 150px;
  }
  
  .scanning-line {
    width: 160px;
  }
  
  .action-buttons {
    padding: 16px;
  }
  
  .instructions {
    padding: 12px;
  }
}
