// Standard Modal Component Styles
.standard-modal {
  --modal-background: #ffffff;
  --modal-border-radius: 16px;
  --modal-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --modal-padding: 20px;
  --modal-margin: 16px;
  --modal-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  // Modal sizing
  &.modal-size-small {
    --modal-width: 400px;
    --modal-max-width: 90vw;
  }
  
  &.modal-size-medium {
    --modal-width: 600px;
    --modal-max-width: 90vw;
  }
  
  &.modal-size-large {
    --modal-width: 800px;
    --modal-max-width: 95vw;
  }
  
  &.modal-size-fullscreen {
    --modal-width: 100vw;
    --modal-max-width: 100vw;
    --modal-height: 100vh;
  }

  // Modal positioning and appearance
  .modal-wrapper {
    width: var(--modal-width);
    max-width: var(--modal-max-width);
    height: auto;
    max-height: 90vh;
    border-radius: var(--modal-border-radius);
    box-shadow: var(--modal-shadow);
    background: var(--modal-background);
    margin: var(--modal-margin);
    overflow: hidden;
    transform: scale(0.9);
    opacity: 0;
    transition: var(--modal-transition);
    
    &.modal-show {
      transform: scale(1);
      opacity: 1;
    }
  }

  // Header styles
  ion-header {
    ion-toolbar {
      --background: var(--ion-color-primary);
      --color: white;
      --border-width: 0;
      --padding-start: var(--modal-padding);
      --padding-end: var(--modal-padding);
      --min-height: 64px;
      
      .modal-title {
        .title-content {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .title-text {
            font-size: 18px;
            font-weight: 600;
            line-height: 1.2;
          }
          
          .subtitle-text {
            font-size: 14px;
            font-weight: 400;
            opacity: 0.9;
            line-height: 1.2;
          }
        }
      }
      
      .close-button {
        --padding-start: 8px;
        --padding-end: 8px;
        --border-radius: 50%;
        --width: 40px;
        --height: 40px;
        
        ion-icon {
          font-size: 20px;
        }
        
        &:hover {
          --background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  // Content styles
  .modal-content {
    --background: #f8f9fa;
    --padding-start: var(--modal-padding);
    --padding-end: var(--modal-padding);
    --padding-top: var(--modal-padding);
    --padding-bottom: var(--modal-padding);
    
    .modal-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      gap: 16px;
      
      ion-spinner {
        --color: var(--ion-color-primary);
        width: 32px;
        height: 32px;
      }
      
      p {
        margin: 0;
        color: #6c757d;
        font-size: 14px;
        font-weight: 500;
      }
    }
    
    .modal-body {
      &.loading {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }

  // Footer styles
  .modal-footer {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: var(--modal-padding);
    margin: 0 calc(-1 * var(--modal-padding)) calc(-1 * var(--modal-padding));
    
    .button-container {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      align-items: center;
      flex-wrap: wrap;
      
      .modal-button {
        --border-radius: 8px;
        --padding-start: 16px;
        --padding-end: 16px;
        --height: 44px;
        font-weight: 500;
        font-size: 14px;
        text-transform: none;
        letter-spacing: 0.25px;
        
        ion-icon {
          font-size: 16px;
        }
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        &:active {
          transform: translateY(0);
        }
        
        &[disabled] {
          opacity: 0.6;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  // Color variants
  &.modal-color-primary {
    ion-toolbar {
      --background: var(--ion-color-primary);
    }
  }
  
  &.modal-color-secondary {
    ion-toolbar {
      --background: var(--ion-color-secondary);
    }
  }
  
  &.modal-color-success {
    ion-toolbar {
      --background: var(--ion-color-success);
    }
  }
  
  &.modal-color-warning {
    ion-toolbar {
      --background: var(--ion-color-warning);
    }
  }
  
  &.modal-color-danger {
    ion-toolbar {
      --background: var(--ion-color-danger);
    }
  }
  
  &.modal-color-medium {
    ion-toolbar {
      --background: var(--ion-color-medium);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .standard-modal {
    --modal-padding: 16px;
    --modal-margin: 8px;
    
    &.modal-size-small,
    &.modal-size-medium,
    &.modal-size-large {
      --modal-width: calc(100vw - 16px);
      --modal-max-width: calc(100vw - 16px);
    }
    
    .modal-footer {
      .button-container {
        flex-direction: column;
        
        .modal-button {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .standard-modal {
    --modal-padding: 12px;
    --modal-margin: 4px;
    
    ion-header {
      ion-toolbar {
        --min-height: 56px;
        
        .modal-title {
          .title-content {
            .title-text {
              font-size: 16px;
            }
            
            .subtitle-text {
              font-size: 12px;
            }
          }
        }
      }
    }
    
    .modal-content {
      --padding-start: 12px;
      --padding-end: 12px;
      --padding-top: 12px;
      --padding-bottom: 12px;
    }
    
    .modal-footer {
      padding: 12px;
      margin: 0 -12px -12px;
    }
  }
}

// Animation classes
.modal-enter {
  animation: modalEnter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-leave {
  animation: modalLeave 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalEnter {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes modalLeave {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.9);
    opacity: 0;
  }
}

// Accessibility improvements
.standard-modal {
  // Focus management
  &:focus-within {
    outline: 2px solid var(--ion-color-primary);
    outline-offset: 2px;
  }
  
  // High contrast mode support
  @media (prefers-contrast: high) {
    --modal-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    
    ion-toolbar {
      border-bottom: 2px solid currentColor;
    }
    
    .modal-footer {
      border-top: 2px solid currentColor;
    }
  }
  
  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    --modal-transition: none;
    
    .modal-button:hover {
      transform: none;
    }
  }
} 