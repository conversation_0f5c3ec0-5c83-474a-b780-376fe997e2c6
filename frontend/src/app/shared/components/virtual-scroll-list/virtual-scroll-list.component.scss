.virtual-scroll-container {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  will-change: scroll-position;
  
  // Optimize scrolling performance
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  
  // Improve rendering performance
  contain: layout style paint;
  
  // Add custom scrollbar styling for better UX
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--ion-color-light);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--ion-color-medium);
    border-radius: 2px;
    
    &:hover {
      background: var(--ion-color-dark);
    }
  }
}

.virtual-scroll-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--ion-color-light);
  padding: 8px 16px;
  min-height: 80px;
  
  // Optimize rendering
  will-change: transform;
  contain: layout style paint;
  
  // Smooth transitions
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: var(--ion-color-light-tint);
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  // Ensure content doesn't overflow
  overflow: hidden;
}

.virtual-scroll-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 80px;
  color: var(--ion-color-medium);
  
  ion-spinner {
    margin-bottom: 8px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
  }
}

// Performance optimizations for mobile
@media (max-width: 768px) {
  .virtual-scroll-container {
    // Reduce scroll sensitivity on mobile
    overscroll-behavior: contain;
    
    // Optimize touch scrolling
    -webkit-overflow-scrolling: touch;
    
    // Improve scrolling performance on iOS
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  
  .virtual-scroll-item {
    // Optimize for mobile rendering
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    
    // Reduce minimum height on mobile
    min-height: 70px;
    padding: 6px 12px;
  }
}



// Accessibility improvements
.virtual-scroll-item {
  &:focus {
    outline: 2px solid var(--ion-color-primary);
    outline-offset: -2px;
  }
  
  // Improve focus visibility for keyboard navigation
  &:focus-visible {
    outline: 2px solid var(--ion-color-primary);
    outline-offset: -2px;
    background-color: var(--ion-color-primary-tint);
  }
}

// Animation for smooth loading
.virtual-scroll-loading {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}