import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ChangeDetectionStrategy, 
  OnInit, 
  OnDestroy,
  ViewChild,
  ElementRef,
  ChangeDetectorRef
} from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';

export interface VirtualScrollItem {
  id: any;
  [key: string]: any;
}

@Component({
  selector: 'app-virtual-scroll-list',
  template: `
    <div class="virtual-scroll-container" 
         #scrollContainer
         (scroll)="onScroll($event)"
         [style.height]="containerHeight + 'px'"
         [style.overflow-y]="'auto'">
      
      <!-- Spacer for items before visible area -->
      <div [style.height]="offsetY + 'px'"></div>
      
      <!-- Visible items -->
      <div class="virtual-scroll-item" 
           *ngFor="let item of visibleItems; trackBy: trackByFn"
           [style.height]="itemHeight + 'px'">
        <ng-content [ngTemplateOutlet]="itemTemplate" 
                   [ngTemplateOutletContext]="{ $implicit: item, index: getItemIndex(item) }">
        </ng-content>
      </div>
      
      <!-- Spacer for items after visible area -->
      <div [style.height]="offsetYEnd + 'px'"></div>
      
      <!-- Loading indicator -->
      <div class="virtual-scroll-loading" *ngIf="loading">
        <ion-spinner></ion-spinner>
        <p>Loading more items...</p>
      </div>
    </div>
  `,
  styleUrls: ['./virtual-scroll-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VirtualScrollListComponent implements OnInit, OnDestroy {
  @Input() items: VirtualScrollItem[] = [];
  @Input() itemHeight: number = 80; // Default height per item
  @Input() containerHeight: number = 400; // Default container height
  @Input() itemTemplate: any; // Template reference for item rendering
  @Input() loading: boolean = false;
  @Input() hasMore: boolean = true;
  @Input() threshold: number = 5; // Items from bottom to trigger load more
  
  @Output() loadMore = new EventEmitter<void>();
  @Output() itemClick = new EventEmitter<VirtualScrollItem>();
  
  @ViewChild('scrollContainer', { static: true }) scrollContainer!: ElementRef;
  
  visibleItems: VirtualScrollItem[] = [];
  startIndex = 0;
  endIndex = 0;
  offsetY = 0;
  offsetYEnd = 0;
  visibleCount = 0;
  
  private destroy$ = new Subject<void>();
  private scrollSubject = new Subject<Event>();

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    this.calculateVisibleCount();
    this.updateVisibleItems();
    
    // Debounce scroll events for better performance
    this.scrollSubject
      .pipe(
        debounceTime(16), // ~60fps
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.updateVisibleItems();
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onScroll(event: Event) {
    this.scrollSubject.next(event);
    
    // Check if we need to load more items
    const element = event.target as HTMLElement;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;
    
    // Trigger load more when near bottom
    if (this.hasMore && !this.loading && 
        scrollTop + clientHeight >= scrollHeight - (this.threshold * this.itemHeight)) {
      this.loadMore.emit();
    }
  }

  private calculateVisibleCount() {
    this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + 2; // Buffer items
  }

  private updateVisibleItems() {
    if (!this.scrollContainer) return;
    
    const scrollTop = this.scrollContainer.nativeElement.scrollTop;
    this.startIndex = Math.floor(scrollTop / this.itemHeight);
    this.endIndex = Math.min(this.startIndex + this.visibleCount, this.items.length);
    
    // Ensure we don't go negative
    this.startIndex = Math.max(0, this.startIndex);
    
    // Update visible items
    this.visibleItems = this.items.slice(this.startIndex, this.endIndex);
    
    // Calculate spacer heights
    this.offsetY = this.startIndex * this.itemHeight;
    this.offsetYEnd = (this.items.length - this.endIndex) * this.itemHeight;
    
    // Trigger change detection
    this.cdr.markForCheck();
  }

  trackByFn(index: number, item: VirtualScrollItem): any {
    return item.id || index;
  }

  getItemIndex(item: VirtualScrollItem): number {
    return this.items.findIndex(i => i.id === item.id);
  }

  onItemClick(item: VirtualScrollItem) {
    this.itemClick.emit(item);
  }

  // Public method to refresh the view when items change
  refresh() {
    this.updateVisibleItems();
  }

  // Public method to scroll to specific item
  scrollToItem(itemId: any) {
    const index = this.items.findIndex(item => item.id === itemId);
    if (index >= 0) {
      const scrollTop = index * this.itemHeight;
      this.scrollContainer.nativeElement.scrollTop = scrollTop;
      this.updateVisibleItems();
    }
  }

  // Public method to scroll to top
  scrollToTop() {
    this.scrollContainer.nativeElement.scrollTop = 0;
    this.updateVisibleItems();
  }
}