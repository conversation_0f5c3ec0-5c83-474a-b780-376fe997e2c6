import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { ProductService } from '../../services/product.service';
import { ToastService } from '../../services/toast.service';
import { IonLoaderService } from '../../services/ion-loader.service';
import { IonicSelectableComponent } from 'ionic-selectable';

@Component({
  selector: 'app-product-form',
  templateUrl: './product-form.component.html',
  styleUrls: ['./product-form.component.scss'],
})
export class ProductFormComponent implements OnInit, OnChanges {
  @Input() isOpen: boolean = false;
  @Input() mode: 'create' | 'edit' = 'create';
  @Input() productData: any = null;
  @Output() modalClosed = new EventEmitter<void>();
  @Output() productCreated = new EventEmitter<any>();
  @Output() productUpdated = new EventEmitter<any>();

  brandData: any[] = [];
  calculatedPrMargin: number = null;
  calculatedPrRate: number = null;
  calculatedSellingMargin: number = null;
  calculatedSellingRate: number = null;
  mrp: number = null;
  marginalItem: any = JSON.parse(localStorage.getItem('metadata'))?.marginalItem || false;
  isCrateBased: boolean = false;
  crateSize: number = 1;

  // Purchase Rate Calculator Modal properties
  isPurchaseCalculatorOpen: boolean = false;
  calculatorMode: 'add' | 'edit' = 'add';
  calculatorTotalPurchaseAmount: number = null;
  calculatorOrderQuantity: number = null;
  calculatorUnitContains: number = null;
  calculatorCrateContains: number = null;
  calculatorGstEnabled: boolean = false;
  calculatorGstRate: number = 18;
  calculatorBasicPurchase: number = null;
  calculatorTaxAmount: number = null;
  calculatorNetPurchaseCost: number = null;
  calculatorPrRate: number = null;

  constructor(
    private productService: ProductService,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService
  ) { }

  ngOnInit() {
    this.getBrand();
    if (this.mode === 'edit' && this.productData) {
      this.initializeEditData();
    }
  }

  ngOnChanges() {
    if (this.isOpen) {
      this.getBrand();
      if (this.mode === 'create') {
        this.resetForm();
      } else if (this.mode === 'edit' && this.productData) {
        this.initializeEditData();
      }
    }
  }

  private initializeEditData() {
    this.mrp = this.productData.mrp;
    this.calculatedPrMargin = this.productData.pr_margin;
    this.calculatedPrRate = this.productData.pr_rate;
    this.calculatedSellingMargin = this.productData.margin;
    this.calculatedSellingRate = this.productData.rate;
    this.isCrateBased = this.productData.is_crate_based || false;
    this.crateSize = this.productData.crate_size || 1;
  }

  private resetForm() {
    this.mrp = null;
    this.calculatedPrMargin = null;
    this.calculatedPrRate = null;
    this.calculatedSellingMargin = null;
    this.calculatedSellingRate = null;
    this.isCrateBased = false;
    this.crateSize = 1;
  }

  async getBrand() {
    try {
      const res: any = await this.productService.getBrand();
      if (res.success) {
        this.brandData = res.data;
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
    }
  }

  async addBrand(event: { component: IonicSelectableComponent }) {
    const name = event.component.searchText;
    await this.ionLoaderService.startLoader().then(async () => {
      await this.productService
        .addBrand(name)
        .then(async (res: any) => {
          if (res.success) {
            this.toast.toastServices(res.message, 'success', 'top');
            this.getBrand();
          } else {
            this.toast.toastServices(res.message, 'danger', 'top');
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top');
          this.ionLoaderService.dismissLoader();
        });
    });
  }

  closeModal() {
    this.isOpen = false;
    this.modalClosed.emit();
  }

  async onSubmit(formData: any) {
    if (this.mode === 'create') {
      await this.createProduct(formData);
    } else {
      await this.updateProduct(formData);
    }
  }

  async createProduct(data: any) {
    data.brand_id = data.brand?.id;
    delete data.brand;

    // Add calculated values
    data.pr_margin = this.calculatedPrMargin;
    data.pr_rate = this.calculatedPrRate;
    data.margin = this.calculatedSellingMargin;
    data.rate = this.calculatedSellingRate;
    data.mrp = this.mrp;
    data.is_crate_based = this.isCrateBased;
    data.crate_size = this.crateSize;

    this.convertEmptyStringsToNull(data);
    
    await this.ionLoaderService.startLoader().then(async () => {
      await this.productService
        .saveProduct(data)
        .then(async (res: any) => {
          if (res.success) {
            this.toast.toastServices(res.message, 'success', 'top');
            this.productCreated.emit(res.data);
            this.closeModal();
          } else {
            this.toast.toastServices(res.message, 'danger', 'top');
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top');
          this.ionLoaderService.dismissLoader();
        });
    });
  }

  async updateProduct(data: any) {
    data.id = this.productData.id;
    data.brand_id = data.brand?.id;
    delete data.brand;

    // Add calculated values
    data.pr_margin = this.calculatedPrMargin;
    data.pr_rate = this.calculatedPrRate;
    data.margin = this.calculatedSellingMargin;
    data.rate = this.calculatedSellingRate;
    data.mrp = this.mrp;
    data.is_crate_based = this.isCrateBased;
    data.crate_size = this.crateSize;

    this.convertEmptyStringsToNull(data);
    
    await this.ionLoaderService.startLoader().then(async () => {
      await this.productService.editProduct(data).then(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.productUpdated.emit(res.data);
          this.closeModal();
        } else {
          this.toast.toastServices(res.message, 'danger', 'top');
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader();
      });
    });
  }

  private convertEmptyStringsToNull(obj: any) {
    for (const key in obj) {
      if (obj[key] === '') {
        obj[key] = null;
      }
    }
  }

  // Calculation methods
  updateMrp(value: number) {
    this.mrp = value;
    this.calculateSellingRate();
  }

  calculatePrMargin(prRate: number) {
    this.calculatedPrRate = prRate;
    if (this.mrp && prRate) {
      this.calculatedPrMargin = ((this.mrp - prRate) / this.mrp) * 100;
    }
  }

  calculatePrRate(prMargin: number) {
    this.calculatedPrMargin = prMargin;
    if (this.mrp && prMargin) {
      this.calculatedPrRate = this.mrp - (this.mrp * prMargin / 100);
    }
  }

  calculateSellingMargin(sellingRate: number) {
    this.calculatedSellingRate = sellingRate;
    if (this.mrp && sellingRate) {
      this.calculatedSellingMargin = ((this.mrp - sellingRate) / this.mrp) * 100;
    }
  }

  calculateSellingRate(sellingMargin?: number) {
    if (sellingMargin !== undefined) {
      this.calculatedSellingMargin = sellingMargin;
    }
    if (this.mrp && this.calculatedSellingMargin) {
      this.calculatedSellingRate = this.mrp - (this.mrp * this.calculatedSellingMargin / 100);
    }
  }

  onCrateBasedToggle(value: boolean) {
    this.isCrateBased = value;
    if (!value) {
      this.crateSize = 1;
    }
  }

  updateCrateSize(value: number) {
    this.crateSize = value;
  }

  // Purchase Rate Calculator methods
  openPurchaseCalculator() {
    this.isPurchaseCalculatorOpen = true;
    this.calculatorMode = this.mode === 'edit' ? 'edit' : 'add';
  }

  closePurchaseCalculator() {
    this.isPurchaseCalculatorOpen = false;
  }

  onCalculatorFieldChange() {
    this.calculatePurchaseRate();
  }

  calculatePurchaseRate() {
    if (this.calculatorTotalPurchaseAmount && this.calculatorOrderQuantity && 
        this.calculatorUnitContains && this.calculatorCrateContains) {
      
      // Calculate basic purchase amount
      if (this.calculatorGstEnabled && this.calculatorGstRate) {
        this.calculatorBasicPurchase = this.calculatorTotalPurchaseAmount / (1 + this.calculatorGstRate / 100);
        this.calculatorTaxAmount = this.calculatorTotalPurchaseAmount - this.calculatorBasicPurchase;
      } else {
        this.calculatorBasicPurchase = this.calculatorTotalPurchaseAmount;
        this.calculatorTaxAmount = 0;
      }
      
      this.calculatorNetPurchaseCost = this.calculatorBasicPurchase;
      
      // Calculate PR Rate using the formula: pr_rate = total_purchase_amount / (order_quantity × unit_contains × crate_contains)
      this.calculatorPrRate = this.calculatorTotalPurchaseAmount / 
        (this.calculatorOrderQuantity * this.calculatorUnitContains * this.calculatorCrateContains);
    }
  }

  applyCalculatedRate() {
    if (this.calculatorPrRate) {
      this.calculatedPrRate = this.calculatorPrRate;
      this.calculatePrMargin(this.calculatedPrRate);
      this.closePurchaseCalculator();
    }
  }
}
