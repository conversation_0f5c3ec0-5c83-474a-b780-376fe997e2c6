<form [formGroup]="form" (ngSubmit)="onSubmit()" class="standard-form">
  <!-- Form Sections -->
  <div class="form-sections">
    <div 
      *ngFor="let section of sections; trackBy: trackBySection" 
      class="form-section"
      [class.collapsible]="section.collapsible"
      [class.collapsed]="section.collapsed">
      
      <!-- Section Header -->
      <div 
        class="section-header"
        [class.clickable]="section.collapsible"
        (click)="toggleSection(section)">
        
        <div class="section-title">
          <ion-icon 
            *ngIf="section.icon" 
            [name]="section.icon" 
            class="section-icon">
          </ion-icon>
          <h3>{{ section.title }}</h3>
          <ion-icon 
            *ngIf="section.collapsible" 
            [name]="section.collapsed ? 'chevron-down' : 'chevron-up'" 
            class="collapse-icon">
          </ion-icon>
        </div>
        
        <p *ngIf="section.subtitle" class="section-subtitle">{{ section.subtitle }}</p>
      </div>

      <!-- Section Content -->
      <div class="section-content" [class.hidden]="section.collapsed">
        <div 
          *ngFor="let field of section.fields; trackBy: trackByField" 
          class="form-field"
          [class.invalid]="isFieldInvalid(field.name)"
          [class.disabled]="field.disabled || disabled">
          
          <!-- Field Label -->
          <ion-label 
            [for]="field.name" 
            class="field-label"
            position="stacked">
            {{ field.label }}
            <ion-text *ngIf="field.required" color="danger">*</ion-text>
          </ion-label>

          <!-- Text Input -->
          <ion-input
            *ngIf="field.type === 'text' || field.type === 'email' || field.type === 'tel' || field.type === 'password'"
            [id]="field.name"
            [formControlName]="field.name"
            [type]="field.type"
            [placeholder]="field.placeholder"
            [readonly]="field.readonly"
            [disabled]="field.disabled || disabled"
            class="field-input">
          </ion-input>

          <!-- Number Input -->
          <ion-input
            *ngIf="field.type === 'number'"
            [id]="field.name"
            [formControlName]="field.name"
            type="number"
            [placeholder]="field.placeholder"
            [min]="field.min"
            [max]="field.max"
            [step]="field.step"
            [readonly]="field.readonly"
            [disabled]="field.disabled || disabled"
            class="field-input">
          </ion-input>

          <!-- Date Input -->
          <ion-input
            *ngIf="field.type === 'date' || field.type === 'time' || field.type === 'datetime-local'"
            [id]="field.name"
            [formControlName]="field.name"
            [type]="field.type"
            [placeholder]="field.placeholder"
            [readonly]="field.readonly"
            [disabled]="field.disabled || disabled"
            class="field-input">
          </ion-input>

          <!-- Textarea -->
          <ion-textarea
            *ngIf="field.type === 'textarea'"
            [id]="field.name"
            [formControlName]="field.name"
            [placeholder]="field.placeholder"
            [rows]="field.rows || 3"
            [readonly]="field.readonly"
            [disabled]="field.disabled || disabled"
            class="field-textarea">
          </ion-textarea>

          <!-- Select -->
          <ion-select
            *ngIf="field.type === 'select'"
            [id]="field.name"
            [formControlName]="field.name"
            [placeholder]="field.placeholder"
            [disabled]="field.disabled || disabled"
            class="field-select">
            <ion-select-option
              *ngFor="let option of field.options"
              [value]="option.value"
              [disabled]="option.disabled">
              {{ option.label }}
            </ion-select-option>
          </ion-select>

          <!-- Checkbox -->
          <ion-checkbox
            *ngIf="field.type === 'checkbox'"
            [id]="field.name"
            [formControlName]="field.name"
            [disabled]="field.disabled || disabled"
            class="field-checkbox">
            <ion-label>{{ field.placeholder }}</ion-label>
          </ion-checkbox>

          <!-- Radio Group -->
          <ion-radio-group
            *ngIf="field.type === 'radio'"
            [id]="field.name"
            [formControlName]="field.name"
            [disabled]="field.disabled || disabled"
            class="field-radio-group">
            <ion-item *ngFor="let option of field.options" lines="none">
              <ion-radio [value]="option.value" [disabled]="option.disabled"></ion-radio>
              <ion-label>{{ option.label }}</ion-label>
            </ion-item>
          </ion-radio-group>

          <!-- File Input -->
          <div *ngIf="field.type === 'file'" class="file-input-container">
            <ion-button
              fill="outline"
              expand="block"
              (click)="fileInput.click()"
              [disabled]="field.disabled || disabled"
              class="file-select-button">
              <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
              {{ field.placeholder || 'Choose File' }}
            </ion-button>
            <input
              #fileInput
              type="file"
              [accept]="field.accept"
              [multiple]="field.multiple"
              (change)="onFileSelected($event, field.name)"
              style="display: none;">
          </div>

          <!-- Help Text -->
          <div *ngIf="field.helpText" class="field-help">
            <ion-icon name="information-circle-outline"></ion-icon>
            <span>{{ field.helpText }}</span>
          </div>

          <!-- Error Message -->
          <div *ngIf="isFieldInvalid(field.name)" class="field-error">
            <ion-icon name="alert-circle-outline"></ion-icon>
            <span>{{ fieldErrors[field.name] }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="form-actions">
    <ion-row>
      <ion-col size="6" *ngIf="showCancelButton">
        <ion-button
          type="button"
          expand="block"
          fill="outline"
          [color]="cancelButtonColor"
          (click)="onCancel()"
          [disabled]="loading || disabled"
          class="cancel-button">
          <ion-icon [name]="cancelButtonIcon" slot="start"></ion-icon>
          {{ cancelButtonText }}
        </ion-button>
      </ion-col>
      <ion-col [size]="showCancelButton ? 6 : 12">
        <ion-button
          type="submit"
          expand="block"
          [color]="submitButtonColor"
          [disabled]="form.invalid || loading || disabled"
          class="submit-button">
          <ion-spinner *ngIf="loading" name="crescent" slot="start"></ion-spinner>
          <ion-icon *ngIf="!loading" [name]="submitButtonIcon" slot="start"></ion-icon>
          {{ submitButtonText }}
        </ion-button>
      </ion-col>
    </ion-row>
  </div>
</form> 