// Modal Header
.modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  
  .title-icon {
    font-size: 20px;
    color: var(--ion-color-primary);
  }
}

.modal-subtitle {
  font-size: 12px;
  font-weight: 400;
  opacity: 0.7;
  margin-top: 2px;
}

// Content
.modal-content {
  --padding-top: 0;
  --padding-bottom: 0;
}

// Upload Section
.upload-section {
  padding: 16px;
  background: var(--ion-color-light);
}

.upload-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-icon {
  font-size: 20px;
  color: var(--ion-color-primary);
  margin-right: 8px;
}

// Image Preview
.image-preview {
  position: relative;
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  background: #f8f9fa;
  
  .preview-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
  }
  
  .remove-preview-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    --color: white;
    --background: rgba(0, 0, 0, 0.5);
    --border-radius: 50%;
    width: 32px;
    height: 32px;
  }
}

// Upload Controls
.upload-controls {
  margin-bottom: 16px;
}

.select-image-btn {
  --border-radius: 12px;
  height: 48px;
  font-weight: 600;
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
}

// Notes Section
.notes-section {
  margin-bottom: 16px;
  
  ion-item {
    --background: transparent;
    --padding-start: 0;
    --padding-end: 0;
  }
  
  ion-textarea {
    --background: white;
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    border-radius: 8px;
    border: 1px solid var(--ion-color-light-shade);
  }
}

// Upload Actions
.upload-actions {
  ion-button {
    --border-radius: 12px;
    height: 48px;
    font-weight: 600;
  }
}

// Photos Section
.photos-section {
  padding: 16px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
  
  .section-icon {
    font-size: 18px;
    color: var(--ion-color-primary);
  }
}

// Loading State
.loading-state {
  text-align: center;
  padding: 48px 16px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    margin: 0;
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

// Photos Grid
.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.photo-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.photo-container {
  position: relative;
  cursor: pointer;
  
  .photo-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
  }
  
  .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
    
    .view-icon {
      font-size: 32px;
      color: white;
    }
  }
  
  &:hover .photo-overlay {
    opacity: 1;
  }
}

.photo-info {
  padding: 12px;
}

.photo-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  .photo-date {
    font-size: 12px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  .photo-time {
    font-size: 11px;
    color: var(--ion-color-medium);
  }
}

.photo-notes {
  margin-bottom: 8px;
  
  p {
    margin: 0;
    font-size: 12px;
    color: var(--ion-color-medium);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.photo-actions {
  display: flex;
  justify-content: flex-end;
  
  ion-button {
    --padding-start: 8px;
    --padding-end: 8px;
    height: 32px;
    width: 32px;
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 48px 16px;
  
  .empty-icon {
    font-size: 64px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

// Footer
.footer-info {
  padding: 12px 16px;
  text-align: center;
  
  small {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 12px;
  }
}

// Action Sheet Styles
.image-options-action-sheet {
  --background: white;
  --border-radius: 16px 16px 0 0;
}

// Responsive Design
@media (max-width: 768px) {
  .photos-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .photo-item {
    margin-bottom: 0;
  }
  
  .upload-section {
    padding: 12px;
  }
  
  .photos-section {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: 16px;
  }
  
  .modal-subtitle {
    font-size: 11px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .photo-container .photo-image {
    height: 160px;
  }
}



.upload-modal {
  --background: var(--ion-background-color);
  --border-radius: 16px;
  --width: 95%;
  --max-width: 600px;
  --height: 90%;
  --max-height: 800px;
}

.upload-form {
  --background: var(--ion-card-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
