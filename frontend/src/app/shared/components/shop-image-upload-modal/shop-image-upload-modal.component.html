<ion-header>
  <ion-toolbar>
    <ion-title>
      <div class="modal-title">
        <ion-icon name="camera-outline" class="title-icon"></ion-icon>
        Shop Images
      </div>
      <div class="modal-subtitle">{{shop.name}} - {{shop.place}}</div>
    </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="modal-content">
  <!-- Upload Section -->
  <div class="upload-section">
    <ion-card class="upload-card">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="cloud-upload-outline" class="upload-icon"></ion-icon>
          Add New Photo
        </ion-card-title>
        <ion-card-subtitle>Take a photo or choose from gallery</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <!-- Image Preview -->
        <div class="image-preview" *ngIf="previewUrl">
          <img [src]="previewUrl" alt="Preview" class="preview-image" />
          <ion-button 
            fill="clear" 
            size="small" 
            class="remove-preview-btn"
            (click)="clearSelection()">
            <ion-icon name="close-circle" slot="icon-only"></ion-icon>
          </ion-button>
        </div>

        <!-- Upload Controls -->
        <div class="upload-controls" *ngIf="!previewUrl">
          <ion-button 
            expand="block" 
            fill="outline" 
            class="select-image-btn"
            (click)="presentImageOptions()">
            <ion-icon name="camera-outline" slot="start"></ion-icon>
            Add Photo
          </ion-button>
        </div>

        <!-- Notes Input -->
        <div class="notes-section" *ngIf="selectedFile">
          <ion-item lines="none">
            <ion-label position="stacked">Notes (Optional)</ion-label>
            <ion-textarea
              [(ngModel)]="notes"
              placeholder="Add notes about this photo..."
              rows="3"
              maxlength="200">
            </ion-textarea>
          </ion-item>
        </div>

        <!-- Upload Button -->
        <div class="upload-actions" *ngIf="selectedFile">
          <ion-button 
            expand="block" 
            fill="solid" 
            color="primary"
            [disabled]="uploading"
            (click)="uploadImage()">
            <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
            <span *ngIf="!uploading">Upload Photo</span>
            <span *ngIf="uploading">Uploading...</span>
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Photos Gallery -->
  <div class="photos-section">
    <div class="section-header">
      <h3 class="section-title">
        <ion-icon name="images-outline" class="section-icon"></ion-icon>
        Photo Gallery ({{photos.length}})
      </h3>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="loading">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading photos...</p>
    </div>

    <!-- Photos Grid -->
    <div class="photos-grid" *ngIf="!loading && photos.length > 0">
      <div 
        *ngFor="let photo of photos; trackBy: trackByPhotoId" 
        class="photo-item">
        
        <div class="photo-container" (click)="viewFullImage(photo)">
          <img 
            [src]="getImageUrl(photo)" 
            [alt]="photo.notes || 'Shop photo'"
            class="photo-image"
            loading="lazy" />
          
          <div class="photo-overlay">
            <ion-icon name="eye-outline" class="view-icon"></ion-icon>
          </div>
        </div>

        <div class="photo-info">
          <div class="photo-meta">
            <span class="photo-date">{{formatDate(photo.date_taken)}}</span>
            <span class="photo-time">{{formatTime(photo.time_taken)}}</span>
          </div>
          
          <div class="photo-notes" *ngIf="photo.notes">
            <p>{{photo.notes}}</p>
          </div>
          
          <div class="photo-actions">
            <ion-button 
              fill="clear" 
              size="small" 
              color="primary"
              (click)="openInBrowser(photo)"
              title="Open in Browser">
              <ion-icon name="open-outline" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-button 
              fill="clear" 
              size="small" 
              color="danger"
              (click)="deletePhoto(photo)"
              title="Delete Photo">
              <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="!loading && photos.length === 0">
      <ion-icon name="images-outline" class="empty-icon"></ion-icon>
      <h4>No Photos Yet</h4>
      <p>Start by adding your first photo of this shop</p>
    </div>
  </div>

  <!-- Hidden File Inputs -->
  <input
    #fileInput
    type="file"
    accept="image/*"
    (change)="onFileSelected($event)"
    style="display: none;" />

  <input
    #cameraInput
    type="file"
    accept="image/*"
    capture="environment"
    (change)="onFileSelected($event)"
    style="display: none;" />
</ion-content>

<ion-footer *ngIf="photos.length > 0">
  <ion-toolbar>
    <div class="footer-info">
      <ion-text color="medium">
        <small>
          <ion-icon name="information-circle-outline"></ion-icon>
          Tap photo to view full screen • Use browser icon to open URL directly
        </small>
      </ion-text>
    </div>
  </ion-toolbar>
</ion-footer>
