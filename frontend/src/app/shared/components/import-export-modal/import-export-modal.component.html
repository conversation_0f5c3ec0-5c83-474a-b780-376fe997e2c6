<ion-header translucent>
  <ion-toolbar color="primary">
    <ion-title>Data Management</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()" fill="clear" color="light">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding modal-content">
      <div class="modal-header">
        <ion-icon name="cloud-upload-outline" class="header-icon"></ion-icon>
        <h2 class="modal-title">Import & Export Data</h2>
        <p class="modal-subtitle">Manage your {{modelName}} data using CSV files</p>
      </div>

      <div class="action-sections">
        <!-- Import Section -->
        <div class="action-section import-section">
          <div class="section-header">
            <ion-icon name="cloud-upload" class="section-icon import-icon"></ion-icon>
            <h3 class="section-title">Import Data</h3>
            <p class="section-description">Upload a CSV file to import {{modelName}} data</p>
          </div>

          <div class="file-input-wrapper">
            <input
              #fileInput
              type="file"
              (change)="onFileSelected($event)"
              accept=".csv"
              class="file-input"
              id="csvFileInputModal">

            <ion-button
              fill="outline"
              expand="block"
              class="file-select-btn"
              (click)="fileInput.click()">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              {{selectedFile ? selectedFile.name : 'Choose CSV File'}}
            </ion-button>
          </div>

          <ion-button
            expand="block"
            fill="solid"
            color="success"
            class="action-btn upload-btn"
            [disabled]="!selectedFile"
            (click)="uploadCsv()">
            <ion-icon name="cloud-upload" slot="start"></ion-icon>
            Import CSV
          </ion-button>

          <div class="file-requirements">
            <ion-text color="medium">
              <small>
                <ion-icon name="information-circle-outline"></ion-icon>
                File requirements: CSV format, max 10MB
              </small>
            </ion-text>
          </div>
        </div>

        <!-- Export Section -->
        <div class="action-section export-section">
          <div class="section-header">
            <ion-icon name="cloud-download" class="section-icon export-icon"></ion-icon>
            <h3 class="section-title">Export Data</h3>
            <p class="section-description">Download current {{modelName}} data as CSV</p>
          </div>

          <ion-button
            expand="block"
            fill="solid"
            color="primary"
            class="action-btn download-btn"
            (click)="downloadCsv()">
            <ion-icon name="cloud-download" slot="start"></ion-icon>
            Export CSV
          </ion-button>

          <div class="export-info">
            <ion-text color="medium">
              <small>
                <ion-icon name="download-outline"></ion-icon>
                Downloads all current {{modelName}} records
              </small>
            </ion-text>
          </div>
        </div>
      </div>
</ion-content>
