# Standardized Modal & Form Components

This document provides comprehensive documentation for the new standardized modal and form components that ensure consistent UI/UX across the King Bill application.

## Overview

The standardized components provide:
- **Consistent Design**: Uniform styling, spacing, and typography
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Responsive Design**: Mobile-first approach with touch-friendly interfaces
- **Form Validation**: Real-time validation with clear error messages
- **Loading States**: Proper loading indicators and disabled states
- **Error Handling**: Consistent error display and recovery

## Components

### 1. Standard Modal Component (`StandardModalComponent`)

A reusable modal wrapper that provides consistent structure, styling, and behavior.

#### Features
- Configurable header with title and subtitle
- Consistent button layout and styling
- Loading states and backdrop management
- Accessibility features (focus management, keyboard navigation)
- Responsive design across all screen sizes

#### Usage

```typescript
import { ModalConfig, ModalButton } from '../shared/components/shared-components.module';

// In your component
modalConfig: ModalConfig = {
  title: 'Add New Item',
  subtitle: 'Create a new item in the system',
  size: 'large',
  color: 'primary',
  showCloseButton: true,
  showBackdrop: true,
  backdropDismiss: true,
  buttons: [
    {
      text: 'Cancel',
      color: 'medium',
      fill: 'outline',
      icon: 'close-outline',
      handler: () => this.closeModal()
    },
    {
      text: 'Save',
      color: 'primary',
      fill: 'solid',
      icon: 'checkmark-outline',
      type: 'submit',
      disabled: false
    }
  ]
};
```

```html
<app-standard-modal
  [config]="modalConfig"
  [isOpen]="isModalOpen"
  (modalClosed)="onModalClosed()"
  (buttonClicked)="onButtonClick($event)">
  
  <!-- Your modal content here -->
  <div class="modal-content">
    <p>This is the modal content.</p>
  </div>
</app-standard-modal>
```

#### Configuration Options

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `title` | string | 'Modal' | Modal title |
| `subtitle` | string | undefined | Optional subtitle |
| `size` | 'small' \| 'medium' \| 'large' \| 'fullscreen' | 'medium' | Modal size |
| `color` | 'primary' \| 'secondary' \| 'success' \| 'warning' \| 'danger' \| 'medium' | 'primary' | Header color |
| `showCloseButton` | boolean | true | Show close button |
| `showBackdrop` | boolean | true | Show backdrop |
| `backdropDismiss` | boolean | true | Allow backdrop dismissal |
| `cssClass` | string | undefined | Additional CSS classes |
| `buttons` | ModalButton[] | [] | Action buttons |
| `loading` | boolean | false | Show loading state |
| `disabled` | boolean | false | Disable modal interactions |

### 2. Standard Form Component (`StandardFormComponent`)

A comprehensive form component with built-in validation, styling, and accessibility features.

#### Features
- Section-based form organization
- Real-time validation with error messages
- Multiple input types (text, number, email, select, etc.)
- Help text and field descriptions
- Responsive design
- Accessibility compliance

#### Usage

```typescript
import { FormSection, FormField } from '../shared/components/shared-components.module';

// In your component
formSections: FormSection[] = [
  {
    title: 'Basic Information',
    icon: 'information-circle-outline',
    fields: [
      {
        name: 'name',
        label: 'Full Name',
        type: 'text',
        required: true,
        placeholder: 'Enter your full name',
        validation: [Validators.minLength(2), Validators.maxLength(50)],
        helpText: 'Enter your complete name as it appears on official documents'
      },
      {
        name: 'email',
        label: 'Email Address',
        type: 'email',
        required: true,
        placeholder: 'Enter your email address',
        validation: [Validators.email],
        errorMessage: 'Please enter a valid email address'
      },
      {
        name: 'age',
        label: 'Age',
        type: 'number',
        required: true,
        placeholder: 'Enter your age',
        min: 18,
        max: 100,
        helpText: 'Must be between 18 and 100 years'
      }
    ]
  },
  {
    title: 'Preferences',
    icon: 'settings-outline',
    collapsible: true,
    collapsed: false,
    fields: [
      {
        name: 'newsletter',
        label: 'Subscribe to Newsletter',
        type: 'checkbox',
        placeholder: 'Receive updates and promotions'
      },
      {
        name: 'category',
        label: 'Preferred Category',
        type: 'select',
        required: true,
        placeholder: 'Select your preferred category',
        options: [
          { value: 'tech', label: 'Technology' },
          { value: 'business', label: 'Business' },
          { value: 'lifestyle', label: 'Lifestyle' }
        ]
      }
    ]
  }
];
```

```html
<app-standard-form
  [sections]="formSections"
  [loading]="isLoading"
  [disabled]="isDisabled"
  submitButtonText="Create Account"
  cancelButtonText="Cancel"
  submitButtonColor="primary"
  cancelButtonColor="medium"
  submitButtonIcon="person-add-outline"
  cancelButtonIcon="close-outline"
  (formSubmit)="onFormSubmit($event)"
  (formCancel)="onFormCancel()"
  (formChange)="onFormChange($event)">
</app-standard-form>
```

#### Field Types

| Type | Description | Additional Properties |
|------|-------------|----------------------|
| `text` | Text input | `maxlength`, `pattern` |
| `number` | Number input | `min`, `max`, `step` |
| `email` | Email input | `pattern` |
| `tel` | Telephone input | `pattern` |
| `date` | Date picker | `min`, `max` |
| `time` | Time picker | `min`, `max` |
| `datetime-local` | Date and time picker | `min`, `max` |
| `password` | Password input | `pattern` |
| `textarea` | Multi-line text | `rows`, `maxlength` |
| `select` | Dropdown selection | `options` |
| `checkbox` | Checkbox input | - |
| `radio` | Radio button group | `options` |
| `file` | File upload | `accept`, `multiple` |

### 3. Ledger Create Modal Component (`LedgerCreateModalComponent`)

A specialized modal component for creating ledger entries with improved UX.

#### Features
- Transaction type selection (Pay-In, Pay-Out, Party Transfer)
- Dynamic party selection based on transaction type
- Payment mode selection
- Image upload with preview
- Form validation and error handling

#### Usage

```typescript
// In your component
isLedgerModalOpen = false;
buyerOptions = [/* buyer data */];
supplierOptions = [/* supplier data */];
partyOptions = [/* party data */];
modeOfPaymentOptions = [/* payment modes */];

onLedgerFormSubmit(formData: any) {
  console.log('Ledger form submitted:', formData);
  // Handle form submission
}
```

```html
<app-ledger-create-modal
  [isOpen]="isLedgerModalOpen"
  [loading]="isLoading"
  [buyerOptions]="buyerOptions"
  [supplierOptions]="supplierOptions"
  [partyOptions]="partyOptions"
  [modeOfPaymentOptions]="modeOfPaymentOptions"
  [ledgerFieldName]="ledgerFieldName"
  (modalClosed)="isLedgerModalOpen = false"
  (formSubmit)="onLedgerFormSubmit($event)">
</app-ledger-create-modal>
```

## Best Practices

### 1. Modal Usage

- **Keep modals focused**: One primary action per modal
- **Use appropriate sizes**: Small for confirmations, large for forms
- **Provide clear titles**: Descriptive and action-oriented
- **Handle loading states**: Show spinners during async operations
- **Validate before closing**: Prevent data loss

### 2. Form Design

- **Group related fields**: Use sections for logical organization
- **Provide helpful labels**: Clear, descriptive field labels
- **Show validation early**: Real-time feedback improves UX
- **Use appropriate input types**: Leverage HTML5 input types
- **Include help text**: Explain complex fields or requirements

### 3. Accessibility

- **Use semantic HTML**: Proper form structure and labels
- **Provide keyboard navigation**: Tab order and keyboard shortcuts
- **Include ARIA labels**: Screen reader support
- **Test with assistive technology**: Ensure compatibility
- **Use sufficient color contrast**: Meet WCAG guidelines

### 4. Responsive Design

- **Mobile-first approach**: Design for small screens first
- **Touch-friendly targets**: Minimum 44px touch targets
- **Flexible layouts**: Adapt to different screen sizes
- **Readable text**: Appropriate font sizes for mobile

## Migration Guide

### From Old Modal Pattern

**Before:**
```html
<ion-modal [isOpen]="isModalOpen">
  <ng-template>
    <ion-header translucent>
      <ion-toolbar>
        <ion-title>Add Item</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeModal()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <!-- Form content -->
    </ion-content>
  </ng-template>
</ion-modal>
```

**After:**
```html
<app-standard-modal
  [config]="modalConfig"
  [isOpen]="isModalOpen"
  (modalClosed)="closeModal()">
  
  <app-standard-form
    [sections]="formSections"
    (formSubmit)="onSubmit($event)">
  </app-standard-form>
</app-standard-modal>
```

### Benefits of Migration

1. **Consistency**: Uniform appearance across all modals
2. **Maintainability**: Centralized styling and behavior
3. **Accessibility**: Built-in accessibility features
4. **Performance**: Optimized components with better rendering
5. **Developer Experience**: Easier to implement and maintain

## Testing

### Unit Tests

```typescript
describe('StandardModalComponent', () => {
  it('should emit modalClosed when close button is clicked', () => {
    // Test implementation
  });

  it('should handle button clicks correctly', () => {
    // Test implementation
  });
});
```

### Integration Tests

```typescript
describe('LedgerCreateModalComponent', () => {
  it('should validate form fields correctly', () => {
    // Test implementation
  });

  it('should handle file uploads', () => {
    // Test implementation
  });
});
```

### Accessibility Tests

- Screen reader compatibility
- Keyboard navigation
- Focus management
- Color contrast
- ARIA label validation

## Troubleshooting

### Common Issues

1. **Modal not opening**: Check `isOpen` binding and modal configuration
2. **Form validation errors**: Verify field configuration and validation rules
3. **Styling inconsistencies**: Ensure proper CSS imports and theme variables
4. **Accessibility issues**: Test with screen readers and keyboard navigation

### Debug Tips

- Use browser dev tools to inspect component structure
- Check console for Angular errors
- Verify data binding and event handling
- Test on different devices and screen sizes

## Support

For questions or issues with the standardized components:

1. Check this documentation first
2. Review the component source code
3. Test with the provided examples
4. Create an issue with detailed reproduction steps

## Future Enhancements

- [ ] Advanced form validation patterns
- [ ] Custom field types
- [ ] Form state management
- [ ] Enhanced accessibility features
- [ ] Performance optimizations
- [ ] Additional modal animations 