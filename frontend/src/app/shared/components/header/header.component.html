<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="toggleMenu()" fill="clear">
        <ion-icon name="menu" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button *ngIf="returnUrl" (click)="routerServices.routerFunction(returnUrl)" fill="clear">
        <ion-icon [name]="(returnUrl=='tabs/home')?'home':'arrow-back'" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>

    <ion-title>{{title}}</ion-title>

    <ion-buttons slot="end">

      <ion-button (click)="logout()" fill="clear">
        <ion-icon name="log-out" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>