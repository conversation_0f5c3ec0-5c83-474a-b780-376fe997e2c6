.popover-content {
  --width: 80%;
  max-height: 400px;
  padding: 0;
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

// Header
.popover-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: var(--ion-color-primary);
  color: white;
  
  .header-info {
    flex: 1;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    p {
      margin: 4px 0 0 0;
      font-size: 12px;
      opacity: 0.8;
    }
  }
  
  ion-button {
    --color: white;
    --padding-start: 8px;
    --padding-end: 8px;
    height: 32px;
    width: 32px;
  }
}

// Summary Section
.summary-section {
  padding: 12px 16px;
  background: var(--ion-color-light);
  border-bottom: 1px solid var(--ion-color-light-shade);
  display: flex;
  gap: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  
  .summary-icon {
    font-size: 14px;
    color: var(--ion-color-medium);
  }
  
  .summary-text {
    font-size: 12px;
    font-weight: 500;
    color: var(--ion-color-dark);
  }
}

// Shops List
.shops-list {
  max-height: 240px;
  overflow-y: auto;
  padding: 8px 0;
}

.shop-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--ion-color-light-shade);
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: var(--ion-color-light);
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.shop-info {
  flex: 1;
  min-width: 0; // Allow text truncation
}

.shop-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
  
  .shop-name {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--ion-color-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 8px;
  }
}

.shop-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  
  .detail-icon {
    font-size: 12px;
    color: var(--ion-color-medium);
    flex-shrink: 0;
  }
  
  .detail-text {
    font-size: 12px;
    color: var(--ion-color-medium);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.shop-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  
  ion-button {
    --padding-start: 6px;
    --padding-end: 6px;
    height: 28px;
    width: 28px;
    
    ion-icon {
      font-size: 16px;
    }
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 32px 16px;
  
  .empty-icon {
    font-size: 48px;
    color: var(--ion-color-success);
    margin-bottom: 12px;
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    font-size: 12px;
    color: var(--ion-color-medium);
  }
}

// Footer
.popover-footer {
  padding: 12px 16px;
  background: var(--ion-color-light);
  border-top: 1px solid var(--ion-color-light-shade);
  
  ion-button {
    --border-radius: 8px;
    height: 36px;
    font-size: 12px;
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .popover-content {
    --width: 95%;
  }

  .shop-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .shop-name {
      margin-right: 0;
    }
  }
}
