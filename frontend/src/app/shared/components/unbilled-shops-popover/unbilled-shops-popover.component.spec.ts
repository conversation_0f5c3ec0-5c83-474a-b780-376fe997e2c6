import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { UnbilledShopsPopoverComponent } from './unbilled-shops-popover.component';

describe('UnbilledShopsPopoverComponent', () => {
  let component: UnbilledShopsPopoverComponent;
  let fixture: ComponentFixture<UnbilledShopsPopoverComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ UnbilledShopsPopoverComponent ],
      imports: [IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(UnbilledShopsPopoverComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
