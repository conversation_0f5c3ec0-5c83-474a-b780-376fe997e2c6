<div class="popover-content">
  <!-- Header -->
  <div class="popover-header">
    <div class="header-info">
      <h3>Unbilled Shops</h3>
      <p>{{routeName}} - {{date}}</p>
    </div>
    <ion-button fill="clear" size="small" (click)="closePopover()">
      <ion-icon name="close-outline"></ion-icon>
    </ion-button>
  </div>

  <!-- Summary -->
  <div class="summary-section" *ngIf="shops.length > 0">
    <div class="summary-item">
      <ion-icon name="storefront-outline" class="summary-icon"></ion-icon>
      <span class="summary-text">{{shops.length}} shop{{shops.length !== 1 ? 's' : ''}} pending</span>
    </div>
    <div class="summary-item" *ngIf="hasShopsWithExpectedAmount()">
      <ion-icon name="cash-outline" class="summary-icon"></ion-icon>
      <span class="summary-text">
        {{formatAmount(getTotalExpectedAmount())}} expected
      </span>
    </div>
  </div>

  <!-- Shops List -->
  <div class="shops-list" *ngIf="shops.length > 0">
    <div 
      *ngFor="let shop of shops; trackBy: trackByShopId" 
      class="shop-item"
      (click)="onShopClick(shop)">
      
      <div class="shop-info">
        <div class="shop-header">
          <h4 class="shop-name">{{shop.name}}</h4>
          <ion-chip [color]="getShopStatusColor(shop)" size="small">
            <ion-label>{{getLastBilledText(shop)}}</ion-label>
          </ion-chip>
        </div>
        
        <div class="shop-details">
          <div class="detail-item" *ngIf="shop.place">
            <ion-icon name="location-outline" class="detail-icon"></ion-icon>
            <span class="detail-text">{{shop.place}}</span>
          </div>
          
          <div class="detail-item" *ngIf="shop.expected_amount">
            <ion-icon name="cash-outline" class="detail-icon"></ion-icon>
            <span class="detail-text">{{formatAmount(shop.expected_amount)}}</span>
          </div>
        </div>
      </div>

      <div class="shop-actions">
        <ion-button 
          fill="clear" 
          size="small" 
          (click)="onCallShop($event, shop)"
          *ngIf="shop.phone_no">
          <ion-icon name="call-outline" slot="icon-only"></ion-icon>
        </ion-button>
        
        <ion-button fill="clear" size="small">
          <ion-icon name="chevron-forward-outline" slot="icon-only"></ion-icon>
        </ion-button>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="shops.length === 0">
    <ion-icon name="checkmark-circle-outline" class="empty-icon"></ion-icon>
    <h4>All Shops Billed!</h4>
    <p>All shops in this route have been billed for {{date}}</p>
  </div>

  <!-- Footer Actions -->
  <div class="popover-footer" *ngIf="shops.length > 0">
    <ion-button 
      fill="outline" 
      size="small" 
      expand="block"
      (click)="closePopover()">
      <ion-icon name="list-outline" slot="start"></ion-icon>
      View All in Sales Bill
    </ion-button>
  </div>
</div>
