<div class="photo-footer" [class.expanded]="isExpanded">
  <!-- Footer Header (Always Visible) -->
  <div class="footer-header" (click)="toggleExpanded()">
    <div class="footer-stats">
      <div class="stat-item">
        <ion-icon name="checkmark-circle-outline" class="stat-icon success"></ion-icon>
        <div class="stat-info">
          <span class="stat-value">{{shopsWithTodayPhotos}}/{{totalShops}}</span>
          <span class="stat-label">Today's Photos</span>
        </div>
      </div>

      <div class="stat-item pending" *ngIf="shopsPending > 0">
        <ion-icon name="time-outline" class="stat-icon warning"></ion-icon>
        <div class="stat-info">
          <span class="stat-value">{{shopsPending}}</span>
          <span class="stat-label">Pending Today</span>
        </div>
      </div>

      <div class="completion-indicator">
        <div class="completion-circle" [attr.data-percentage]="getCompletionPercentageForCSS()">
          <span class="completion-text">{{getCompletionPercentage()}}%</span>
        </div>
      </div>
    </div>
    
    <div class="footer-actions">
      <ion-button 
        fill="clear" 
        size="small" 
        (click)="refreshStatistics(); $event.stopPropagation()"
        [disabled]="loading">
        <ion-icon name="refresh-outline" slot="icon-only"></ion-icon>
      </ion-button>
      
      <ion-button fill="clear" size="small" class="expand-button">
        <ion-icon 
          [name]="isExpanded ? 'chevron-down-outline' : 'chevron-up-outline'" 
          slot="icon-only">
        </ion-icon>
      </ion-button>
    </div>
  </div>

  <!-- Expandable Content -->
  <div class="footer-content" [class.visible]="isExpanded">
    <!-- Loading State -->
    <div class="loading-section" *ngIf="loading">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading photo statistics...</p>
    </div>

    <!-- Shops List -->
    <div class="shops-section" *ngIf="!loading && allShops.length > 0">
      <div class="section-header">
        <h4 class="section-title">
          <ion-icon name="camera-outline" class="section-icon"></ion-icon>
          Today's Photo Status ({{allShops.length}} shops)
        </h4>
      </div>
      
      <div class="shops-list">
        <div
          *ngFor="let shop of allShops; trackBy: trackByShopId"
          class="shop-item"
          [class.has-today-photos]="shop.has_today_photos">

          <div class="shop-info">
            <div class="shop-header">
              <div class="shop-name-container">
                <h5 class="shop-name">{{shop.name}}</h5>
                <ion-icon
                  *ngIf="shop.has_today_photos"
                  name="checkmark-circle"
                  class="today-photo-indicator">
                </ion-icon>
              </div>
              <span class="shop-route" *ngIf="shop.route_name">{{shop.route_name}}</span>
            </div>

            <div class="shop-details">
              <span class="shop-place" *ngIf="shop.place">
                <ion-icon name="location-outline"></ion-icon>
                {{shop.place}}
              </span>

              <span class="shop-phone" *ngIf="shop.phone_no">
                <ion-icon name="call-outline"></ion-icon>
                {{shop.phone_no}}
              </span>

              <span class="shop-photo-status" *ngIf="shop.has_today_photos">
                <ion-icon name="camera"></ion-icon>
                {{shop.today_photo_count}} photo{{shop.today_photo_count > 1 ? 's' : ''}} today
              </span>
            </div>
          </div>

          <div class="shop-actions">
            <ion-button
              [fill]="shop.has_today_photos ? 'outline' : 'solid'"
              size="small"
              [color]="shop.has_today_photos ? 'success' : 'primary'"
              (click)="openShopImageModal(shop)">
              <ion-icon [name]="shop.has_today_photos ? 'images-outline' : 'camera-outline'" slot="start"></ion-icon>
              {{shop.has_today_photos ? 'View Photos' : 'Add Photo'}}
            </ion-button>
          </div>
        </div>
      </div>
    </div>

    <!-- All Complete State -->
    <div class="complete-section" *ngIf="!loading && shopsPending === 0 && totalShops > 0">
      <div class="complete-content">
        <ion-icon name="checkmark-circle-outline" class="complete-icon"></ion-icon>
        <h4>All Today's Photos Complete!</h4>
        <p>All {{totalShops}} shops have photos uploaded today</p>
      </div>
    </div>

    <!-- No Shops State -->
    <div class="empty-section" *ngIf="!loading && totalShops === 0">
      <div class="empty-content">
        <ion-icon name="storefront-outline" class="empty-icon"></ion-icon>
        <h4>No Shops Found</h4>
        <p>No shops available for photo upload</p>
      </div>
    </div>
  </div>
</div>
