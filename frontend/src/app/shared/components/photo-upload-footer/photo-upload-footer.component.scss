// Photo Footer Container
.photo-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--ion-color-light-shade);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
  max-height: 80px;
  overflow: hidden;
  
  &.expanded {
    max-height: 70vh;
    overflow-y: auto;
  }
}

// Footer Header (Always Visible)
.footer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  background: white;
  border-bottom: 1px solid var(--ion-color-light-shade);
  min-height: 56px;
}

.footer-stats {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .stat-icon {
    font-size: 18px;
    color: var(--ion-color-primary);

    &.warning {
      color: var(--ion-color-warning);
    }

    &.success {
      color: var(--ion-color-success);
    }
  }
  
  .stat-info {
    display: flex;
    flex-direction: column;
    
    .stat-value {
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-dark);
      line-height: 1.2;
    }
    
    .stat-label {
      font-size: 11px;
      color: var(--ion-color-medium);
      line-height: 1.2;
    }
  }
  
  &.pending .stat-value {
    color: var(--ion-color-warning);
  }
}

.completion-indicator {
  margin-left: auto;
  margin-right: 12px;
}

.completion-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: conic-gradient(
    var(--ion-color-success) 0deg,
    var(--ion-color-success) calc(var(--percentage, 0) * 3.6deg),
    var(--ion-color-light) calc(var(--percentage, 0) * 3.6deg),
    var(--ion-color-light) 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    width: 32px;
    height: 32px;
    background: white;
    border-radius: 50%;
  }
  
  .completion-text {
    position: relative;
    z-index: 1;
    font-size: 10px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  
  ion-button {
    --padding-start: 8px;
    --padding-end: 8px;
    height: 32px;
    width: 32px;
  }
}

// Expandable Content
.footer-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--ion-color-light);
  
  &.visible {
    max-height: 60vh;
    overflow-y: auto;
  }
}

// Loading Section
.loading-section {
  text-align: center;
  padding: 24px;
  
  ion-spinner {
    margin-bottom: 12px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

// Pending Section
.pending-section {
  padding: 16px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
  
  .section-icon {
    font-size: 18px;
    color: var(--ion-color-primary);
  }
}

// Shops List
.shops-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shop-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &.has-today-photos {
    background: #f0f9f0;
    border-left: 4px solid var(--ion-color-success);
  }
}

.shop-info {
  flex: 1;
  min-width: 0;
}

.shop-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;

  .shop-name-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .shop-name {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-dark);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }

    .today-photo-indicator {
      font-size: 16px;
      color: var(--ion-color-success);
      flex-shrink: 0;
    }
  }

  .shop-route {
    font-size: 11px;
    color: var(--ion-color-medium);
    background: var(--ion-color-light);
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
  }
}

.shop-details {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .shop-place,
  .shop-phone,
  .shop-photo-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--ion-color-medium);

    ion-icon {
      font-size: 12px;
    }
  }

  .shop-photo-status {
    color: var(--ion-color-success);
    font-weight: 500;

    ion-icon {
      color: var(--ion-color-success);
    }
  }
}

.shop-actions {
  margin-left: 12px;
  
  ion-button {
    --border-radius: 6px;
    height: 32px;
    font-size: 12px;
    font-weight: 500;
  }
}

// Complete Section
.complete-section {
  padding: 32px 16px;
}

.complete-content {
  text-align: center;
  
  .complete-icon {
    font-size: 48px;
    color: var(--ion-color-success);
    margin-bottom: 12px;
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

// Empty Section
.empty-section {
  padding: 32px 16px;
}

.empty-content {
  text-align: center;
  
  .empty-icon {
    font-size: 48px;
    color: var(--ion-color-medium);
    margin-bottom: 12px;
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .footer-stats {
    gap: 12px;
  }
  
  .stat-item .stat-info {
    .stat-value {
      font-size: 13px;
    }
    
    .stat-label {
      font-size: 10px;
    }
  }
  
  .completion-circle {
    width: 36px;
    height: 36px;
    
    &::before {
      width: 28px;
      height: 28px;
    }
    
    .completion-text {
      font-size: 9px;
    }
  }
  
  .shop-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    
    .shop-route {
      margin-left: 0;
    }
  }
  
  .shop-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .shop-actions {
    margin-left: 0;
    width: 100%;
    
    ion-button {
      width: 100%;
    }
  }
}

// Dynamic completion percentage
.completion-circle {
  --percentage: 0;
}

// Generate percentage classes
.completion-circle[data-percentage="0"] { --percentage: 0; }
.completion-circle[data-percentage="10"] { --percentage: 10; }
.completion-circle[data-percentage="20"] { --percentage: 20; }
.completion-circle[data-percentage="30"] { --percentage: 30; }
.completion-circle[data-percentage="40"] { --percentage: 40; }
.completion-circle[data-percentage="50"] { --percentage: 50; }
.completion-circle[data-percentage="60"] { --percentage: 60; }
.completion-circle[data-percentage="70"] { --percentage: 70; }
.completion-circle[data-percentage="80"] { --percentage: 80; }
.completion-circle[data-percentage="90"] { --percentage: 90; }
.completion-circle[data-percentage="100"] { --percentage: 100; }
