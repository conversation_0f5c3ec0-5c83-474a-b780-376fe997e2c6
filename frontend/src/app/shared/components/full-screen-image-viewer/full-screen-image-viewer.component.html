<ion-header class="ion-no-border">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="close-outline" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>
    
    <ion-title class="ion-text-center">
      <div class="viewer-title">
        <ion-icon name="image-outline" class="title-icon"></ion-icon>
        Photo Viewer
      </div>
    </ion-title>
    
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="openInBrowser()" title="Open in Browser">
        <ion-icon name="open-outline" size="large"></ion-icon>
      </ion-button>
      <ion-button fill="clear" (click)="shareImage()" title="Share">
        <ion-icon name="share-outline" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="viewer-content">
  <div class="image-container">
    <img 
      [src]="getImageUrl()" 
      [alt]="image.notes || 'Shop photo'"
      class="full-screen-image"
      loading="lazy" />
  </div>
  
  <div class="image-info">
    <ion-card class="info-card">
      <ion-card-content>
        <div class="info-header">
          <h3 class="image-title">
            <ion-icon name="information-circle-outline" class="info-icon"></ion-icon>
            Photo Details
          </h3>
        </div>
        
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">Date:</span>
            <span class="info-value">{{formatDate(image.date_taken)}}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">Time:</span>
            <span class="info-value">{{formatTime(image.time_taken)}}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">Uploaded by:</span>
            <span class="info-value">{{image.uploaded_by_name}}</span>
          </div>
          
          <div class="info-item" *ngIf="image.notes">
            <span class="info-label">Notes:</span>
            <span class="info-value">{{image.notes}}</span>
          </div>
          
          <div class="info-item" *ngIf="image.location_latitude && image.location_longitude">
            <span class="info-label">Location:</span>
            <span class="info-value">
              {{image.location_latitude | number:'1.6-6'}}, {{image.location_longitude | number:'1.6-6'}}
            </span>
          </div>
        </div>
        
        <div class="action-buttons">
          <ion-button 
            expand="block" 
            fill="outline" 
            color="primary"
            (click)="openInBrowser()">
            <ion-icon name="open-outline" slot="start"></ion-icon>
            Open in Browser
          </ion-button>
          
          <ion-button 
            expand="block" 
            fill="outline" 
            color="secondary"
            (click)="shareImage()">
            <ion-icon name="share-outline" slot="start"></ion-icon>
            Share Image
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content> 