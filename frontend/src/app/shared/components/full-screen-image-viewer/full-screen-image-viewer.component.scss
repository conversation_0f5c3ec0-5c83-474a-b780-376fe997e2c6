// Full Screen Image Viewer Styles
.viewer-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  
  .title-icon {
    font-size: 20px;
    color: var(--ion-color-primary);
  }
}

.viewer-content {
  --background: #000;
  --padding-top: 0;
  --padding-bottom: 0;
}

// Image Container
.image-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  
  .full-screen-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

// Image Info Section
.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px 16px 16px;
  
  .info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    margin: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  }
  
  .info-header {
    margin-bottom: 16px;
    
    .image-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
      
      .info-icon {
        font-size: 18px;
        color: var(--ion-color-primary);
      }
    }
  }
  
  .info-grid {
    display: grid;
    gap: 12px;
    margin-bottom: 20px;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 12px;
    
    .info-label {
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-dark);
      min-width: 80px;
    }
    
    .info-value {
      font-size: 14px;
      color: var(--ion-color-medium);
      text-align: right;
      flex: 1;
      word-break: break-word;
    }
  }
  
  .action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    
    ion-button {
      --border-radius: 12px;
      height: 44px;
      font-weight: 600;
      font-size: 14px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .image-info {
    padding: 16px 12px 12px;
    
    .info-grid {
      gap: 10px;
    }
    
    .info-item {
      .info-label {
        font-size: 13px;
        min-width: 70px;
      }
      
      .info-value {
        font-size: 13px;
      }
    }
    
    .action-buttons {
      grid-template-columns: 1fr;
      gap: 8px;
      
      ion-button {
        height: 40px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .viewer-title {
    font-size: 16px;
    
    .title-icon {
      font-size: 18px;
    }
  }
  
  .image-info {
    .info-header .image-title {
      font-size: 14px;
    }
    
    .info-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      
      .info-label {
        min-width: auto;
      }
      
      .info-value {
        text-align: left;
      }
    }
  }
}

// Animation for modal entry
ion-modal {
  --backdrop-opacity: 0.9;
}

// Custom modal class for full screen
.full-screen-image-modal {
  --width: 100%;
  --height: 100%;
  --border-radius: 0;
  --background: #000;
}

// Loading state for image
.full-screen-image {
  transition: opacity 0.3s ease;
  
  &:not([src]) {
    opacity: 0;
  }
  
  &[src] {
    opacity: 1;
  }
} 