import { Component, OnInit, Input, ViewChild, ElementRef } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ActionSheetController } from '@ionic/angular';

@Component({
  selector: 'app-dynamic-form-field',
  templateUrl: './dynamic-form-field.component.html',
  styleUrls: ['./dynamic-form-field.component.scss'],
})
export class DynamicFormFieldComponent implements OnInit {
  @Input() field: any;
  @Input() form: FormGroup;
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('cameraInput', { static: false }) cameraInput!: ElementRef<HTMLInputElement>;

  // Camera/File selection properties
  selectedFile: File | null = null;
  previewUrl: string | null = null;
  captureMode: 'camera' | 'gallery' = 'camera';

  constructor(private actionSheetController: ActionSheetController) { }

  ngOnInit() {}
  
  async presentImageOptions() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Add Image',
      cssClass: 'image-options-action-sheet',
      buttons: [
        {
          text: 'Take Photo',
          icon: 'camera-outline',
          handler: () => {
            this.openCamera();
          }
        },
        {
          text: 'Choose from Gallery',
          icon: 'images-outline',
          handler: () => {
            this.openGallery();
          }
        },
        {
          text: 'Cancel',
          icon: 'close-outline',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }

  openCamera() {
    this.captureMode = 'camera';
    if (this.cameraInput) {
      this.cameraInput.nativeElement.click();
    }
  }

  openGallery() {
    this.captureMode = 'gallery';
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  handleFileSelection(file: File) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Please select a valid image file');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.error('Image size should not exceed 10MB');
      return;
    }

    this.selectedFile = file;
    
    // Set form value
    if (this.field.multiple) {
      const currentFiles = this.form.controls[this.field.name].value || [];
      this.form.controls[this.field.name].setValue([...currentFiles, file]);
    } else {
      this.form.controls[this.field.name].setValue(file);
    }
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      this.previewUrl = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }

  clearImageSelection() {
    this.selectedFile = null;
    this.previewUrl = null;
    
    // Clear form value
    if (this.field.multiple) {
      this.form.controls[this.field.name].setValue([]);
    } else {
      this.form.controls[this.field.name].setValue(null);
    }
    
    // Reset file inputs
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    if (this.cameraInput) {
      this.cameraInput.nativeElement.value = '';
    }
  }
  
  onFileChange(event: any, fieldName: string, multiple: boolean) {
    const files = event.target.files;
    if (multiple) {
      this.form.controls[fieldName].setValue(files);
    } else {
      this.form.controls[fieldName].setValue(files.length > 0 ? files[0] : null);
    }
  }
  setChecked(field,event){
    this.form.controls[field].setValue(event?.detail?.checked)
  }
  setValue(field,event){
    this.form.controls[field].setValue(event?.detail?.value)
  }
}
