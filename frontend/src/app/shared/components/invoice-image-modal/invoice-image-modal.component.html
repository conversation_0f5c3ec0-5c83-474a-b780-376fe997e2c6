<ion-header>
  <ion-toolbar>
    <ion-title>
      <div class="modal-title">
        <ion-icon name="images-outline" class="title-icon"></ion-icon>
        Invoice Images
      </div>
      <div class="modal-subtitle" *ngIf="invoiceData">
        {{invoiceData.name}} - INV-{{invoiceData.id}}
      </div>
    </ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="modal-content">
  <!-- Upload Section -->
  <div class="upload-section">
    <ion-card class="upload-card">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="cloud-upload-outline" class="upload-icon"></ion-icon>
          Add New Image
        </ion-card-title>
        <ion-card-subtitle>Take a photo or choose from gallery</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <!-- Image Preview -->
        <div class="image-preview" *ngIf="previewUrl">
          <img [src]="previewUrl" alt="Preview" class="preview-image" />
          <ion-button 
            fill="clear" 
            size="small" 
            class="remove-preview-btn"
            (click)="clearSelection()">
            <ion-icon name="close-circle" slot="icon-only"></ion-icon>
          </ion-button>
        </div>

        <!-- Upload Controls -->
        <div class="upload-controls" *ngIf="!previewUrl">
          <ion-button 
            expand="block" 
            fill="outline" 
            class="select-image-btn"
            (click)="presentImageOptions()">
            <ion-icon name="camera-outline" slot="start"></ion-icon>
            Add Image
          </ion-button>
        </div>

        <!-- Description Input -->
        <div class="description-section" *ngIf="selectedFile">
          <ion-item lines="none">
            <ion-label position="stacked">Description (Optional)</ion-label>
            <ion-textarea
              [(ngModel)]="description"
              placeholder="Add description about this image..."
              rows="3"
              maxlength="200">
            </ion-textarea>
          </ion-item>
        </div>

        <!-- Upload Button -->
        <div class="upload-actions" *ngIf="selectedFile">
          <ion-button 
            expand="block" 
            fill="solid" 
            color="primary"
            [disabled]="uploading"
            (click)="uploadImage()">
            <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
            <span *ngIf="!uploading">Upload Image</span>
            <span *ngIf="uploading">Uploading...</span>
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Images Gallery -->
  <div class="images-section">
    <div class="section-header">
      <h3 class="section-title">
        <ion-icon name="images-outline" class="section-icon"></ion-icon>
        Image Gallery ({{images.length}})
      </h3>
    </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="loading">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading images...</p>
    </div>

    <!-- Images Grid -->
    <div class="images-grid" *ngIf="!loading && images.length > 0">
      <div 
        *ngFor="let image of images; trackBy: trackByImageId" 
        class="image-item">
        
        <div class="image-container" (click)="viewFullImage(image)">
          <img 
            [src]="image.image_url" 
            [alt]="image.description || 'Invoice image'"
            class="image-thumbnail"
            loading="lazy" />
          
          <div class="image-overlay">
            <ion-icon name="eye-outline" class="view-icon"></ion-icon>
          </div>
        </div>

        <div class="image-info">
          <div class="image-meta">
            <span class="image-date">{{formatDate(image.uploaded_at)}}</span>
            <span class="image-size">{{formatFileSize(image.file_size)}}</span>
          </div>
          
          <div class="image-description" *ngIf="image.description">
            <p>{{image.description}}</p>
          </div>
          
          <div class="image-actions">
            <ion-button 
              fill="clear" 
              size="small" 
              color="danger"
              (click)="deleteImage(image)">
              <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="!loading && images.length === 0">
      <ion-icon name="images-outline" class="empty-icon"></ion-icon>
      <h4>No Images Yet</h4>
      <p>Start by adding your first image for this invoice</p>
    </div>
  </div>
</ion-content>

<ion-footer *ngIf="images.length > 0">
  <ion-toolbar>
    <div class="footer-info">
      <ion-text color="medium">
        <small>
          <ion-icon name="information-circle-outline"></ion-icon>
          Tap on any image to view full size
        </small>
      </ion-text>
    </div>
  </ion-toolbar>
</ion-footer> 