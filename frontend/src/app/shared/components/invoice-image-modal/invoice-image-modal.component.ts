import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>ontroller, AlertController, ActionSheetController } from '@ionic/angular';
import { InvoiceImageService, InvoiceImage } from '../../services/invoice-image.service';
import { ToastService } from '../../services/toast.service';
import { IonLoaderService } from '../../services/ion-loader.service';

@Component({
  selector: 'app-invoice-image-modal',
  templateUrl: './invoice-image-modal.component.html',
  styleUrls: ['./invoice-image-modal.component.scss']
})
export class InvoiceImageModalComponent implements OnInit {
  @Input() invoiceId: number;
  @Input() invoiceData: any;

  images: InvoiceImage[] = [];
  loading = true;
  uploading = false;
  selectedFile: File | null = null;
  previewUrl: string | null = null;
  description = '';

  constructor(
    private modalController: <PERSON><PERSON><PERSON>ontroller,
    private alertController: <PERSON><PERSON><PERSON><PERSON>roll<PERSON>,
    private actionSheetController: ActionSheetController,
    private invoiceImageService: InvoiceImageService,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService
  ) { }

  ngOnInit() {
    this.loadImages();
  }

  async loadImages() {
    try {
      this.loading = true;
      const result: any = await this.invoiceImageService.getInvoiceImages(this.invoiceId);
      if (result.success) {
        this.images = result.data;
      } else {
        this.toast.toastServices(result.message || 'Failed to load images', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error loading images:', error);
      this.toast.toastServices('Failed to load images', 'danger', 'top');
    } finally {
      this.loading = false;
    }
  }

  async presentImageOptions() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Select Image Source',
      buttons: [
        {
          text: 'Take Photo',
          icon: 'camera-outline',
          handler: () => {
            this.takePicture();
          }
        },
        {
          text: 'Choose from Gallery',
          icon: 'images-outline',
          handler: () => {
            this.selectFromGallery();
          }
        },
        {
          text: 'Cancel',
          icon: 'close',
          role: 'cancel'
        }
      ]
    });
    await actionSheet.present();
  }

  takePicture() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.capture = 'environment';
    
    input.onchange = (event: any) => {
      const file = event.target.files[0];
      if (file) {
        this.handleFileSelection(file);
      }
    };
    
    input.click();
  }

  selectFromGallery() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    
    input.onchange = (event: any) => {
      const file = event.target.files[0];
      if (file) {
        this.handleFileSelection(file);
      }
    };
    
    input.click();
  }

  handleFileSelection(file: File) {
    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      this.toast.toastServices('Image size should be less than 5MB', 'danger', 'top');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      this.toast.toastServices('Please select a valid image file', 'danger', 'top');
      return;
    }

    this.selectedFile = file;
    
    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      this.previewUrl = reader.result as string;
    };
    reader.readAsDataURL(file);
  }

  clearSelection() {
    this.selectedFile = null;
    this.previewUrl = null;
    this.description = '';
  }

  async uploadImage() {
    if (!this.selectedFile) {
      this.toast.toastServices('Please select an image first', 'warning', 'top');
      return;
    }

    try {
      this.uploading = true;
      const result: any = await this.invoiceImageService.uploadInvoiceImage(
        this.invoiceId, 
        this.selectedFile, 
        this.description
      );
      
      if (result.success) {
        this.toast.toastServices('Image uploaded successfully', 'success', 'top');
        this.clearSelection();
        await this.loadImages(); // Refresh the list
      } else {
        this.toast.toastServices(result.message || 'Failed to upload image', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      this.toast.toastServices('Failed to upload image', 'danger', 'top');
    } finally {
      this.uploading = false;
    }
  }

  async deleteImage(image: InvoiceImage) {
    const alert = await this.alertController.create({
      header: 'Confirm Delete',
      message: 'Are you sure you want to delete this image?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: async () => {
            try {
              const result: any = await this.invoiceImageService.deleteInvoiceImage(image.id);
              if (result.success) {
                this.toast.toastServices('Image deleted successfully', 'success', 'top');
                await this.loadImages(); // Refresh the list
              } else {
                this.toast.toastServices(result.message || 'Failed to delete image', 'danger', 'top');
              }
            } catch (error) {
              console.error('Error deleting image:', error);
              this.toast.toastServices('Failed to delete image', 'danger', 'top');
            }
          }
        }
      ]
    });
    await alert.present();
  }

  viewFullImage(image: InvoiceImage) {
    // Open image in new tab or modal
    window.open(image.image_url, '_blank');
  }

  closeModal() {
    this.modalController.dismiss();
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  trackByImageId(index: number, image: InvoiceImage): number {
    return image.id;
  }
} 