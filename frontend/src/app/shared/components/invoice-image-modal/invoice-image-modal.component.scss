// Modal Styles
.modal-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  
  .title-icon {
    font-size: 20px;
    color: var(--ion-color-primary);
  }
}

.modal-subtitle {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-top: 4px;
}

// Upload Section
.upload-section {
  padding: 16px;
}

.upload-card {
  --background: var(--ion-card-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.upload-card ion-card-header {
  padding-bottom: 8px;
}

.upload-card ion-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  
  .upload-icon {
    font-size: 18px;
    color: var(--ion-color-primary);
  }
}

.upload-card ion-card-subtitle {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin: 4px 0 0 0;
}

// Image Preview
.image-preview {
  position: relative;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  
  .preview-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
  }
  
  .remove-preview-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    --background: rgba(0, 0, 0, 0.6);
    --color: white;
    --border-radius: 50%;
    width: 32px;
    height: 32px;
  }
}

// Upload Controls
.upload-controls {
  margin-bottom: 16px;
  
  .select-image-btn {
    --border-radius: 8px;
    --border-width: 2px;
    --border-style: dashed;
    --border-color: var(--ion-color-medium);
    height: 120px;
    margin: 0;
  }
}

// Description Section
.description-section {
  margin-bottom: 16px;
  
  ion-item {
    --background: transparent;
    --border-radius: 8px;
    --border-width: 1px;
    --border-color: var(--ion-color-light-shade);
  }
}

// Upload Actions
.upload-actions {
  ion-button {
    --border-radius: 8px;
    margin: 0;
  }
}

// Images Section
.images-section {
  padding: 0 16px 16px 16px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
  
  .section-icon {
    font-size: 18px;
    color: var(--ion-color-primary);
  }
}

// Loading State
.loading-state {
  text-align: center;
  padding: 48px 16px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    margin: 0;
    color: var(--ion-color-medium);
    font-size: 14px;
  }
}

// Images Grid
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.image-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.image-container {
  position: relative;
  cursor: pointer;
  
  .image-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
  }
  
  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
    
    .view-icon {
      font-size: 32px;
      color: white;
    }
  }
  
  &:hover .image-overlay {
    opacity: 1;
  }
}

.image-info {
  padding: 12px;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  .image-date {
    font-size: 12px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  .image-size {
    font-size: 11px;
    color: var(--ion-color-medium);
  }
}

.image-description {
  margin-bottom: 8px;
  
  p {
    margin: 0;
    font-size: 12px;
    color: var(--ion-color-medium);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.image-actions {
  display: flex;
  justify-content: flex-end;
  
  ion-button {
    --padding-start: 8px;
    --padding-end: 8px;
    height: 32px;
    width: 32px;
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 48px 16px;
  
  .empty-icon {
    font-size: 64px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

// Footer
.footer-info {
  padding: 12px 16px;
  text-align: center;
  
  small {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 12px;
  }
}

// Action Sheet Styles
.image-options-action-sheet {
  --background: white;
  --border-radius: 16px 16px 0 0;
}

// Responsive Design
@media (max-width: 768px) {
  .images-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .image-item {
    margin-bottom: 0;
  }
  
  .upload-section {
    padding: 12px;
  }
  
  .images-section {
    padding: 0 12px 12px 12px;
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: 16px;
  }
  
  .modal-subtitle {
    font-size: 11px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .image-container .image-thumbnail {
    height: 160px;
  }
}

// Modal specific styles
.invoice-image-modal {
  --background: var(--ion-background-color);
  --border-radius: 16px;
  --width: 95%;
  --max-width: 600px;
  --height: 90%;
  --max-height: 800px;
} 