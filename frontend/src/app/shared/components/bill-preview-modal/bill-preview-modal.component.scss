.preview-content {
  --background: #f5f5f5;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: var(--ion-color-medium);
}

/* Error Container */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.error-card {
  max-width: 400px;
  width: 100%;
}

.error-content {
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 64px;
  color: var(--ion-color-danger);
  margin-bottom: 20px;
}

/* Invoice Container */
.invoice-container {
  padding: 16px;
}

/* Barcode Info Card */
.barcode-info-card {
  margin-bottom: 16px;
  background: var(--ion-color-success-tint);
  border-left: 4px solid var(--ion-color-success);
}

.barcode-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.success-icon {
  font-size: 24px;
  color: var(--ion-color-success);
  flex-shrink: 0;
}

.barcode-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-success-shade);
}

.barcode-text p {
  margin: 0;
  font-size: 14px;
  color: var(--ion-color-success-shade);
  font-family: monospace;
}

/* Invoice Card */
.invoice-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.invoice-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 12px;
}

.invoice-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* Invoice Icon */
.invoice-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: var(--ion-color-primary-tint);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.receipt-icon {
  font-size: 24px;
  color: var(--ion-color-primary);
}

/* Invoice Details */
.invoice-details {
  flex: 1;
  min-width: 0;
}

.customer-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.invoice-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.invoice-id {
  background: var(--ion-color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.invoice-date {
  background: var(--ion-color-light);
  color: var(--ion-color-dark);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* Contact Details */
.contact-details {
  margin-bottom: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 14px;
  color: var(--ion-color-medium-shade);
}

.contact-icon {
  font-size: 16px;
  color: var(--ion-color-medium);
}

/* Amount Details */
.amount-details {
  background: var(--ion-color-light);
  padding: 12px;
  border-radius: 8px;
  margin-top: 12px;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.amount-row:last-child {
  margin-bottom: 0;
}

.amount-label {
  font-size: 14px;
  color: var(--ion-color-medium-shade);
  font-weight: 500;
}

.amount-value {
  font-size: 14px;
  font-weight: 600;
}

.bill-amount {
  color: var(--ion-color-dark);
}

.received-amount {
  color: var(--ion-color-success);
}

.balance-positive {
  color: var(--ion-color-danger);
}

.balance-zero {
  color: var(--ion-color-success);
}

.payment-status-badge {
  font-size: 12px;
  padding: 4px 8px;
}

/* Action Buttons Footer */
.invoice-actions-footer {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 16px;
  background: var(--ion-card-background);
  border-top: 1px solid var(--ion-border-color);
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  --padding-start: 8px;
  --padding-end: 8px;
  min-width: 44px;
}

/* Action Button Colors */
.payment-button {
  --color: var(--ion-color-primary);
  --background: var(--ion-color-primary-tint);
}

.print-button {
  --color: var(--ion-color-primary);
  --background: var(--ion-color-primary-tint);
}

.receipt-button {
  --color: var(--ion-color-secondary);
  --background: var(--ion-color-secondary-tint);
}

.edit-button {
  --color: var(--ion-color-warning);
  --background: var(--ion-color-warning-tint);
}

.delivery-button {
  --color: var(--ion-color-success);
  --background: var(--ion-color-success-tint);
}

.delete-button {
  --color: var(--ion-color-danger);
  --background: var(--ion-color-danger-tint);
}

/* Quick Actions */
.quick-actions {
  margin-top: 16px;
}

.view-full-button {
  --background: var(--ion-color-primary);
  --color: white;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .invoice-container {
    padding: 12px;
  }
  
  .invoice-header {
    gap: 8px;
  }
  
  .customer-name {
    font-size: 18px;
  }
  
  .invoice-actions-footer {
    padding: 8px 12px;
    gap: 6px;
  }
  
  .action-button {
    min-width: 40px;
  }
}
