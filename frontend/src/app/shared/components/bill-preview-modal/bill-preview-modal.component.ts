import { Component, OnInit, Input } from '@angular/core';
import { <PERSON>dal<PERSON>ontroller, AlertController } from '@ionic/angular';
import { SalesInvoiceService } from '../../services/sales-invoice.service';
import { PrintServiceService } from '../../services/print-service.service';
import { PaymentReceiptService } from '../../services/payment-receipt.service';
import { ToastService } from '../../services/toast.service';
import { RouteService } from '../../services/route.service';

@Component({
  selector: 'app-bill-preview-modal',
  templateUrl: './bill-preview-modal.component.html',
  styleUrls: ['./bill-preview-modal.component.scss']
})
export class BillPreviewModalComponent implements OnInit {
  @Input() invoiceId!: number;
  @Input() barcodeData!: string;

  invoice: any = null;
  loading = true;
  error: string | null = null;

  // Payment receipt modal properties
  isPaymentModalOpen: boolean = false;

  constructor(
    private modalController: ModalController,
    private salesInvoiceService: SalesInvoiceService,
    private printerService: PrintServiceService,
    private paymentReceiptService: PaymentReceiptService,
    private toast: ToastService,
    private routeService: RouteService,
    private alertController: AlertController
  ) {}

  async ngOnInit() {
    await this.loadInvoiceDetails();
  }

  async loadInvoiceDetails() {
    try {
      this.loading = true;
      this.error = null;

      // Fetch invoice details by ID
      // For now, we'll simulate getting invoice data
      // In a real implementation, you'd have a getInvoiceById method
      const response = { success: true, data: this.getMockInvoiceData() };
      
      if (response.success) {
        this.invoice = response.data;
      } else {
        this.error = 'Invoice not found';
      }
    } catch (error) {
      console.error('Error loading invoice details:', error);
      this.error = 'Failed to load invoice details';
    } finally {
      this.loading = false;
    }
  }

  // Action Methods - Same as sales-bill page
  printInvoice() {
    if (this.invoice) {
      this.printerService.print(this.invoice, false, this.invoice.name + " - " + this.invoice.date);
    }
  }

  printCollectionReceipt() {
    if (this.invoice) {
      this.printerService.printCollectionReceipt(this.invoice);
    }
  }

  openPaymentModal() {
    if (this.invoice && this.invoice.current_balance > 0) {
      this.isPaymentModalOpen = true;
    }
  }

  closePaymentModal() {
    this.isPaymentModalOpen = false;
  }

  onPaymentCreated(paymentData: any) {
    // Refresh invoice data after payment
    this.loadInvoiceDetails();
    this.toast.toastServices('Payment receipt created successfully', 'success', 'top');
    this.closePaymentModal();
  }

  editInvoice() {
    if (this.invoice) {
      this.closeModal();
      this.routeService.routerFunction('edit-invoice/' + this.invoice.id);
    }
  }

  async deliveryInvoice() {
    if (this.invoice) {
      // Implement delivery logic similar to sales-bill page
      this.toast.toastServices('Delivery functionality not implemented yet', 'warning', 'top');
    }
  }

  async deleteInvoice() {
    if (this.invoice) {
      const alert = await this.alertController.create({
        header: 'Delete Invoice',
        message: 'Are you sure you want to delete this invoice?',
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'Delete',
            role: 'destructive',
            handler: () => {
              this.performDelete();
            }
          }
        ]
      });

      await alert.present();
    }
  }

  private async performDelete() {
    try {
      // For now, just simulate deletion since we don't have the actual API method
      this.toast.toastServices('Invoice deletion functionality not implemented yet', 'warning', 'top');
      this.closeModal();
    } catch (error) {
      console.error('Error deleting invoice:', error);
      this.toast.toastServices('Failed to delete invoice', 'danger', 'top');
    }
  }

  getPaymentStatus(invoice: any): string {
    if (invoice.current_balance <= 0) {
      return 'paid';
    } else if (invoice.received_amount > 0) {
      return 'partial';
    } else {
      return 'pending';
    }
  }

  getPaymentStatusText(status: string): string {
    switch (status) {
      case 'paid': return 'Paid';
      case 'partial': return 'Partial';
      case 'pending': return 'Pending';
      default: return 'Unknown';
    }
  }

  getPaymentStatusColor(status: string): string {
    switch (status) {
      case 'paid': return 'success';
      case 'partial': return 'warning';
      case 'pending': return 'danger';
      default: return 'medium';
    }
  }

  async closeModal() {
    await this.modalController.dismiss();
  }

  // Navigate to full invoice view
  viewFullInvoice() {
    this.closeModal();
    this.routeService.routerFunction('view-invoice/' + this.invoice.id);
  }

  // Mock data for testing
  private getMockInvoiceData() {
    return {
      id: this.invoiceId,
      name: 'John Doe',
      phone_no: '+91 9876543210',
      place: 'Mumbai, Maharashtra',
      date: new Date().toISOString().split('T')[0],
      bill_amount: 1250.00,
      received_amount: 500.00,
      current_balance: 750.00,
      items: [
        { name: 'Product A', quantity: 2, rate: 300, amount: 600 },
        { name: 'Product B', quantity: 1, rate: 650, amount: 650 }
      ]
    };
  }
}
