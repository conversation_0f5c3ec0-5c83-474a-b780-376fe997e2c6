/* Side Menu Full Height Styling */
:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

ion-header {
  flex-shrink: 0;
}

ion-content {
  flex: 1;
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
  --overflow: auto;
  -webkit-overflow-scrolling: touch;
}

ion-footer {
  flex-shrink: 0;
}

/* Side Menu Header Styling */
.menu-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.app-icon {
  font-size: 24px;
  margin-right: 12px;
  color: var(--ion-text-color, white);
}

.app-info {
  display: flex;
  flex-direction: column;
}

.app-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-text-color, white);
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* Menu List Styling */
.menu-list {
  padding: 0 0 20px 0;
  min-height: fit-content;
}

/* Scrollbar Styling */
ion-content {
  --scrollbar-width: 4px;
}

ion-content::-webkit-scrollbar {
  width: 4px;
}

ion-content::-webkit-scrollbar-track {
  background: transparent;
}

ion-content::-webkit-scrollbar-thumb {
  background: rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.3);
  border-radius: 2px;
}

ion-content::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.5);
}

ion-list-header {
  background: var(--ion-color-light, #f4f5f8);
  border-bottom: 1px solid var(--ion-color-light-shade, #e8e9ed);
  margin-top: 16px;
  padding: 12px 16px;
}

ion-list-header:first-child {
  margin-top: 0;
}

ion-list-header ion-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--ion-color-medium, #92949c);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Menu Item Styling */
.menu-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 8px;
  --min-height: 48px;
  transition: all 300ms ease-in-out;
  border-left: 3px solid transparent;
}

.menu-item:hover {
  --background: var(--ion-color-light-shade, #e8e9ed);
  border-left-color: var(--ion-color-primary, #3880ff);
}

.menu-item:active {
  --background: var(--ion-color-light-tint, #f5f6f9);
}

.menu-item ion-icon {
  color: var(--ion-color-medium, #92949c);
  font-size: 20px;
  margin-right: 16px;
  transition: color 300ms ease-in-out;
}

.menu-item:hover ion-icon {
  color: var(--ion-color-primary, #3880ff);
}

.menu-item ion-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--ion-color-dark, #222428);
}

/* Logout Item Special Styling */
.logout-item {
  margin-top: 16px;
  border-top: 1px solid var(--ion-color-light-shade, #e8e9ed);
}

.logout-item:hover {
  --background: rgba(var(--ion-color-danger-rgb, 235, 68, 90), 0.1);
  border-left-color: var(--ion-color-danger, #eb445a);
}

.logout-item ion-icon {
  color: var(--ion-color-danger, #eb445a);
}

.logout-item:hover ion-icon {
  color: var(--ion-color-danger-shade, #cf3c4f);
}

.logout-item ion-label {
  color: var(--ion-color-danger, #eb445a);
}

/* Footer Styling */
ion-footer ion-toolbar {
  --background: var(--ion-color-light, #f4f5f8);
  --border-color: var(--ion-color-light-shade, #e8e9ed);
  --border-width: 1px 0 0 0;
}

.footer-info {
  text-align: center;
  font-size: 12px;
  color: var(--ion-color-medium, #92949c);
  font-weight: 400;
}

/* Responsive Design */
@media (max-width: 320px) {
  .app-name {
    font-size: 14px;
  }

  .user-role {
    font-size: 11px;
  }

  .menu-item {
    --min-height: 44px;
  }

  .menu-item ion-icon {
    font-size: 18px;
    margin-right: 12px;
  }

  .menu-item ion-label {
    font-size: 13px;
  }
}

@media (min-width: 768px) {
  .menu-header {
    padding: 12px 0;
  }

  .app-icon {
    font-size: 28px;
    margin-right: 16px;
  }

  .app-name {
    font-size: 18px;
  }

  .user-role {
    font-size: 13px;
  }

  .menu-item {
    --min-height: 52px;
    --padding-start: 20px;
    --padding-end: 20px;
  }

  .menu-item ion-icon {
    font-size: 22px;
    margin-right: 20px;
  }

  .menu-item ion-label {
    font-size: 15px;
  }
}

/* Desktop specific improvements */
@media (min-width: 1024px) {
  .menu-header {
    padding: 16px 0;
  }

  .app-icon {
    font-size: 32px;
    margin-right: 20px;
  }

  .app-name {
    font-size: 20px;
  }

  .user-role {
    font-size: 14px;
  }

  .menu-item {
    --min-height: 56px;
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .menu-item ion-icon {
    font-size: 24px;
    margin-right: 24px;
  }

  .menu-item ion-label {
    font-size: 16px;
  }
}

/* Accessibility Improvements */
.menu-item:focus {
  outline: 2px solid var(--ion-color-primary, #3880ff);
  outline-offset: -2px;
}

/* Animation for menu items */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.menu-item {
  animation: slideInLeft 300ms ease-out;
}

/* Stagger animation for menu items */
.menu-item:nth-child(1) { animation-delay: 0ms; }
.menu-item:nth-child(2) { animation-delay: 50ms; }
.menu-item:nth-child(3) { animation-delay: 100ms; }
.menu-item:nth-child(4) { animation-delay: 150ms; }
.menu-item:nth-child(5) { animation-delay: 200ms; }
.menu-item:nth-child(6) { animation-delay: 250ms; }
.menu-item:nth-child(7) { animation-delay: 300ms; }
.menu-item:nth-child(8) { animation-delay: 350ms; }
