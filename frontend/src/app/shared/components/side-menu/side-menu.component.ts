import { Component, OnInit } from '@angular/core';
import { MenuController } from '@ionic/angular';
import { RouteService } from '../../services/route.service';
import { ConstantsService } from '../../services/constants.service';
import { AuthenticationService } from '../../services/authentication.service';
import { AlertService } from '../../services/alert.service';

@Component({
  selector: 'app-side-menu',
  templateUrl: './side-menu.component.html',
  styleUrls: ['./side-menu.component.scss'],
})
export class SideMenuComponent implements OnInit {
  role: string;
  component: any = [];

  constructor(
    private menuController: MenuController,
    public routeService: RouteService,
    public constant: ConstantsService,
    public authService: AuthenticationService,
    private alertService: AlertService
  ) {
    this.role = localStorage.getItem('role');

    // Get component metadata if available - exact same logic as menu page
    try {
      const metadata = localStorage.getItem('metadata');
      if (metadata) {
        const parsedMetadata = JSON.parse(metadata);
        this.component = parsedMetadata.component || [];
        // Sort by sort_order - exact same logic as menu page
        this.component.sort((b, a) => b.sort_order - a.sort_order);
      }
    } catch (error) {
      console.error('Error parsing metadata:', error);
      this.component = [];
    }
  }

  ngOnInit() {}

  async navigateAndCloseMenu(url: string) {
    await this.menuController.close('main-menu');
    const mappedUrl = this.mapUrlToTabRoute(url);
    this.routeService.routerFunction(mappedUrl);
  }

  // Map old individual routes to new tab routes
  private mapUrlToTabRoute(url: string): string {
    const urlMapping: { [key: string]: string } = {
      'tabs/home': 'tabs/home',
      'product': 'tabs/product',
      'sales-bill': 'tabs/sales-bill',
      'purchase-order': 'purchase-order',
      'report': 'report',
      'settings': 'tabs/settings'
    };

    return urlMapping[url] || url;
  }

  // Logout functionality - same as header component
  logout() {
    this.alertService
      .alertConfirm(
        "Alert",
        "Do you want to logout !!!",
        "Yes",
        "No"
      )
      .then((res) => {
        if (res) {
          localStorage.clear();
          this.routeService.routerFunction("login");
        }
      });
  }
}
