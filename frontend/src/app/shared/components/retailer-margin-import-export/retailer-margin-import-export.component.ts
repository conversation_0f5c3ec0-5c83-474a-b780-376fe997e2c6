import { Component, Input, Output, EventEmitter } from '@angular/core';
import { RetailerClassService } from '../../services/retailer-class.service';
import { ToastService } from '../../services/toast.service';

@Component({
  selector: 'app-retailer-margin-import-export',
  templateUrl: './retailer-margin-import-export.component.html',
  styleUrls: ['./retailer-margin-import-export.component.scss']
})
export class RetailerMarginImportExportComponent {
  @Input() buyerClassId: number;
  @Output() importComplete = new EventEmitter<void>();

  selectedFile: File | null = null;
  isUploading: boolean = false;
  isDownloading: boolean = false;

  constructor(
    private retailerClassService: RetailerClassService,
    private toast: ToastService
  ) {}

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file && file.type === 'text/csv') {
      this.selectedFile = file;
    } else {
      this.toast.toastServices('Please select a valid CSV file.', "danger", "bottom");
      this.selectedFile = null;
    }
  }

  async uploadCsv() {
    if (!this.selectedFile || !this.buyerClassId) {
      this.toast.toastServices('Please select a CSV file and ensure buyer class is selected.', "danger", "bottom");
      return;
    }

    this.isUploading = true;
    this.toast.toastServices('Uploading CSV file...', "primary", "bottom");

    this.retailerClassService.importBuyerClassMargin(this.buyerClassId, this.selectedFile)
      .subscribe({
        next: (res: any) => {
          this.toast.toastServices(`CSV imported successfully! ${res.message || ''}`, "success", "bottom");
          this.selectedFile = null;
          this.resetFileInput();
          this.importComplete.emit();
          this.isUploading = false;
        },
        error: (err) => {
          const errorMessage = err.error?.message || err.message || 'Import failed';
          this.toast.toastServices(`Import Error: ${errorMessage}`, "danger", "bottom");
          this.isUploading = false;
        }
      });
  }

  async downloadCsv() {
    if (!this.buyerClassId) {
      this.toast.toastServices('Buyer class ID is required for export.', "danger", "bottom");
      return;
    }

    this.isDownloading = true;
    this.toast.toastServices('Preparing CSV download...', "primary", "bottom");

    this.retailerClassService.exportBuyerClassMargin(this.buyerClassId)
      .subscribe({
        next: (blob: Blob) => {
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `buyer_class_margins_${this.buyerClassId}.csv`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
          
          this.toast.toastServices('CSV downloaded successfully!', "success", "bottom");
          this.isDownloading = false;
        },
        error: (err) => {
          const errorMessage = err.error?.message || err.message || 'Download failed';
          this.toast.toastServices(`Download Error: ${errorMessage}`, "danger", "bottom");
          this.isDownloading = false;
        }
      });
  }

  private resetFileInput() {
    const fileInput = document.getElementById('csvFileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }
}
