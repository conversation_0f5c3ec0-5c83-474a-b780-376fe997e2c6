.import-export-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ion-color-primary);
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.title-icon {
  font-size: 24px;
}

.card-subtitle {
  color: var(--ion-color-medium);
  font-size: 14px;
  margin-top: 4px;
}

.card-content {
  padding: 24px;
}

.action-grid {
  margin-bottom: 24px;
}

.action-row {
  gap: 24px;
}

.upload-section, .download-section {
  padding: 20px;
  background: var(--ion-color-light-tint);
  border-radius: 8px;
  border: 2px dashed var(--ion-color-light-shade);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--ion-color-primary);
    background: var(--ion-color-primary-tint);
  }
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
}

.section-icon {
  font-size: 20px;
  color: var(--ion-color-primary);
}

.section-description {
  font-size: 13px;
  color: var(--ion-color-medium);
  margin-bottom: 16px;
  line-height: 1.4;
}

.file-input-wrapper {
  margin-bottom: 16px;
}

.file-input {
  display: none;
}

.file-select-btn {
  --border-radius: 8px;
  --border-width: 2px;
  --border-style: dashed;
  --border-color: var(--ion-color-medium);
  --background: white;
  --color: var(--ion-color-dark);
  margin-bottom: 12px;
  height: 48px;
  font-size: 14px;

  &:hover {
    --border-color: var(--ion-color-primary);
    --color: var(--ion-color-primary);
  }
}

.action-btn {
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  height: 44px;
  font-weight: 600;
  font-size: 14px;
}

.upload-btn {
  --background: var(--ion-color-success);
}

.download-btn {
  --background: var(--ion-color-primary);
}

.download-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--ion-color-medium);
}

.info-icon {
  font-size: 16px;
  color: var(--ion-color-primary);
}

.instructions-section {
  background: var(--ion-color-light);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.instructions-header {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 12px;

  ion-label h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
}

.help-icon {
  color: var(--ion-color-primary);
  font-size: 20px;
}

.instructions-content {
  padding-left: 28px;
}

.instruction-item {
  margin-bottom: 16px;
  font-size: 13px;
  line-height: 1.4;

  strong {
    color: var(--ion-color-dark);
    display: block;
    margin-bottom: 4px;
  }

  ul {
    margin: 4px 0 0 16px;
    padding: 0;
    color: var(--ion-color-medium);

    li {
      margin-bottom: 2px;
    }
  }

  code {
    background: var(--ion-color-light-shade);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--ion-color-primary);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .action-row {
    flex-direction: column;
    gap: 16px;
  }

  .upload-section, .download-section {
    padding: 16px;
  }

  .card-content {
    padding: 16px;
  }

  .instructions-content {
    padding-left: 0;
  }
}

// Loading states
.action-btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

ion-spinner {
  width: 16px;
  height: 16px;
}
