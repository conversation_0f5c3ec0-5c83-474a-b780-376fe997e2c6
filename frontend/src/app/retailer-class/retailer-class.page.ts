import { Component, OnInit } from '@angular/core';
import { Platform, NavController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { RouteService } from '../shared/services/route.service';

import { ToastService } from '../shared/services/toast.service';
import { RetailerClassService } from '../shared/services/retailer-class.service';

@Component({
  selector: 'app-retailer-class',
  templateUrl: './retailer-class.page.html',
  styleUrls: ['./retailer-class.page.scss'],
})
export class RetailerClassPage implements OnInit {
  data: any;
  isModalOpen = false;
  isEditModalOpen = false;
  selectedData: any;

  constructor(
    private api: RetailerClassService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public nav: NavController,
    public routerService: RouteService
  ) { }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.getData();
  }

  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getBuyerClass()
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.data = res.data;
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  async delete(id) {
    this.alertService.alertConfirm('Alert', 'Are you sure you want to delete the buyer class !!!', 'yes', 'no').then((res) => {
      if (res) {
        this.deleteBuyerClass(id)
      }
    });
  }

  async deleteBuyerClass(id) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .deleteBuyerClass(id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.getData();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async editBuyerClass(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .putBuyerClass(data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.setEditOpen(false, null)
            this.getData();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async addBuyerClass(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .postBuyerClass(data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.setOpen(false)
            this.getData();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
  }
  setEditOpen(isOpen: boolean, item) {
    this.isEditModalOpen = isOpen;
    this.selectedData = item;
  }
  viewClassMargins(id){
    this.nav.navigateForward(`/retailer-class-mgmt?id=${id}`);
  }
}