import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RetailerClassPageRoutingModule } from './retailer-class-routing.module';

import { RetailerClassPage } from './retailer-class.page';
import { SharedModule } from '../shared/modules/shared/shared.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RetailerClassPageRoutingModule,
    SharedModule
  ],
  declarations: [RetailerClassPage]
})
export class RetailerClassPageModule {}
