// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':bcyesil-capacitor-plugin-printer')
    implementation project(':capacitor-app')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-status-bar')
    implementation "androidx.webkit:webkit:1.4.0"
    implementation "androidx.legacy:legacy-support-v4:1.0.0"
}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
