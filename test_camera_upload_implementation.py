#!/usr/bin/env python3
"""
Test script to verify the camera upload implementation.
Tests that:
1. Camera upload component is properly integrated in all form pages
2. Configuration options work correctly
3. File validation and handling work as expected
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "https://billing-api.kingwizard.in"
API_URL = f"{BASE_URL}/api"

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "username": "admin",  # Replace with actual test credentials
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{API_URL}/login/", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                return data.get('token')
        return None
    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def test_expense_image_upload(token):
    """Test expense page image upload functionality"""
    print("\n=== Testing Expense Image Upload ===")
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Test expense creation with image
    expense_data = {
        "amount": 100.00,
        "category": 1,  # Assuming category ID 1 exists
        "subcategory": 1,  # Assuming subcategory ID 1 exists
        "mode_of_payment": "cash",
        "notes": "Test expense with camera upload"
    }
    
    try:
        response = requests.post(f"{API_URL}/expense/", json=expense_data, headers=headers)
        print(f"Expense creation response: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Message: {data.get('message')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error testing expense image upload: {e}")

def test_ledger_image_upload(token):
    """Test ledger page image upload functionality"""
    print("\n=== Testing Ledger Image Upload ===")
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Test ledger creation with image
    ledger_data = {
        "amount": 500.00,
        "discount_amount": 0.00,
        "remarks": "Test ledger entry with camera upload",
        "entry_type": "credit",
        "party__id": 1,  # Assuming buyer/supplier ID 1 exists
        "mode_of_payment": "cash"
    }
    
    try:
        response = requests.post(f"{API_URL}/ledger/?buyer=true", json=ledger_data, headers=headers)
        print(f"Ledger creation response: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Message: {data.get('message')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error testing ledger image upload: {e}")

def test_checklist_image_upload(token):
    """Test checklist item image upload functionality"""
    print("\n=== Testing Checklist Image Upload ===")
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Test checklist item creation with image
    checklist_item_data = {
        "checklist": 1,  # Assuming checklist ID 1 exists
        "title": "Test item with camera upload",
        "description": "Test checklist item",
        "is_mandatory": False,
        "is_completed": False,
        "notes": "Test notes"
    }
    
    try:
        response = requests.post(f"{API_URL}/checklist-item/", json=checklist_item_data, headers=headers)
        print(f"Checklist item creation response: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Message: {data.get('message')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error testing checklist image upload: {e}")

def test_file_validation():
    """Test file validation logic"""
    print("\n=== Testing File Validation ===")
    
    # Test file size validation
    test_cases = [
        {
            "name": "Valid image file (1MB)",
            "size": 1 * 1024 * 1024,
            "type": "image/jpeg",
            "expected": True
        },
        {
            "name": "Large image file (10MB)",
            "size": 10 * 1024 * 1024,
            "type": "image/png",
            "expected": False
        },
        {
            "name": "Invalid file type",
            "size": 1 * 1024 * 1024,
            "type": "application/pdf",
            "expected": False
        },
        {
            "name": "Small image file (100KB)",
            "size": 100 * 1024,
            "type": "image/jpeg",
            "expected": True
        }
    ]
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        print(f"Size: {test_case['size'] / (1024 * 1024):.2f}MB")
        print(f"Type: {test_case['type']}")
        print(f"Expected: {test_case['expected']}")
        
        # Simulate validation logic
        is_valid_size = test_case['size'] <= 5 * 1024 * 1024  # 5MB limit
        is_valid_type = test_case['type'].startswith('image/')
        is_valid = is_valid_size and is_valid_type
        
        print(f"Actual: {is_valid}")
        print(f"Result: {'PASS' if is_valid == test_case['expected'] else 'FAIL'}")

def test_camera_upload_configuration():
    """Test camera upload configuration options"""
    print("\n=== Testing Camera Upload Configuration ===")
    
    # Test default configuration
    default_config = {
        "maxSize": 5 * 1024 * 1024,
        "allowedTypes": ["image/*"],
        "showPreview": True,
        "showDescription": False,
        "descriptionLabel": "Description",
        "descriptionPlaceholder": "Add description...",
        "buttonText": "Add Image",
        "buttonIcon": "camera-outline"
    }
    
    print("Default Configuration:")
    for key, value in default_config.items():
        print(f"  {key}: {value}")
    
    # Test custom configuration
    custom_config = {
        "maxSize": 10 * 1024 * 1024,
        "allowedTypes": ["image/jpeg", "image/png"],
        "showPreview": True,
        "showDescription": True,
        "descriptionLabel": "Photo Notes",
        "descriptionPlaceholder": "Add notes about this photo...",
        "buttonText": "Take Photo",
        "buttonIcon": "camera"
    }
    
    print("\nCustom Configuration:")
    for key, value in custom_config.items():
        print(f"  {key}: {value}")

def main():
    """Main test function"""
    print("Camera Upload Implementation Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now()}")
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token. Exiting.")
        sys.exit(1)
    
    print(f"Authentication successful. Token: {token[:20]}...")
    
    # Run tests
    test_camera_upload_configuration()
    test_file_validation()
    test_expense_image_upload(token)
    test_ledger_image_upload(token)
    test_checklist_image_upload(token)
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print(f"Test finished at: {datetime.now()}")

if __name__ == "__main__":
    main() 