# King Bill - Complete Business Management System

A comprehensive business management application built with Django REST API backend and Ionic Angular frontend, featuring advanced inventory management, sales tracking, purchase orders, and automated forecasting with Celery task processing.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Redis 6+
- Ionic CLI

### Development Setup

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver
```

#### Frontend Setup
```bash
cd frontend
npm install
ionic serve
```

#### Celery Setup (Development)
```bash
# Terminal 1: Start Redis
redis-server

# Terminal 2: Start Celery Worker
cd backend
celery -A vegetable_bill_app worker --loglevel=info

# Terminal 3: Start Celery Beat
celery -A vegetable_bill_app beat --loglevel=info
```

## 📋 Features

### Core Business Features
- **Sales Management**: Modern POS-style invoice creation with QR code scanning
- **Purchase Orders**: Complete supplier management and automated ordering
- **Inventory Tracking**: Real-time stock monitoring with automated alerts
- **Customer Management**: Comprehensive buyer/customer relationship management
- **Analytics & Reporting**: Sales trends, forecasting, and business intelligence

### Advanced Features
- **AI-Powered Forecasting**: Automated sales predictions using machine learning
- **Multi-format Printing**: A4, A5, and thermal printer support
- **Route Management**: Delivery route optimization and tracking
- **Photo Management**: Shop image capture and automated cleanup
- **Brand-based Filtering**: Advanced product categorization and filtering

### Technical Features
- **Celery Task Queue**: Automated background processing
- **Redis Caching**: High-performance data caching
- **RESTful API**: Comprehensive API with OpenAPI documentation
- **Mobile-First Design**: Responsive Ionic Angular frontend
- **Real-time Updates**: Live data synchronization

## 🏗️ Architecture

```
King Bill Application
├── Backend (Django REST API)
│   ├── Core Business Logic
│   ├── Celery Task Processing
│   ├── Database Models
│   └── API Endpoints
├── Frontend (Ionic Angular)
│   ├── Mobile-First UI
│   ├── Real-time Updates
│   ├── Print Services
│   └── Analytics Dashboard
└── Infrastructure
    ├── PostgreSQL Database
    ├── Redis Cache/Queue
    ├── Celery Workers
    └── Background Tasks
```

## 🔧 Production Deployment

### AWS Lightsail + Ubuntu + Bitnami

For detailed production deployment instructions including Supervisor, Gunicorn, and Celery setup, see:
- **[Backend Setup Guide](backend/README.md)** - Comprehensive production deployment with Supervisor + Gunicorn + Celery
- **[Frontend Build Guide](frontend/README.MD)** - Mobile app building instructions

### Key Production Components

#### Application Server Stack
- **Gunicorn**: WSGI HTTP Server for Django application
- **Supervisor**: Process management for all services
- **Nginx**: Reverse proxy and static file serving
- **Redis**: Message broker and caching

#### Celery Task Processing
- **Daily Inventory Check**: Automated stock level monitoring
- **Sales Forecasting**: AI-powered demand prediction
- **Purchase Order Generation**: Automated supplier ordering
- **Photo Cleanup**: Automated old image removal

#### Scheduled Tasks
- Inventory checks at 8 AM daily
- Sales forecast generation daily
- AI processing every 2 hours
- Photo cleanup at 2 AM daily

#### Service Management
- All services managed by Supervisor for reliability
- Automatic restart on failure
- Centralized logging and monitoring
- Easy scaling and configuration management

## 📊 Celery Tasks Overview

### Background Processing Tasks

| Task | Queue | Schedule | Description |
|------|-------|----------|-------------|
| `daily_inventory_check` | inventory | Daily 8 AM | Check stock levels, generate purchase orders |
| `generate_sales_forecast` | forecast | Daily | Generate sales predictions from historical data |
| `process_ai_forecasts` | ai | Every 2 hours | Process forecasts through AI API |
| `cleanup_old_shop_photos` | cleanup | Daily 2 AM | Remove photos older than 30 days |
| `generate_purchase_order` | purchase | On-demand | Generate specific purchase orders |

### Task Monitoring
```bash
# Check active tasks
celery -A vegetable_bill_app inspect active

# Monitor task events
celery -A vegetable_bill_app events

# Check worker status
celery -A vegetable_bill_app inspect stats
```

## 🗂️ Project Structure

```
king-bill/
├── backend/                 # Django REST API
│   ├── master/             # Core business models and logic
│   ├── vegetable_bill_app/ # Django project settings
│   ├── requirements.txt    # Python dependencies
│   └── README.md          # Detailed backend setup guide
├── frontend/               # Ionic Angular application
│   ├── src/               # Application source code
│   ├── android/           # Android build files
│   ├── ios/               # iOS build files
│   └── README.MD          # Frontend build instructions
├── docs/                   # Comprehensive documentation
│   ├── features/          # Feature documentation
│   ├── implementation/    # Technical implementation guides
│   ├── fixes/             # Bug fixes and resolutions
│   └── tasks/             # Task lists and testing docs
└── tests/                  # Test scripts and sample data
```

## 🔍 Key Technologies

### Backend Stack
- **Django 3.1+**: Web framework
- **Django REST Framework**: API development
- **Celery 5.3+**: Distributed task queue
- **Redis 5.0+**: Message broker and caching
- **PostgreSQL**: Primary database
- **django-celery-beat**: Periodic task scheduling

### Frontend Stack
- **Ionic 6+**: Mobile app framework
- **Angular 15+**: Frontend framework
- **TypeScript**: Programming language
- **Capacitor**: Native mobile features

### Infrastructure
- **AWS Lightsail**: Cloud hosting platform
- **Ubuntu**: Operating system
- **Bitnami**: Application stack foundation
- **Nginx**: Reverse proxy and web server
- **Supervisor**: Process management and monitoring
- **Gunicorn**: WSGI HTTP server for Django

## 📖 Documentation

Comprehensive documentation is available in the `docs/` directory:

- **[Features Documentation](docs/features/)** - Detailed feature descriptions
- **[Implementation Guides](docs/implementation/)** - Technical implementation details
- **[Bug Fixes](docs/fixes/)** - Issue resolutions and fixes
- **[Task Management](docs/tasks/)** - Development tasks and testing

## 🧪 Testing

### Backend Testing
```bash
cd backend
python manage.py test

# Test Celery tasks
python ../tests/simple_celery_test.py
```

### Frontend Testing
```bash
cd frontend
npm test
ionic build
```

## 🔐 Environment Configuration

### Required Environment Variables
```env
# Django Settings
SECRET_KEY=your_secret_key
DEBUG=False
ALLOWED_HOSTS=your-domain.com

# Database
DB_NAME=king_bill_db
DB_HOST=localhost
DB_USER=king_bill_user
DB_PASSWORD=your_password

# Celery
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# AI Integration (Optional)
DEEPSEEK_API_KEY=your_api_key
AI_FORECAST_ENABLED=True
```

## 🚨 Monitoring & Troubleshooting

### Service Status
```bash
# Check all supervised services
sudo supervisorctl status

# Check system services
sudo systemctl status supervisor nginx redis-server postgresql

# View application logs
sudo supervisorctl tail -f king_bill_django
sudo supervisorctl tail -f king_bill_celery_worker

# View system logs
sudo journalctl -u supervisor -f
tail -f /var/log/gunicorn/error.log
```

### Common Issues
- **Redis Connection**: Ensure Redis is running and accessible
- **Database Connection**: Verify PostgreSQL credentials and connectivity
- **Task Queue**: Check Celery worker status and queue configuration
- **Memory Usage**: Monitor Redis memory usage and configure limits

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For technical support or questions:
- Check the documentation in `docs/`
- Review the detailed setup guides in `backend/README.md`
- Contact the development team

---

**Last Updated**: June 17, 2025  
**Version**: 2.0.0  
**Environment**: Production Ready
