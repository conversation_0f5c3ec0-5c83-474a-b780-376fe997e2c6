#!/usr/bin/env python
"""
Test script for the new sales reports implementation
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from django.test import TestCase
from master.models import User
from master.models import Brand, Buyer, Product, SalesInvoice, SalesInvoiceItem
from master.views import ReportView
from rest_framework.test import APIRequestFactory, force_authenticate
from rest_framework.authtoken.models import Token
from django.utils import timezone

def test_sales_reports():
    """Test the new sales reports functionality"""
    print("Testing Sales Reports Implementation...")
    
    # Create test data
    print("1. Creating test data...")
    
    # Clean up any existing test user before creating
    User.objects.filter(username='testuser').delete()
    # Create a test user
    user = User.objects.create_user(
        username='testuser',
        password='testpass123',
        email='<EMAIL>'
    )
    
    # Create a brand
    brand = Brand.objects.create(
        name='Test Brand',
        user=user
    )
    
    # Create a buyer
    buyer = Buyer.objects.create(
        name='Test Buyer',
        phone_no='1234567890',
        place='Test City',
        user=user
    )
    
    # Create a product
    product = Product.objects.create(
        name='Test Product',
        brand=brand,
        rate=100.0,
        tax_rate=18.0,
        user=user
    )
    
    # Create a sales invoice
    invoice = SalesInvoice.objects.create(
        user=user,
        buyer=buyer,
        date=timezone.now().date(),
        bill_amount=1180.0,
        receivable_amount=1180.0,
        invoice_status='billed'
    )
    
    # Create sales invoice items
    SalesInvoiceItem.objects.create(
        sales_invoice=invoice,
        product=product,
        no=10.0,  # quantity
        weight=5.0,  # pieces
        rate=100.0,
        line_total=1000.0,
        remarks='Test item'
    )
    
    # Create another item with zero sales
    SalesInvoiceItem.objects.create(
        sales_invoice=invoice,
        product=product,
        no=0.0,
        weight=0.0,
        rate=50.0,
        line_total=0.0,
        remarks='Zero sales item'
    )
    
    print("2. Test data created successfully!")
    
    # Test the reports
    factory = APIRequestFactory()
    
    # Test Brand-wise Sales Report
    print("3. Testing Brand-wise Sales Report...")
    request = factory.get('/report/', {
        'report_type': 'brand_wise_sales',
        'from_date': (timezone.now().date() - timedelta(days=1)).isoformat(),
        'to_date': (timezone.now().date() + timedelta(days=1)).isoformat(),
        'include_zero_sales': 'true',
        'brand_ids': [brand.id]
    })
    force_authenticate(request, user=user)
    view = ReportView.as_view()
    response = view(request)
    
    if response.status_code == 200:
        data = response.data
        if data.get('success') and data.get('data'):
            print("   ✓ Brand-wise Sales Report working correctly")
            print(f"   ✓ Found {len(data['data'])} records")
        else:
            print("   ✗ Brand-wise Sales Report failed")
    else:
        print(f"   ✗ Brand-wise Sales Report failed with status {response.status_code}")
    
    # Test General Sales Report
    print("4. Testing General Sales Report...")
    request = factory.get('/report/', {
        'report_type': 'general_sales',
        'from_date': (timezone.now().date() - timedelta(days=1)).isoformat(),
        'to_date': (timezone.now().date() + timedelta(days=1)).isoformat(),
        'include_zero_sales': 'true'
    })
    force_authenticate(request, user=user)
    response = view(request)
    
    if response.status_code == 200:
        data = response.data
        if data.get('success') and data.get('data'):
            print("   ✓ General Sales Report working correctly")
            print(f"   ✓ Found {len(data['data'])} records")
        else:
            print("   ✗ General Sales Report failed")
    else:
        print(f"   ✗ General Sales Report failed with status {response.status_code}")
    
    # Test zero sales exclusion
    print("5. Testing zero sales exclusion...")
    request = factory.get('/report/', {
        'report_type': 'general_sales',
        'from_date': (timezone.now().date() - timedelta(days=1)).isoformat(),
        'to_date': (timezone.now().date() + timedelta(days=1)).isoformat(),
        'include_zero_sales': 'false'
    })
    force_authenticate(request, user=user)
    response = view(request)
    
    if response.status_code == 200:
        data = response.data
        if data.get('success') and data.get('data'):
            # Should have fewer records when excluding zero sales
            records_with_zero = len(data['data'])
            print(f"   ✓ Zero sales exclusion working (found {records_with_zero} records)")
        else:
            print("   ✗ Zero sales exclusion failed")
    else:
        print(f"   ✗ Zero sales exclusion failed with status {response.status_code}")
    
    print("6. Cleaning up test data...")
    
    # Clean up
    SalesInvoiceItem.objects.filter(sales_invoice=invoice).delete()
    SalesInvoice.objects.filter(id=invoice.id).delete()
    Product.objects.filter(id=product.id).delete()
    Buyer.objects.filter(id=buyer.id).delete()
    Brand.objects.filter(id=brand.id).delete()
    User.objects.filter(id=user.id).delete()
    
    print("✓ Test completed successfully!")
    print("✓ All new sales reports are working correctly!")

if __name__ == '__main__':
    test_sales_reports() 