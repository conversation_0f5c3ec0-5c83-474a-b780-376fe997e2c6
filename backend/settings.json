{"reports": [], "enablePR": true, "bill_type": [{"size": [{"name": "A4 Sheet", "size": "A4", "active": true, "selected": true}, {"name": "A5 Sheet", "size": "A5", "active": true, "selected": false}], "type": "Regular", "active": true, "selected": false}, {"size": [{"name": "2 inch", "size": 58, "active": true, "selected": false}, {"name": "3 inch", "size": 80, "active": true, "selected": false}], "type": "Thermal", "active": true, "selected": true}], "component": [{"url": "product", "icon": "fast-food-outline", "slug": "product", "title": "Products", "active": true, "sort_order": 1}, {"url": "sales-bill", "icon": "bag-add-outline", "slug": "salesinvoice", "title": "Sales Bill", "active": true, "sort_order": 2}, {"url": "sales-order", "icon": "document-attach", "slug": "salesinvoice", "title": "Sales Order", "active": true, "sort_order": 3}, {"url": "buyers", "icon": "person-add-outline", "slug": "buyer", "title": "Buyers", "active": true, "sort_order": 4}, {"url": "purchase-bill", "icon": "bag-remove-outline", "slug": "purchaseinvoice", "title": "Purchase Bill", "active": true, "sort_order": 5}, {"url": "ledger", "icon": "cash", "slug": "ledger", "title": "Ledger", "active": true, "sort_order": 6}, {"url": "tally", "icon": "trending-up-outline", "slug": "tally", "title": "<PERSON><PERSON>", "active": true, "sort_order": 7}, {"url": "expense", "icon": "pricetag-outline", "slug": "expense", "title": "Expense", "active": true, "sort_order": 8}, {"url": "sales-payment", "icon": "cash", "slug": "salesinvoice", "title": "Sales Report", "active": true, "sort_order": 9}, {"url": "purchase-payment", "icon": "cash-outline", "slug": "purchaseinvoice", "title": "Purchase Report", "active": true, "sort_order": 10}, {"url": "suppliers", "icon": "person-remove-outline", "slug": "suplier", "title": "Suppliers", "active": true, "sort_order": 11}, {"url": "report", "icon": "albums", "slug": "reports", "title": "Reports", "active": true, "sort_order": 12}, {"url": "retailer-class", "icon": "crop", "slug": "buyerclass", "title": "Retailer Class", "active": true, "sort_order": 13}, {"url": "closing-stocks", "icon": "time", "slug": "closingstocks", "title": "Closing Stock", "active": true, "sort_order": 14}], "gstReport": true, "marginalItem": true, "modeOfPayment": [{"name": "Cash", "slug": "cash"}, {"name": "South Indian Bank 0013", "slug": "south-indian-bank-0013"}, {"name": "South Indian Bank 0592", "slug": "south-indian-bank-0592"}], "ledgerFieldName": [{"slug": "debit", "displayName": "Paid"}, {"slug": "credit", "displayName": "Received"}], "billing_settings": {"item_display": "short_code", "enableMarginalView": true, "rateChangePassword": "Abinesh*1998", "enablePopoverWeight": false, "enableRateChangeField": false, "enableRemarksItemView": true, "enableRemarksInvoiceView": true, "defaultIncrementType": "boxes", "primaryIncrementField": "box", "defaultViewMode": "table"}, "componentSorting": true, "billingHeaderData": [{"name": "PO Number", "slug": "po_number"}, {"name": "Vechile Number", "slug": "vechile_number"}, {"name": "Driver Name", "slug": "driver_name"}], "billing_field_settings": [{"size": "2", "slug": "box", "style": {"font-size": "20px !important"}, "active": true, "sort_order": 4, "displayName": "Box", "print_order": 4, "print_active": true}, {"size": "2", "slug": "pcs", "style": {"font-size": "20px !important"}, "active": true, "sort_order": 5, "displayName": "Pcs", "print_order": 5, "print_active": true}, {"size": "8", "slug": "item", "style": {"font-size": "20px !important"}, "active": true, "sort_order": 1, "displayName": "<PERSON><PERSON>", "print_order": 1, "print_active": true}, {"size": "2", "slug": "rate", "style": {"font-size": "20px !important"}, "active": false, "sort_order": 3, "displayName": "Rate", "print_order": 3, "print_active": true}, {"size": "1.9", "slug": "total", "style": {"font-size": "20px !important"}, "active": false, "sort_order": 6, "displayName": "Total", "print_order": 8, "print_active": true}, {"size": "12", "slug": "desc", "style": {"font-size": "20px !important"}, "active": false, "sort_order": 7, "displayName": "remarks", "print_order": 6, "print_active": false}, {"size": "12", "slug": "mrp", "style": {"font-size": "20px !important"}, "active": false, "sort_order": 7, "displayName": "MRP", "print_order": 2, "print_active": true}, {"size": "12", "slug": "tax_rate", "style": {"font-size": "20px !important"}, "active": false, "sort_order": 7, "displayName": "Tax %", "print_order": 6, "print_active": true}, {"size": "12", "slug": "tax_amount", "style": {"font-size": "20px !important"}, "active": false, "sort_order": 7, "displayName": "Tax ₹", "print_order": 7, "print_active": true}], "billingAdditionalFields": [{"slug": "discount", "active": true, "fieldType": "discount", "operation": "minus", "sort_order": 1, "displayName": "Discount", "placeholder": "0"}, {"slug": "commission", "active": false, "fieldType": "discount", "operation": "add", "sort_order": 1, "displayName": "Comission", "placeholder": "0"}, {"slug": "sungam", "active": false, "fieldType": "number", "operation": "minus", "sort_order": 2, "displayName": "<PERSON><PERSON>", "placeholder": "0"}, {"slug": "labour", "active": false, "fieldType": "number", "operation": "minus", "sort_order": 3, "displayName": "Labour", "placeholder": "0"}, {"slug": "frieght", "active": false, "fieldType": "number", "operation": "minus", "sort_order": 4, "displayName": "<PERSON><PERSON><PERSON>", "placeholder": "0"}, {"slug": "miscellaneous", "active": false, "fieldType": "number", "operation": "minus", "sort_order": 5, "displayName": "Miscellaneous", "placeholder": "0"}]}