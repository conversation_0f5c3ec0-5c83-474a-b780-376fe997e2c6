from django.urls import path, include
from django.views.decorators.csrf import csrf_exempt
from . import views

urlpatterns = [
    path('login/', views.LoginView.as_view()),
    path('product/',views.ProductView.as_view()),
    path('closing_stock/',views.ClosingStockView.as_view()),
    path('incentive/',views.IncentiveView.as_view()),
    path('brand/',views.BrandView.as_view()),
    path('route/',views.RouteView.as_view()),
    path('buyer_class/',views.BuyerClassView.as_view()),
    path('buyer_class_margin/',views.BuyerClassMarginView.as_view()),
    path('buyer/',views.BuyerView.as_view()),
    path('deposit/',views.DepositView.as_view()),
    path('deposit/',views.AssetView.as_view()),
    path('metadata/',views.MetadataView.as_view()),
    path('suplier/',views.SuplierView.as_view()),
    path('supplier_brand/',views.SupplierBrandView.as_view()),
    path('buyer_brand/',views.BuyerBrandView.as_view()),
    path('sales_invoice/',views.SalesInvoiceView.as_view()),
    path('sales_invoice_payment/',views.SalesInvoicePaymentView.as_view()),
    path('sales_invoice_print/',views.SalesInvoicePrintView.as_view()),
    path('sales_order/',views.SalesOrderView.as_view()),
    path('sales_payment/',views.SalesPaymentView.as_view()),
    path('purchase_payment/',views.PurchasePaymentView.as_view()),
    path('purchase_invoice/',views.PurchaseInvoiceView.as_view()),
    path('line_account_data/',views.LineAccountsView.as_view()),
    path('tally/',views.TallyView.as_view()),
    path('ledger/',views.LedgerView.as_view()),
    path('party/',views.PartyView.as_view()),
    path('item/',views.ItemView.as_view()),
    path('rate/',views.RateView.as_view()),
    path('order/',views.OrderView.as_view()),
    path('invoice/',views.InvoiceView.as_view()),
    path('expense_category/',views.ExpenseCategoryView.as_view()),
    path('expense_sub_category/',views.ExpenseSubCategoryView.as_view()),
    path('expense/',views.ExpenseView.as_view()),
    path('sales_invoice_report/',views.SalesInvoiceReportView.as_view()),
    path('report/',views.ReportView.as_view()),
    path('import-export/<str:app_label>/<str:model_name>/', views.ImportExportView.as_view()),
    # path('analytics-summary', views.analytics_summary),
    # path('po-drafts', views.PurchaseOrderDraftViewSet),
    path('dashboard/',views.DashboardView.as_view()),
    path('sales-trend-by-brand/',views.DashboardView.as_view()),
    path('product-summary/',views.ProductSummaryView.as_view()),
    path('buyer-summary/',views.BuyerSummaryView.as_view()),
    path('purchase_order/',views.PurchaseOrderView.as_view()),
    path('purchase_order_draft/',views.PurchaseOrderDraftView.as_view()),
    path('supplier_products/',views.SupplierProductsView.as_view()),
    path('buyer_products/',views.BuyerProductsView.as_view()),
    path('buyer-management/',views.UserManagementView.as_view()),
    path('checklist/', views.ChecklistView.as_view()),
    path('checklist-item/', views.ChecklistItemView.as_view()),
    path('route-billing-summary/', views.RouteBillingSummaryView.as_view()),
    path('shop-freezer-photo/', views.ShopFreezerPhotoView.as_view()),
    path('route-schedule/', views.RouteScheduleView.as_view()),
    path('language-switch/', views.LanguageSwitchView.as_view()),
    path('invoice-image/', views.InvoiceImageView.as_view()),
]
