from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from .models import SalesInvoiceItem, PurchaseInvoiceItem, Product

# ✅ Track old quantity before saving
@receiver(pre_save, sender=SalesInvoiceItem)
def track_old_sales_qty(sender, instance, **kwargs):
    """
    Store the old quantity before updating a SalesInvoiceItem.
    """
    if instance.pk:  # If updating an existing record
        try:
            old_instance = SalesInvoiceItem.objects.get(pk=instance.pk)
            instance._old_confirmed_pcs = (old_instance.product.unit_contains * old_instance.no) + old_instance.weight  # Store old qty
        except SalesInvoiceItem.DoesNotExist:
            instance._old_confirmed_pcs = 0
    else:
        instance._old_confirmed_pcs = 0  # New record, no previous quantity


# ✅ Update stock when a sales invoice item is created or updated
@receiver(post_save, sender=SalesInvoiceItem)
def update_stock_on_sales(sender, instance, **kwargs):
    """
    Adjust stock when a sale is made or updated.
    """
    product = instance.product
    if product:
        old_qty = instance._old_confirmed_pcs  # Get the old quantity
        new_qty = (instance.product.unit_contains * instance.no) + instance.weight  # Get the new quantity

        difference = new_qty - old_qty  # Calculate the difference

        product.stock_quantity -= difference  # Adjust stock
        product.save()


# ✅ Restore stock when a sales invoice item is deleted
@receiver(post_delete, sender=SalesInvoiceItem)
def restore_stock_on_sales_delete(sender, instance, **kwargs):
    """
    Restore stock when a sales invoice item is deleted.
    """
    try:
        product = instance.product
        if product:
            product.stock_quantity += (instance.product.unit_contains * instance.no) + instance.weight  # Restore deleted quantity
            product.save()
    except:
        pass


# ✅ Track old quantity before saving purchase items
@receiver(pre_save, sender=PurchaseInvoiceItem)
def track_old_purchase_qty(sender, instance, **kwargs):
    """
    Store the old quantity before updating a PurchaseInvoiceItem.
    """
    if instance.pk:
        try:
            old_instance = PurchaseInvoiceItem.objects.get(pk=instance.pk)
            instance._old_confirmed_pcs =  (old_instance.product.unit_contains * old_instance.no) + old_instance.weight
        except PurchaseInvoiceItem.DoesNotExist:
            instance._old_confirmed_pcs = 0
    else:
        instance._old_confirmed_pcs = 0


# ✅ Update stock when a purchase invoice item is created or updated
@receiver(post_save, sender=PurchaseInvoiceItem)
def update_stock_on_purchase(sender, instance, **kwargs):
    """
    Adjust stock when a purchase is made or updated.
    """
    product = instance.product
    if product:
        old_qty = instance._old_confirmed_pcs
        new_qty = (instance.product.unit_contains * instance.no) + instance.weight

        difference = new_qty - old_qty
        product.stock_quantity += difference  # Adjust stock
        product.save()


# ✅ Reduce stock when a purchase invoice item is deleted
@receiver(post_delete, sender=PurchaseInvoiceItem)
def remove_stock_on_purchase_delete(sender, instance, **kwargs):
    """
    Reduce stock when a purchase is deleted.
    """
    try:
        product = instance.product
        if product and product.stock_quantity and instance.no and instance.weight:
            product.stock_quantity = float(product.stock_quantity) - (float(instance.product.unit_contains * instance.no) + float(instance.weight))
            product.save()
    except:
        pass
