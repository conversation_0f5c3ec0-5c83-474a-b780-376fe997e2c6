from rest_framework.views import exception_handler
from rest_framework.exceptions import PermissionDenied, NotAuthenticated
from rest_framework.response import Response
from django.db.models import Sum, Value, FloatField,F
from django.db.models.functions import Coalesce
from django.utils.timezone import make_aware
from collections import defaultdict
from datetime import datetime
from .models import *

def custom_exception_handler(exc, context):
    # Call the default exception handler first to get the standard response.
    response = exception_handler(exc, context)
    
    # Customize the 403 response.
    if isinstance(exc, (PermissionDenied, NotAuthenticated)):
        if response is not None:
            response.data = {
                "message": response.data.get("detail", "You do not have permission to perform this action.")
            }
    return response



class ProductStockManager:

    @staticmethod
    def get_closing_stock(from_date, to_date, products_queryset=None):
        # Convert dates to timezone-aware
        from_date = make_aware(datetime.strptime(from_date, "%Y-%m-%d"))
        to_date = make_aware(datetime.strptime(to_date, "%Y-%m-%d"))

        # Use all products if no filter is given
        products_queryset = products_queryset or Product.objects.all()
        product_ids = products_queryset.values_list("id", flat=True)

        # Aggregate opening stock (before from_date)
        purchases_before = PurchaseInvoiceItem.objects.filter(
            product_id__in=product_ids, purchase_invoice__date__lt=from_date
        ).values("product").annotate(
            total_purchased=Coalesce(Sum("no", output_field=FloatField()), Value(0, output_field=FloatField())),
            total_weight_purchased=Coalesce(Sum("weight", output_field=FloatField()), Value(0, output_field=FloatField()))
        )

        sales_before = SalesInvoiceItem.objects.filter(
            product_id__in=product_ids, sales_invoice__date__lt=from_date
        ).values("product").annotate(
            total_sold=Coalesce(Sum("no", output_field=FloatField()), Value(0, output_field=FloatField())),
            total_weight_sold=Coalesce(Sum("weight", output_field=FloatField()), Value(0, output_field=FloatField()))
        )

        # Aggregate purchases & sales within the date range
        purchases_within = PurchaseInvoiceItem.objects.filter(
            product_id__in=product_ids, purchase_invoice__date__range=(from_date, to_date)
        ).values("product").annotate(
            total_purchased=Coalesce(Sum("no", output_field=FloatField()), Value(0, output_field=FloatField())),
            total_weight_purchased=Coalesce(Sum("weight", output_field=FloatField()), Value(0, output_field=FloatField()))
        )

        sales_within = SalesInvoiceItem.objects.filter(
            product_id__in=product_ids, sales_invoice__date__range=(from_date, to_date)
        ).values("product").annotate(
            total_sold=Coalesce(Sum("no", output_field=FloatField()), Value(0, output_field=FloatField())),
            total_weight_sold=Coalesce(Sum("weight", output_field=FloatField()), Value(0, output_field=FloatField()))
        )

        # Store aggregated results in dictionaries
        opening_stock_dict = defaultdict(float)
        opening_weight_dict = defaultdict(float)
        purchased_dict = defaultdict(float)
        purchased_weight_dict = defaultdict(float)
        sold_dict = defaultdict(float)
        sold_weight_dict = defaultdict(float)

        for item in purchases_before:
            product_id = item["product"]
            opening_stock_dict[product_id] = item["total_purchased"]
            opening_weight_dict[product_id] = item["total_weight_purchased"]

        for item in sales_before:
            product_id = item["product"]
            opening_stock_dict[product_id] -= item["total_sold"]
            opening_weight_dict[product_id] -= item["total_weight_sold"]

        for item in purchases_within:
            product_id = item["product"]
            purchased_dict[product_id] = item["total_purchased"]
            purchased_weight_dict[product_id] = item["total_weight_purchased"]

        for item in sales_within:
            product_id = item["product"]
            sold_dict[product_id] = item["total_sold"]
            sold_weight_dict[product_id] = item["total_weight_sold"]

        # Compile results
        return [
            {
                "product": product.name,
                "opening_qty": opening_stock_dict[product.id],
                "opening_pcs": opening_weight_dict[product.id],
                "total_purchased_qty": purchased_dict[product.id],
                "total_purchased_pcs": purchased_weight_dict[product.id],
                "total_sold_qty": sold_dict[product.id],
                "total_sold_pcs": sold_weight_dict[product.id],
                "closing_qty": opening_stock_dict[product.id] + purchased_dict[product.id] - sold_dict[product.id],
                "closing_pcs": opening_weight_dict[product.id] + purchased_weight_dict[product.id] - sold_weight_dict[product.id]
            }
            for product in products_queryset
            if any([
                opening_stock_dict[product.id], 
                purchased_dict[product.id], 
                sold_dict[product.id], 
                (opening_stock_dict[product.id] + purchased_dict[product.id] - sold_dict[product.id])
            ])
        ]

def calculate_salesman_incentive(salesman_id, from_date, to_date):
    """
    Calculate incentive for a salesman based on confirmed sales.

    :param salesman_id: ID of the salesman
    :param from_date: Start date (YYYY-MM-DD)
    :param to_date: End date (YYYY-MM-DD)
    :return: Total confirmed sales and calculated incentive
    """

    # Convert dates to timezone-aware
    from_date = make_aware(datetime.strptime(from_date, "%Y-%m-%d"))
    to_date = make_aware(datetime.strptime(to_date, "%Y-%m-%d"))

    # Get total confirmed sales amount
    total_confirmed_sales = SalesInvoiceItem.objects.filter(
        sales_invoice__sales_person__id=salesman_id,
        sales_invoice__date__range=(from_date, to_date),
        sales_invoice__invoice_status="billed"
    ).aggregate(
        total_amount=Coalesce(Sum(((F("no") * F("product__unit_contains"))+ F("weight")) * F("rate"), output_field=FloatField()), Value(0, output_field=FloatField()))
    )["total_amount"]

    # Define incentive slabs
    incentive_slabs = [
        (100000, 0.02),  # 2% for confirmed sales >= 1L
        (200000, 0.03),  # 3% for confirmed sales >= 2L
        (500000, 0.05)   # 5% for confirmed sales >= 5L
    ]

    # Determine incentive percentage
    incentive_percentage = 0
    for sales_threshold, percentage in incentive_slabs:
        if total_confirmed_sales >= sales_threshold:
            incentive_percentage = percentage

    # Calculate incentive amount
    # incentive_amount = total_confirmed_sales * incentive_percentage
    incentive_amount = total_confirmed_sales * 0.02

    return {
        "salesman_id": salesman_id,
        "total_confirmed_sales": total_confirmed_sales,
        "incentive_percentage": 0.02 * 100,
        "incentive_amount": incentive_amount
    }
