from django.contrib.admin.models import LogEntry, ADDITION
from django.contrib.contenttypes.models import ContentType
from django.utils import translation
from django.utils.deprecation import MiddlewareMixin

class AddFieldsMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Code to execute before the view is called
        self.add_custom_fields_to_request(request)

        response = self.get_response(request)

        # Code to execute after the view is called
        return response

    def add_custom_fields_to_request(self, request):
        # Add or modify fields in the request
        print(request.user)

        if request.user.is_authenticated:
            if  request.user and request.user.company is not None:
                print(request.user)
                request.company = request.user.company
            else:
                request.company = request.user
        else:
            request.company = None

    def process_view(self, request, view_func, view_args, view_kwargs):
        return None


class UserLanguageMiddleware(MiddlewareMixin):
    """
    Middleware to activate user's preferred language for each request
    """
    
    def process_request(self, request):
        # Skip for anonymous users
        if not hasattr(request, 'user') or request.user.is_anonymous:
            return
            
        # Get user's preferred language
        if hasattr(request.user, 'preferred_language') and request.user.preferred_language:
            language_code = request.user.preferred_language
            
            # Validate language code
            if language_code in ['en', 'ta']:
                translation.activate(language_code)
                request.LANGUAGE_CODE = language_code
