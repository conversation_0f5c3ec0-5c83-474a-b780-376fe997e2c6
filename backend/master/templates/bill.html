{% load template_filters %}
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{user.company_name}}</title>
    <script type="module" src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.esm.js"></script>
    <script nomodule src="https://cdn.jsdelivr.net/npm/@ionic/core/dist/ionic/ionic.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@ionic/core/css/ionic.bundle.css" />
    <style type="text/css">
         @page {
            size: 80mm 3276mm;
            /* Set the page size to 8.5 inches by 11 inches */
            margin: 0.2mm 0.2mm 0.2mm 0.2mm;
        } 
    </style>
</head>

<body>
    <ion-app>
        <ion-content fullscreen>
            <table>
                <tr>
                    <th style="width: 15%;"><img src="{{logo}}" width="120px" height="120px"></th>
                    <th>
                        <div>
                            <ion-label>
                                <b style="padding:4px;font-size: 18px;margin-top: 4px;">{{user.company_name}}</b>
                            </ion-label><br>
                            <ion-label style="font-size: medium;">{{user.address}}</ion-label><br>
                            <table>
                                <tr>
                                    <th>
                                        <ion-label>{{user.contact_no_left}}</ion-label>
                                    </th>
                                    <th>
                                        <ion-label>{{user.contact_no_right}}</ion-label>
                                    </th>
                                </tr>
                            </table>
                            <table>
                                <tr>
                                    <th>
                                        <ion-label>FASSAI license no:{{user.fssai_no}}</ion-label>
                                    </th>
                                </tr>
                                <tr>
                                    <th>
                                        <ion-label>GST:{{user.gst_no}}</ion-label>
                                    </th>
                                </tr>
                            </table>
                        </div>
                    </th>
                </tr>
            </table>
            <hr style="height:2px; width:100%; border-width:0; color:black; background-color:black">
            <table>
                <tr>
                    <th style="text-align: start;width: 65%;padding-left: 2px;">
                        <ion-label style="font-size: 16px;"> Name:{{ invoice.name }}</b> </ion-label><br>
                        <ion-label><b> Place:{{ invoice.place }}</b> </ion-label><br>
                        <ion-label><b> Phone No:{{ invoice.phone_no }}</b> </ion-label>
                    </th>
                    <th style="text-align: start;width: 35%;">
                        <p><b>Bill no: </b>{{ invoice.id }}</p>
                        <p> <b>Date</b> :{{ invoice.date }}</p>
                    </th>
                </tr>
            </table>

            <hr style="height:2px; width:100%; border-width:0; color:black; background-color:black">
            <table>
                <thead>
                    <th style="width: 40%;">Product</th>
                    <th style="width: 15%;">Rate</th>
                    <th style="width: 10%;">Tax</th>
                    <th style="width: 10%;">Box</th>
                    <th style="width: 10%;">Pcs</th>
                    <th sstyle="width: 15%;">Amount</th>
                </thead>
            </table>
            <hr style="height:2px; width:100%; border-width:0; color:black; background-color:black">
            </table>
            <table>
                {% for o in invoice.sales_invoice_items %}
                <tr>
                    <th style="width: 40%;">
                        <p>{{o.product_name}}</p>
                    </th>
                    <th style="width: 15%;">
                        <p>{{o.rate}}</p>
                    </th>
                    <th style="width: 10%;">
                        <p>{{o.tax_rate}}</p>
                    </th>
                    <th style="width: 10%;">
                        <p>{{o.box}}</p>
                    </th>
                    <th style="width: 10%;">
                        <p>{{o.pcs}}</p>
                    </th>
                    <th style="width: 15%;">
                        <p>{{o.line_total}}</p>
                    </th>
                </tr>
                {% endfor %}
            </table>
            <hr style="height:2px; width:100%; border-width:0; color:black; background-color:black">
            <table>
                <tr>
                    <th>Total</th>
                    <th>{{total_qty}} Item(s)</th>
                    <th>{{total_box}} Box</th>
                    <th>{{total_pcs}} pcs</th>
                    <th>{{invoice.bill_amount}}</th>
                </tr>
            </table>
            <hr style="height:2px; width:100%; border-width:0; color:black; background-color:black">
            <table>
                {% for t in tax_seperation %}
                <tr>
                    <th>CGST {{ t.product__tax_rate | divide_by:2  }}%</th>
                    <th>{{ t.tax_amount__sum | divide_by:2  }}</th>
                    <th>SGST {{ t.product__tax_rate | divide_by:2  }}%</th>
                    <th>{{ t.tax_amount__sum | divide_by:2  }}</th>
                </tr>
                {% endfor %}
            </table>
            <hr style="height:2px; width:100%; border-width:0; color:black; background-color:black">
            <table>
                <tr>
                    <th>
                        <table>
                            <tr>
                                <th>Opening Amt:</th>
                                <th>{{invoice.previous_balance}}</th>
                            </tr>
                            <tr>
                                <th>Bill Amt :</th>
                                <th>{{invoice.receivable_amount}}</th>
                            </tr>
                            <tr>
                                <th>Received Amt:</th>
                                <th>{{invoice.received_amount}}</th>
                            </tr>
                            <tr>
                                <th>Balance Amt:</th>
                                <th>{{invoice.current_balance}}</th>
                            </tr>
                        </table>
                    </th>
                    <th>
                        <table>
                            <tr>
                                <th>Sub Total:</th>
                                <th>{{sub_total}}</th>
                            </tr>
                            <tr>
                                <th>Tax Amount :</th>
                                <th>{{tax_amount}}</th>
                            </tr>
                            <tr>
                                <th>Gross Total:</th>
                                <th>{% if invoice.gross_total %}{{invoice.gross_total}}{% else %}{{invoice.bill_amount}}{% endif %}</th>
                            </tr>
                            {% if invoice.rounding_enabled and invoice.rounding_adjustment != 0 %}
                            <tr>
                                <th>Rounding Off:</th>
                                <th>{% if invoice.rounding_adjustment > 0 %}+{% endif %}{{invoice.rounding_adjustment}}</th>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>Net Amount:</th>
                                <th>{{invoice.bill_amount}}</th>
                            </tr>
                        </table>
                    </th>

                </tr>
            </table>


            <hr style="height:2px; width:100%; border-width:0; color:black; background-color:black">
            <div style="text-align: center;">
                <ion-label>Thanks For Purchase With {{user.company_name}} </ion-label><br>
                <ion-label>* Visit Again * </ion-label>
            </div>
        </ion-content>
    </ion-app>
</body>

</html>