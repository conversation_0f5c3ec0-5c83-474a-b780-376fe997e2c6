{% load template_filters %}
<!DOCTYPE html>
<html>

<head>
    <title>{{ name}} - {{date}}</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.3.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.3.2/html2canvas.min.js"></script>

    <style>
        * {
            font-family: "arial";
            width: 100%;
            box-sizing: border-box;
        }

        .sheet {

            page-break-after: auto !important;
        }

        html,
        body {
            height: 100%;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden;
        }

        .page {
            page-break-after: avoid;
            page-break-inside: avoid;
            font-size: smaller;
        }

        .container {
            display: table;
        }

        .container:nth-child(21) {
            page-break-after: always;
            page-break-inside: avoid;
        }

        .container {
            font-size: 0;
        }

        .header-item {
            display: inline-block;
            font-size: 22px !important;
        }

        div {
            box-sizing: border-box;
        }

        .page {
            min-height: 297mm;
            max-height: 297mm;
        }

        @page {
            size: A4;

        }


        @media print {

            body {
                margin: 0;
                zoom: 65%;
            }



            thead {
                display: table-header-group;
            }

            tfoot {
                display: table-footer-group;
            }
        }
    </style>
</head>

<body>
    <script>
        // Your JavaScript function here
        function printInvoice(data, logo, user) {
            let totalWeight = 0,
                totalNo = 0,
                totalAmount = 0;
            data.sales_invoice_items.forEach((element) => {
                if (element.weight) {
                    totalWeight += element.weight;
                }
                if (element.no) {
                    totalNo += element.no;
                }
                if (element.line_total) {
                    totalAmount += element.line_total;
                }
            });
            let item = data.sales_invoice_items.map((e, index) => {

                return `
        <div class="header-item"
            style="border-right: 0.5px solid; border-top:0.5px solid; width: 35%; font-size: 9px; padding: 3px;text-align: left;">
            ${e.product_name}
        </div>
        <div class="header-item"
            style="border-right: 0.5px solid; border-top:0.5px solid; width: 9%; font-size: 9px; padding: 3px;text-align: right;">
            ${e.mrp}
        </div>
        <div class="header-item"
            style="border-right: 0.5px solid; border-top:0.5px solid; width: 9%; font-size: 9px; padding: 3px;text-align: right;">
                ${(e.rate - (e.rate * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)} 
        </div>
        <div class="header-item"
            style="border-right: 0.5px solid; border-top:0.5px solid; width: 8%; font-size: 9px; padding: 3px;text-align: right;">
        ${e.no}
        </div>
        <div class="header-item"
            style="border-right: 0.5px solid; border-top:0.5px solid; width: 8%; font-size: 9px; padding: 3px;text-align: right;">
            ${e.weight} 
        </div>
        <div class="header-item"
            style="border-right: 0.5px solid; border-top:0.5px solid; width: 7%; font-size: 9px; padding: 3px;text-align: right;">
            ${e.tax_rate}
        </div>
        <div class="header-item"
            style="border-right: 0.5px solid; border-top:0.5px solid; width: 12%; font-size: 9px; padding: 3px;text-align: right;">
            ${((e.line_total * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)}
        </div>
        <div class="header-item"
            style="border-top:0.5px solid; width: 12%; font-size: 9px; padding: 3px;text-align: right;">
            ${e.line_total.toFixed(2)}
        </div>`;
            });

            let items = item.join("");
            let distinctValues = [...new Set(data.sales_invoice_items.map(item => item.tax_rate))];
            let taxDetails = distinctValues.map(value => ({
                value,
                tax_amount: data.sales_invoice_items
                    .filter(item => item.tax_rate === value)
                    .reduce((acc, cur) => acc + (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate), 0),
                taxable_amount: data.sales_invoice_items
                    .filter(item => item.tax_rate === value)
                    .reduce((acc, cur) => acc + (cur.line_total - (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate)), 0),
            }));
            let totalTaxAmount = data.sales_invoice_items.reduce((acc, cur) => acc + (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate), 0)
            let tax_item = taxDetails.map(e => {
                return `<div style="display: flex;">
            <p
                style="margin: 2px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                ${e.value}</p>
            <p
                style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                ${e.taxable_amount.toFixed(2)}</p>
            <p
                style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                ${e.tax_amount.toFixed(2)}</p>
        </div>`;
            });
            let tax_items = tax_item.join("");

            document.getElementById('invoice-container').innerHTML = `
                <table class="sheet padding-10mm" cellspacing="0" cellpadding="0" width="100%" style="width: 100%;">
                    <thead>
                        <tr>
                            <td width="100%">
                                <div style="display: flex;width: 100%;height:70%;">
                                    <div style="width:auto;"> <img src="${logo}" style="width: 125px;height: 140px;">
                                    </div>
                                    <div style="widht:100%;height:100%;margin-top: -25px;text-align: center;">
                                        <div style="display:flex;">
                                            <h4 style="padding:0;text-align: start;">Phone
                                                No:-${user.contact_no_left}</h4>
                                            <h4 style="padding:0;text-align: center;font-weight:bolder;">Tax Invoice</h4>
                                            <h4 style="padding:0;text-align: right;">Phone
                                                No:-${user.contact_no_right}</h4>
                                        </div>
                                        <h1 style="padding: 0px;margin: 0px;font-weight:bolder;">
                                            ${user.company_name}</h1>
                                        <h4 style="padding: 0px;margin: 0px;font-size:18px;">${user.address}</h4>
                                        <h5>GST NO:- ${user.gst_no} &emsp;FSSAI
                                            NO:-${user.fssai_no}</h6>
                                    </div>
                                </div>
                                <div style="display: flex;">
                                    <div
                                        style="width: 70%;margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid;border-bottom:none; border-right:none;">
                                        <p style="margin: 0; font-size: 25px;font-weight: 300;">To</p>
                                        <h1 style="margin: 0; font-size: 30px;">${data.name}</h1>
                                        <div style="display:flex;">
                                            <!--  <h4 style="margin: 0; font-size: 25px;font-weight: 300;">
                                                    <strong> Phone : </strong> ${data.phone_no}
                                                </h4> -->
                                            <h4 style="margin: 0; font-size: 25px;font-weight: 300;width: 40%;">
                                                ${data.place}
                                            </h4>
                                            <h4 style="margin: 0; font-size: 25px;font-weight: 300;width: 60%;">
                                                GST No:-${data.gst_no}
                                            </h4>
                                        </div>
                                    </div>
                                    <div style="width: 30%;margin-top: 3px; padding: 10px; border: 0.5px solid; border-bottom:none; display: flex;"
                                        align="center">
                                        <div class="content" style="text-align: left; width: 100%;">
                                            <p style="font-size: 25px;font-weight: 300;margin: 0;">
                                                No  : ${data.id}
                                            </p>
                                            <p style="font-size: 25px;font-weight: 300;margin: 0;">
                                                Date &nbsp;: ${data.date}
                                            </p>
                                        </div>
                                    </div>
                                </div>
            
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="page"
                                    style="border: 0.5px solid;position:relative;padding-bottom:30px;font-size: smaller;">
                                    <div class="container" style="width: 100%;">
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 35%; font-size: 12px; padding: 3px;">
                                            <strong> Particulars </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 9%; font-size: 12px; padding: 3px;">
                                            <strong> MRP </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 9%; font-size: 12px; padding: 3px;">
                                            <strong> Rate </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;">
                                            <strong> Box </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;">
                                            <strong> Pcs </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 7%; font-size: 12px; padding: 3px;">
                                            <strong> Tax%</strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 12%; font-size: 12px; padding: 3px;">
                                            <strong> Tax ₹</strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-bottom:0.5px solid; width: 12%; font-size: 10px; padding: 3px;">
                                            <strong> Amount </strong>
                                        </div>
                                    </div>
                                    <div class="container" style="width: 100%;border-bottom:0.5px solid;">
                                        ${items}
                                    </div>
                                    <div class="container"
                                        style="width: 100%;position:absolute;bottom:0;left:0;border-top: 0.5px solid;">
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 40%; font-size: 12px; padding: 3px;">
                                            <strong> Total </strong>
                                        </div>
            
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 10%; font-size: 12px; padding: 3px;">
                                            <strong> &nbsp; </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;text-align:right;">
                                            <strong> ${totalNo} </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;text-align:right;">
                                            <strong> ${totalWeight} </strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 7%; font-size: 12px; padding: 3px;">
                                            <strong> &nbsp;</strong>
                                        </div>
                                        <div class="header-item"
                                            style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 13%; font-size: 12px; padding: 3px;text-align:right;">
                                            <strong>  ${totalTaxAmount.toFixed(2)}</strong>
                                        </div>
            
                                        <div class="header-item"
                                            style="border-bottom:0.5px solid; width: 14%; font-size: 12px; padding: 3px;text-align:right;">
                                            <strong> ${totalAmount.toFixed(2)} </strong>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td>
                                <div id="page-footer">
                                    <div style="display: flex;">
                                        <div
                                            style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-right:none;">
                                            <div style="display: flex;">
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                    Opening Balance</p>
                                                <p
                                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                     ${data.previous_balance} </p>
                                            </div>
                                            <div style="display: flex;">
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                    Bill value</p>
                                                <p
                                                    style="margin: 5px; font-size: 20px; font-weight:900;font-weight:1200em;width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                     ${data.bill_amount.toFixed(2)}</p>
                                            </div>
                                            <div style="display: flex;">
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                    Received Amount</p>
                                                <p
                                                    style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                     ${data.received_amount}</p>
                                            </div>
                                            <div style="display: flex;">
                                                <p
                                                    style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                    Closing Balance</p>
                                                <p
                                                    style="margin: 5px; font-size: 25px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                     <b>${data.current_balance.toFixed(2)}</b> </p>
                                            </div>
                                        </div>

                                            <div
                                                style="display: flex; flex-direction: column; width: 40%; border: 0.5px solid; padding-top: 5px;border-top:none;">
                                                <div style="display: flex;border-bottom:0.5px solid;">
                                                    <p
                                                        style="margin: 2px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                                        Tax Rate</p>
                                                    <p
                                                        style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                                        Taxable Amount</p>
                                                    <p
                                                        style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                                        Tax Amount</p>
                                                </div>
                                                ${tax_items}
                                            </div>
                                            <div
                                                style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-left:none;">
                                                <div style="display: flex;">
                                                    <p
                                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                        Base Amt</p>
                                                    <p
                                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                         ${(data.bill_amount - totalTaxAmount).toFixed(2)} </p>
                                                </div>
                                                <div style="display: flex;">
                                                    <p
                                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                        Tax Amt</p>
                                                    <p
                                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                         ${totalTaxAmount.toFixed(2)} </p>
                                                </div>
                                                <div style="display: flex;">
                                                    <p
                                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                                        Net Amt</p>
                                                    <p
                                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                                         ${data.bill_amount.toFixed(2)}</p>
                                                </div>
                                                <div style="display: flex;">
                                                    <p
                                                        style="margin: 5px; font-size: 20px;  text-align: center; padding-left: 10px; border-top: 0.5px solid;">
                                                        Authorized Sign</p>
            
                                                </div>
                                            </div>
            
                                        </div>
                                    </div>
                            </td>
                        </tr>
                    </tfoot>
                </table>`;
        };

        $(document).ready(function () {
            // Assuming 'data' is passed from your Django view
            var invoiceData = {{ data| safe}};
            console.log(invoiceData);
            printInvoice(invoiceData.invoice, invoiceData.logo, invoiceData.user);
        });
    </script>

    <div id="invoice-container">
        <!-- The JavaScript will fill this in -->
    </div>
</body>

</html>