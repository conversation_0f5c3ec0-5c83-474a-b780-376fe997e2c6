<html>

<head>
    <title>{{data.name}} - {{data.date}}</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
    <style>
        * {
            font-family: "arial";
            width: 100%;
            box-sizing: border-box;
        }}

        .sheet {

            page-break-after: auto !important;
        }}

        html,
        body {
            height: 100%;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden;
        }}

        .page {
            page-break-after: avoid;
            page-break-inside: avoid;
            font-size: smaller;
        }}

        .container {
            display: table;
        }}

        .container:nth-child(21) {
            page-break-after: always;
            page-break-inside: avoid;
        }}

        .container {
            font-size: 0;
        }}

        .header-item {
            display: inline-block;
            font-size: 22px !important;
        }}

        div {
            box-sizing: border-box;
        }}

        .page {
            min-height: 297mm;
            max-height: 297mm;
        }}

        @page {
            size: A4;

        }}


        @media print {

            body {
                margin: 0;
                zoom: 65%;
            }}



            thead {
                display: table-header-group;
            }}

            tfoot {
                display: table-footer-group;
            }}
        }}
    </style>
</head>

<body>


    <table class="sheet padding-10mm" cellspacing="0" cellpadding="0" width="100%" style="width: 100%;">
        <thead>
            <tr>
                <td width="100%">
                    <div style="display: flex;width: 100%;height:70%;">
                        <div style="width:auto;"> <img src="{{environment.apiUrlClean + localStorage.getItem("
                                company_logo")}}" style="width: 125px;height: 140px;">
                        </div>
                        <div style="widht:100%;height:100%;margin-top: -25px;text-align: center;">
                            <div style="display:flex;">
                                <h4 style="padding:0;text-align: start;">Phone
                                    No:-{{localStorage.getItem('contact_no_left')}}</h4>
                                <h4 style="padding:0;text-align: center;font-weight:bolder;">Tax Invoice</h4>
                                <h4 style="padding:0;text-align: right;">Phone
                                    No:-{{localStorage.getItem('contact_no_right')}}</h4>
                            </div>
                            <h1 style="padding: 0px;margin: 0px;font-weight:bolder;">
                                {{localStorage.getItem("company_name")}}</h1>
                            <h4 style="padding: 0px;margin: 0px;font-size:18px;">{{localStorage.getItem("address")}}</h4>
                            <h5>GST NO:- {{localStorage.getItem('gst_no')}} &emsp;FSSAI
                                NO:-{{localStorage.getItem('fssai_no')}}</h6>
                        </div>
                    </div>
                    <div style="display: flex;">
                        <div
                            style="width: 70%;margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid;border-bottom:none; border-right:none;">
                            <p style="margin: 0; font-size: 25px;font-weight: 300;">To</p>
                            <h1 style="margin: 0; font-size: 30px;">{{data.name}}</h1>
                            <div style="display:flex;">
                                <!--  <h4 style="margin: 0; font-size: 25px;font-weight: 300;">
                                                    <strong> Phone : </strong> {{data.phone_no}}
                                                </h4> -->
                                <h4 style="margin: 0; font-size: 25px;font-weight: 300;width: 40%;">
                                    {{data.place}}
                                </h4>
                                <h4 style="margin: 0; font-size: 25px;font-weight: 300;width: 60%;">
                                    GST No:-{{data.gst_no}}
                                </h4>
                            </div>
                        </div>
                        <div style="width: 30%;margin-top: 3px; padding: 10px; border: 0.5px solid; border-bottom:none; display: flex;"
                            align="center">
                            <div class="content" style="text-align: left; width: 100%;">
                                <p style="font-size: 25px;font-weight: 300;margin: 0;">
                                    No  : {{data.id}}
                                </p>
                                <p style="font-size: 25px;font-weight: 300;margin: 0;">
                                    Date &nbsp;: {{data.date}}
                                </p>
                            </div>
                        </div>
                    </div>

                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <div class="page"
                        style="border: 0.5px solid;position:relative;padding-bottom:30px;font-size: smaller;">
                        <div class="container" style="width: 100%;">
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 35%; font-size: 12px; padding: 3px;">
                                <strong> Particulars </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 9%; font-size: 12px; padding: 3px;">
                                <strong> MRP </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 9%; font-size: 12px; padding: 3px;">
                                <strong> Rate </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;">
                                <strong> Box </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;">
                                <strong> Pcs </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 7%; font-size: 12px; padding: 3px;">
                                <strong> Tax%</strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 12%; font-size: 12px; padding: 3px;">
                                <strong> Tax ₹</strong>
                            </div>
                            <div class="header-item"
                                style="border-bottom:0.5px solid; width: 12%; font-size: 10px; padding: 3px;">
                                <strong> Amount </strong>
                            </div>
                        </div>
                        <div class="container" style="width: 100%;border-bottom:0.5px solid;">
                            {{items}}
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-top:0.5px solid; width: 35%; font-size: 9px; padding: 3px;text-align: left;">
                                {{e.product_name}}
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-top:0.5px solid; width: 9%; font-size: 9px; padding: 3px;text-align: right;">
                                {{e.mrp}}
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-top:0.5px solid; width: 9%; font-size: 9px; padding: 3px;text-align: right;">
                                {{(e.rate - (e.rate * e.tax_rate) / (100+ e.tax_rate)).toFixed(2)}}
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-top:0.5px solid; width: 8%; font-size: 9px; padding: 3px;text-align: right;">
                                {{e.no}}
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-top:0.5px solid; width: 8%; font-size: 9px; padding: 3px;text-align: right;">
                                {{e.weight}}
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-top:0.5px solid; width: 7%; font-size: 9px; padding: 3px;text-align: right;">
                                {{e.tax_rate}}
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-top:0.5px solid; width: 12%; font-size: 9px; padding: 3px;text-align: right;">
                                {{((e.line_total * e.tax_rate) / (100+ e.tax_rate)).toFixed(2)}}
                            </div>
                            <div class="header-item"
                                style="border-top:0.5px solid; width: 12%; font-size: 9px; padding: 3px;text-align: right;">
                                {{e.line_total.toFixed(2)}}
                            </div>
                        </div>
                        <div class="container"
                            style="width: 100%;position:absolute;bottom:0;left:0;border-top: 0.5px solid;">
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 40%; font-size: 12px; padding: 3px;">
                                <strong> Total </strong>
                            </div>

                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 10%; font-size: 12px; padding: 3px;">
                                <strong> &nbsp; </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;text-align:right;">
                                <strong> {{totalNo}} </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 12px; padding: 3px;text-align:right;">
                                <strong> {{totalWeight}} </strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 7%; font-size: 12px; padding: 3px;">
                                <strong> &nbsp;</strong>
                            </div>
                            <div class="header-item"
                                style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 13%; font-size: 12px; padding: 3px;text-align:right;">
                                <strong> {{totalTaxAmount.toFixed(2)}}</strong>
                            </div>

                            <div class="header-item"
                                style="border-bottom:0.5px solid; width: 14%; font-size: 12px; padding: 3px;text-align:right;">
                                <strong> {{totalAmount.toFixed(2)}} </strong>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td>
                    <div id="page-footer">
                        <div style="display: flex;">
                            <div
                                style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-right:none;">
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Opening Balance</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                        {{data.previous_balance}} </p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Bill value</p>
                                    <p
                                        style="margin: 5px; font-size: 20px; font-weight:900;font-weight:1200em;width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                        {{data.bill_amount.toFixed(2)}}</p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Received Amount</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                        {{data.received_amount}}</p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Closing Balance</p>
                                    <p
                                        style="margin: 5px; font-size: 25px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                        <b>{{data.current_balance.toFixed(2)}}</b>
                                    </p>
                                </div>
                            </div>

                            <div
                                style="display: flex; flex-direction: column; width: 40%; border: 0.5px solid; padding-top: 5px;border-top:none;">
                                <div style="display: flex;border-bottom:0.5px solid;">
                                    <p
                                        style="margin: 2px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                        Tax Rate</p>
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                        Taxable Amount</p>
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 40%; text-align: right; padding-left: 10px;">
                                        Tax Amount</p>
                                </div>
                                {{tax_items}}
                            </div>
                            <div
                                style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-left:none;">
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Base Amt</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                        {{(data.bill_amount - totalTaxAmount)}} </p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Tax Amt</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                        {{totalTaxAmount.toFixed(2)}} </p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                        Net Amt</p>
                                    <p
                                        style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                        {{data.bill_amount.toFixed(2)}}</p>
                                </div>
                                <div style="display: flex;">
                                    <p
                                        style="margin: 5px; font-size: 20px;  text-align: center; padding-left: 10px; border-top: 0.5px solid;">
                                        Authorized Sign</p>

                                </div>
                            </div>

                        </div>
                    </div>
                </td>
            </tr>
        </tfoot>
    </table>


</body>

</html>