<!DOCTYPE html>
<html lang="{% get_current_language as LANGUAGE_CODE %}{{ LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% trans "Vegetable Bill App" %}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }
        .language-switcher {
            margin-left: auto;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .language-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
        }
        .language-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .language-btn:hover {
            background: #f8f9fa;
        }
        .language-btn.active:hover {
            background: #0056b3;
        }
        .content {
            line-height: 1.6;
        }
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .menu-item {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .menu-item h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    {% load i18n %}
    
    <div class="container">
        <div class="header">
            <h1>{% trans "Vegetable Bill Application" %}</h1>
            <div class="language-switcher">
                <span>{% trans "Language" %}:</span>
                <form method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" name="language_code" value="en" 
                            class="language-btn {% if LANGUAGE_CODE == 'en' %}active{% endif %}">
                        {% trans "English" %}
                    </button>
                    <button type="submit" name="language_code" value="ta" 
                            class="language-btn {% if LANGUAGE_CODE == 'ta' %}active{% endif %}">
                        {% trans "Tamil" %}
                    </button>
                </form>
            </div>
        </div>
        
        <div class="content">
            <p>{% trans "Welcome to the Vegetable Bill Management System" %}</p>
            <p>{% trans "Current Language" %}: <strong>{% if LANGUAGE_CODE == 'ta' %}தமிழ்{% else %}English{% endif %}</strong>
               <span class="status-indicator status-active">{% trans "Active" %}</span>
            </p>
            
            <div class="menu-grid">
                <div class="menu-item">
                    <h3>{% trans "Products" %}</h3>
                    <p>{% trans "Manage your product inventory, brands, and pricing information." %}</p>
                </div>
                
                <div class="menu-item">
                    <h3>{% trans "Sales" %}</h3>
                    <p>{% trans "Create and manage sales invoices for your customers." %}</p>
                </div>
                
                <div class="menu-item">
                    <h3>{% trans "Purchase" %}</h3>
                    <p>{% trans "Handle purchase orders and supplier management." %}</p>
                </div>
                
                <div class="menu-item">
                    <h3>{% trans "Customers" %}</h3>
                    <p>{% trans "Manage buyer information, routes, and credit limits." %}</p>
                </div>
                
                <div class="menu-item">
                    <h3>{% trans "Reports" %}</h3>
                    <p>{% trans "Generate detailed reports for sales, purchases, and inventory." %}</p>
                </div>
                
                <div class="menu-item">
                    <h3>{% trans "Dashboard" %}</h3>
                    <p>{% trans "View key metrics and business insights at a glance." %}</p>
                </div>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px;">
                <h3>{% trans "Language Feature Demonstration" %}</h3>
                <ul>
                    <li>{% trans "All model names and field labels are translated" %}</li>
                    <li>{% trans "User language preference is saved in database" %}</li>
                    <li>{% trans "Automatic language activation for each user session" %}</li>
                    <li>{% trans "API endpoints support language switching" %}</li>
                    <li>{% trans "Admin interface supports multiple languages" %}</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // You can add JavaScript here for dynamic language switching via API
        console.log('Current language:', '{{ LANGUAGE_CODE }}');
    </script>
</body>
</html>