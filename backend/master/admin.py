from django.contrib import admin

# Register your models here.
from .models import *
from django.contrib.auth.admin import UserAdmin
from jsoneditor.forms import JSONEditor
# Register your models here.

admin.site.site_header = 'Veg'
admin.site.index_title = 'Veg'
admin.site.site_title = 'Veg'


class CustomUserAdmin(UserAdmin):

    fieldsets = UserAdmin.fieldsets + (
        (None, {'fields': ('role', 'device_token','contact_no_left','contact_no_right','address','company_name','driver_name','vehicle_no','company_logo','gst_no','fssai_no','send_wp_message','wp_id','wp_token','company','metadata')}),
    )
    add_fieldsets = UserAdmin.add_fieldsets + (
        (None, {'fields': ('role',  'device_token','contact_no_left','contact_no_right','address','company_name','driver_name','vehicle_no','company_logo','gst_no','fssai_no','send_wp_message','wp_id','wp_token','company','metadata')}),
    )
    formfield_overrides = {
        models.JSONField: {'widget': JSONEditor},
    }

admin.site.register(User, CustomUserAdmin)

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display=['id','name','short_code','sort_order','add','active']
    search_fields=['name']
    list_filter = ['active','user']
    # list_editable = ['name','short_code','sort_order','add','active']
    list_editable = ['sort_order','add','active']

@admin.register(Buyer)
class BuyerAdmin(admin.ModelAdmin):
    list_display=['name','current_balance','active']
    search_fields=['name']
    list_filter = ['active']
    list_editable = ['active']
    

@admin.register(BuyerUser)
class BuyerUserAdmin(admin.ModelAdmin):
    list_display =['user','buyer']
    raw_id_fields=['user','buyer']    


@admin.register(Suplier)
class SuplierAdmin(admin.ModelAdmin):
    list_display=['name','current_balance','active']
    search_fields=['name']
    list_filter = ['active']
    list_editable = ['active']

class SalesInvoiceItemInline(admin.TabularInline):
    model = SalesInvoiceItem
    extra = 0 
    readonly_fields =['product']

@admin.register(SalesInvoice)
class SalesInvoiceAdmin(admin.ModelAdmin):
    list_display = ['id','buyer','receivable_amount','received_amount','current_balance','date']
    list_filter = ['buyer','date']
    search_fields=['buyer__name']
    inlines = [SalesInvoiceItemInline]

class PurchaseInvoiceItemInline(admin.TabularInline):
    model = PurchaseInvoiceItem
    extra = 0 
    readonly_fields =['product']

@admin.register(PurchaseInvoice)
class PurchaseInvoiceAdmin(admin.ModelAdmin):
    list_display = ['id','suplier','receivable_amount','received_amount','date']
    list_filter = ['suplier','date']
    search_fields=['suplier__name']
    inlines = [PurchaseInvoiceItemInline]

@admin.register(Ledger)
class LedgerAdmin(admin.ModelAdmin):
    list_display = ['buyer','supplier','amount','entry_type','closing_amount','invoice']
    list_filter = ['buyer','supplier']
    date_hierarchy = 'created_at'
    search_fields = ['buyer__name','invoice__id']


@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    pass


@admin.register(SubCategory)
class SubCategoryAdmin(admin.ModelAdmin):
    pass

@admin.register(ExpenseCategory)
class ExpenseCategoryAdmin(admin.ModelAdmin):
    pass

@admin.register(Route)
class RouteAdmin(admin.ModelAdmin):
    pass

@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    pass

@admin.register(BuyerBrand)
class BuyerBrandAdmin(admin.ModelAdmin):
    list_display = ['buyer', 'brand', 'is_active', 'created_date']
    list_filter = ['is_active', 'created_date', 'buyer', 'brand']
    search_fields = ['buyer__name', 'brand__name']
    list_editable = ['is_active']
    date_hierarchy = 'created_date'

@admin.register(SupplierBrand)
class SupplierBrandAdmin(admin.ModelAdmin):
    list_display = ['supplier', 'brand', 'is_active', 'created_date']
    list_filter = ['is_active', 'created_date', 'supplier', 'brand']
    search_fields = ['supplier__name', 'brand__name']
    list_editable = ['is_active']
    date_hierarchy = 'created_date'

