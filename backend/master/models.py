from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext as _
from django.utils import timezone
from django.db.models.aggregates import Sum
from django.db.models.signals import pre_save, post_save
from helper.msg import sendTransaction
import re
from django.db.models import Prefetch
from django.utils.functional import cached_property

def is_valid_phone_number(number):
    # Remove all non-numeric characters from the phone number
    clean_number = re.sub("[^0-9]", "", number)
    
    # Check if the cleaned number has exactly ten digits
    return len(clean_number) == 10

USER_ROLES = (
    ('superadmin', 'Super Admin'),
    ('admin', 'Admin'),
    ('accountant', 'Accountant'),
    ('manager', 'Manager'),
    ('customer', 'Customer'),
    ('buyer', 'Buyer'),
    ('sales_officer', 'Sales Officer'),
)

# Create your models here.


class User(AbstractUser):
    role = models.CharField(max_length=25, choices=USER_ROLES,
                            default='customer', null=True, blank=True)
    gst_no = models.CharField(max_length=225, null=True, blank=True)
    fssai_no = models.CharField(max_length=225, null=True, blank=True)
    contact_no_left = models.CharField(max_length=225, null=True, blank=True)
    contact_no_right = models.CharField(max_length=225, null=True, blank=True)
    address = models.CharField(max_length=225, null=True, blank=True)
    company_name = models.CharField(max_length=225, null=True, blank=True)
    company_logo = models.ImageField(upload_to="logo/", null=True,blank=True)
    driver_name = models.CharField(max_length=225, null=True, blank=True)
    vehicle_no = models.CharField(max_length=225, null=True, blank=True)
    device_token = models.CharField(max_length=225, null=True, blank=True)
    send_wp_message = models.BooleanField(default=False)
    wp_id = models.CharField(max_length=225, null=True, blank=True)
    wp_token = models.CharField(max_length=2250, null=True, blank=True)
    company = models.ForeignKey('self', verbose_name=_("Company User"), on_delete=models.CASCADE,null=True,blank=True)
    metadata = models.JSONField(default=dict,null=True,blank=True)
    preferred_language = models.CharField(max_length=20, default='english')

    class Meta:
        permissions = [
            ("view_reports", "Can view reports"),
            ("view_closingstocks", "Can view closing stocks"),
        ]
    def __str__(self):
        return f'{self.first_name} {self.username}'

class Brand(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=50, blank=True, null=True)    

    class Meta: 
        verbose_name = _("brand")
        verbose_name_plural = _("brands")
        

    def __str__(self):
        return self.name

class Product(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    brand = models.ForeignKey(Brand, verbose_name=_("Brand Name"), on_delete=models.SET_NULL,null=True,blank=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    short_code = models.CharField(max_length=50, blank=True, null=True)
    hsn_code = models.CharField(max_length=50, blank=True, null=True)
    rate = models.FloatField(default=0.0, blank=True, null=True)
    tax_rate = models.FloatField(default=0.0, blank=True, null=True)
    tax_amount = models.FloatField(default=0.0, blank=True, null=True)
    margin = models.FloatField(default=0.0, blank=True, null=True)
    pr_margin = models.FloatField(default=0.0, blank=True, null=True)
    pr_rate = models.FloatField(default=0.0, blank=True, null=True)
    mrp = models.FloatField(default=0.0, blank=True, null=True)
    unit_contains = models.FloatField(default=0.0, blank=True, null=True)
    active = models.BooleanField(default=True)
    sort_order = models.IntegerField(default=0, blank=True, null=True)
    add = models.BooleanField(null=True,blank=True,default=True)
    crate_size = models.PositiveIntegerField(default=1)
    is_crate_based = models.BooleanField(default=False)
    min_stock_threshold = models.PositiveIntegerField(default=10)
    stock_quantity = models.FloatField(default=0.0)
    abc_classification = models.CharField(max_length=1, choices=[('A', 'A - High Value'), ('B', 'B - Medium Value'), ('C', 'C - Low Value')], default='C')
    reorder_point = models.FloatField(default=0.0)
    safety_stock = models.FloatField(default=0.0)
    lead_time_days = models.PositiveIntegerField(default=7)
    active = models.BooleanField(default=True)

    class Meta:
        verbose_name = _("product")
        verbose_name_plural = _("products")
        ordering = ['sort_order']

    def __str__(self):
        return self.name

class InventoryInsight(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    insight = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    

class ProductForecast(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    forecast_quantity = models.FloatField()
    generated_on = models.DateField(auto_now_add=True)


class BuyerClass(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=250, blank=True, null=True)

    class Meta:
        verbose_name = _("buyer class")
        verbose_name_plural = _("buyer clases")

    def __str__(self):
        return self.name

class BuyerClassMargin(models.Model):
    buyer_class = models.ForeignKey(
        BuyerClass, on_delete=models.CASCADE, related_name="buyer_class",null=True)
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="buyer_class_margin",null=True)
    margin = models.FloatField(default=0.0, blank=True, null=True)
    

    class Meta:
        verbose_name = _("buyer class margin")
        verbose_name_plural = _("buyer class margins")



class Route(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=50, blank=True, null=True)    

    class Meta:
        verbose_name = _("route")
        verbose_name_plural = _("routes")

    def __str__(self):
        return self.name



class Buyer(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    buyer_class = models.ForeignKey(
        BuyerClass, on_delete=models.SET_NULL, null=True, blank=True)
    route =  models.ForeignKey(
        Route, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    active = models.BooleanField(default=True)
    place = models.CharField(max_length=50, null=True, blank=True)
    contact_person = models.CharField(max_length=50, null=True, blank=True)
    phone_no = models.CharField(max_length=50, null=True, blank=True)
    gst_no = models.CharField(max_length=225, null=True, blank=True)
    credit_limit = models.FloatField(default=10000.0, null=True, blank=True)
    current_balance = models.FloatField(default=0.0)
    sort_order = models.IntegerField(default=0)
    buyer = models.BooleanField(default=True)

    class Meta:
        verbose_name = _("buyer")
        verbose_name_plural = _("buyers")
        ordering = ['sort_order']
        permissions = [
            ("change_creditlimit", "Can change credit limit"),
        ]

    def __str__(self):
        return self.name

class Deposit(models.Model):
    buyer = models.ForeignKey(
        Buyer, on_delete=models.SET_NULL, null=True, blank=True,related_name="buyer_deposit")
    amount =  models.FloatField(default=0.0, null=True, blank=True)
    date = models.DateTimeField(null=True,blank=True)
    reference =  models.CharField(max_length=225, null=True, blank=True)
    reference_file = models.FileField(upload_to="uploads/", null=True, blank=True)

    class Meta:
        verbose_name = _("deposit")
        verbose_name_plural = _("deposits")


class Asset(models.Model):
    buyer = models.ForeignKey(
        Buyer, on_delete=models.SET_NULL, null=True, blank=True,related_name="buyer_asset")
    asset_type =  models.CharField(max_length=2250, null=True, blank=True)
    serial_no =  models.CharField(max_length=2250, null=True, blank=True)
    capacity =  models.CharField(max_length=2250, null=True, blank=True)
    model =  models.CharField(max_length=2250, null=True, blank=True)

    class Meta:
        verbose_name = _("deposit")
        verbose_name_plural = _("deposits")

class AssetFile(models.Model):
    asset = models.ForeignKey(Asset, related_name="files", on_delete=models.CASCADE)
    asset_file = models.FileField(upload_to="uploads/")

class BuyerUser(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    buyer = models.ForeignKey(
        Buyer, on_delete=models.SET_NULL, null=True, blank=True)

class Suplier(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=50, blank=True, null=True)
    active = models.BooleanField(default=True)
    gst_no = models.CharField(max_length=225, null=True, blank=True)
    place = models.CharField(max_length=50, null=True, blank=True)
    contact_person = models.CharField(max_length=50, null=True, blank=True)
    phone_no = models.CharField(max_length=50, null=True, blank=True)
    current_balance = models.FloatField(default=0.0)
    sort_order = models.IntegerField(default=0)
    supplier = models.BooleanField(default=True)
    deposit =  models.ForeignKey(
        Deposit, on_delete=models.SET_NULL, null=True, blank=True)
    contact_email = models.EmailField(blank=True, null=True)

    class Meta:
        verbose_name = _("Supplier")
        verbose_name_plural = _("Suppliers")
        ordering = ['sort_order']

    def __str__(self):
        return self.name


# Purchase Order Status Choices
PO_STATUS_CHOICES = [
    ('draft', 'Draft'),
    ('pending_approval', 'Pending Approval'),
    ('approved', 'Approved'),
    ('sent_to_supplier', 'Sent to Supplier'),
    ('partially_received', 'Partially Received'),
    ('fully_received', 'Fully Received'),
    ('cancelled', 'Cancelled'),
]

# ABC Classification Choices
ABC_CLASSIFICATION_CHOICES = [
    ('A', 'A - High Value'),
    ('B', 'B - Medium Value'),
    ('C', 'C - Low Value'),
]

class SupplierPreference(models.Model):
    """Supplier preferences for products"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='supplier_preferences')
    supplier = models.ForeignKey(Suplier, on_delete=models.CASCADE, related_name='product_preferences')
    is_preferred = models.BooleanField(default=False)
    lead_time_days = models.PositiveIntegerField(default=7)
    minimum_order_quantity = models.FloatField(default=1.0)
    packaging_constraint = models.CharField(max_length=100, blank=True, null=True)  # e.g., "crates only"
    priority = models.PositiveIntegerField(default=1)  # 1 = highest priority

    class Meta:
        verbose_name = _("Supplier Preference")
        verbose_name_plural = _("Supplier Preferences")
        unique_together = ['product', 'supplier']
        ordering = ['priority']

    def __str__(self):
        return f"{self.supplier.name} - {self.product.name}"


class SupplierBrand(models.Model):
    """Many-to-many relationship between Suppliers and Brands"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    supplier = models.ForeignKey(Suplier, on_delete=models.CASCADE, related_name='supplier_brands')
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, related_name='brand_suppliers')
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Supplier Brand")
        verbose_name_plural = _("Supplier Brands")
        unique_together = ['supplier', 'brand']
        ordering = ['brand__name']

    def __str__(self):
        return f"{self.supplier.name} - {self.brand.name}"


class BuyerBrand(models.Model):
    """Many-to-many relationship between Buyers and Brands"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    buyer = models.ForeignKey(Buyer, on_delete=models.CASCADE, related_name='buyer_brands')
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, related_name='brand_buyers')
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Buyer Brand")
        verbose_name_plural = _("Buyer Brands")
        unique_together = ['buyer', 'brand']
        ordering = ['brand__name']

    def __str__(self):
        return f"{self.buyer.name} - {self.brand.name}"


class SalesForecast(models.Model):
    """Sales forecasting data with AI integration"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='forecasts')
    forecast_date = models.DateField()
    forecast_month = models.PositiveIntegerField()  # 1-12
    forecast_year = models.PositiveIntegerField()

    # Internal calculations
    last_year_same_month_sales = models.FloatField(default=0.0)
    last_3_months_avg_sales = models.FloatField(default=0.0)
    growth_factor = models.FloatField(default=0.1)  # 10% default
    internal_forecast_quantity = models.FloatField(default=0.0)

    # AI Integration
    ai_confirmed_quantity = models.FloatField(null=True, blank=True)
    ai_confidence_score = models.FloatField(null=True, blank=True)
    ai_analysis_comment = models.TextField(blank=True, null=True)
    ai_processed = models.BooleanField(default=False)
    ai_processed_at = models.DateTimeField(null=True, blank=True)

    # Final forecast
    final_forecast_quantity = models.FloatField(default=0.0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Sales Forecast")
        verbose_name_plural = _("Sales Forecasts")
        unique_together = ['product', 'forecast_month', 'forecast_year']
        ordering = ['-forecast_date']

class PurchaseOrder(models.Model):
    """Main Purchase Order model"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    supplier = models.ForeignKey(Suplier, on_delete=models.CASCADE, related_name='purchase_orders')
    po_number = models.CharField(max_length=50, unique=True)

    # Status and workflow
    status = models.CharField(max_length=20, choices=PO_STATUS_CHOICES, default='draft')
    auto_generated = models.BooleanField(default=False)

    # Dates
    created_date = models.DateTimeField(auto_now_add=True)
    expected_delivery_date = models.DateField(null=True, blank=True)
    approved_date = models.DateTimeField(null=True, blank=True)
    sent_date = models.DateTimeField(null=True, blank=True)

    # Financial
    total_amount = models.FloatField(default=0.0)
    tax_amount = models.FloatField(default=0.0)
    discount_amount = models.FloatField(default=0.0)
    net_amount = models.FloatField(default=0.0)

    # Additional info
    remarks = models.TextField(blank=True, null=True)
    terms_and_conditions = models.TextField(blank=True, null=True)

    # Approval workflow
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_pos')

    class Meta:
        verbose_name = _("Purchase Order")
        verbose_name_plural = _("Purchase Orders")
        ordering = ['-created_date']

    def __str__(self):
        return f"PO-{self.po_number} - {self.supplier.name}"

    def save(self, *args, **kwargs):
        if not self.po_number:
            # Generate PO number
            last_po = PurchaseOrder.objects.filter(
                user=self.user
            ).order_by('-id').first()

            if last_po and last_po.po_number.startswith('PO'):
                try:
                    last_number = int(last_po.po_number.split('-')[1])
                    self.po_number = f"PO-{last_number + 1:06d}"
                except (IndexError, ValueError):
                    self.po_number = f"PO-{timezone.now().strftime('%Y%m%d')}-001"
            else:
                self.po_number = f"PO-{timezone.now().strftime('%Y%m%d')}-001"

        super().save(*args, **kwargs)

class PurchaseOrderItem(models.Model):
    """Purchase Order line items"""
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='po_items')

    # Quantities
    ordered_quantity = models.FloatField()
    box_quantity = models.FloatField(default=0.0)
    pieces_quantity = models.FloatField(default=0.0)
    received_quantity = models.FloatField(default=0.0)
    pending_quantity = models.FloatField(default=0.0)

    # Pricing
    unit_rate = models.FloatField(default=0.0)
    line_total = models.FloatField(default=0.0)
    tax_rate = models.FloatField(default=0.0)
    tax_amount = models.FloatField(default=0.0)

    # Forecasting context
    forecast_quantity = models.FloatField(null=True, blank=True)
    current_stock = models.FloatField(null=True, blank=True)
    min_stock_threshold = models.FloatField(null=True, blank=True)
    reorder_reason = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = _("Purchase Order Item")
        verbose_name_plural = _("Purchase Order Items")
        ordering = ['product__sort_order']

    def save(self, *args, **kwargs):
        # Calculate line total using the correct formulas
        if self.product and self.product.is_crate_based:
            # For crate-based products: qty * crate_size * unit_contains * rate
            crate_size = self.product.crate_size if self.product.crate_size else 1
            unit_contains = self.product.unit_contains if self.product.unit_contains else 1
            self.line_total = self.box_quantity * crate_size * unit_contains * self.unit_rate
            self.ordered_quantity = self.box_quantity * crate_size * unit_contains
        else:
            # For regular products: (box * unit_contains * rate) + (pieces * rate)
            unit_contains = self.product.unit_contains if self.product and self.product.unit_contains else 1
            self.line_total = (self.box_quantity * unit_contains * self.unit_rate) + (self.pieces_quantity * self.unit_rate)
            self.ordered_quantity = (self.box_quantity * unit_contains) + self.pieces_quantity

        self.tax_amount = self.line_total * (self.tax_rate / 100)
        self.pending_quantity = self.ordered_quantity - self.received_quantity
        super().save(*args, **kwargs)

class PurchaseOrderDraft(models.Model):
    """Draft purchase orders for review before approval"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    supplier = models.ForeignKey(Suplier, on_delete=models.CASCADE, related_name='draft_orders')
    created_on = models.DateTimeField(auto_now_add=True)
    status = models.CharField(default='draft', max_length=20)
    auto_generated = models.BooleanField(default=True)
    forecast_based = models.BooleanField(default=True)
    total_estimated_amount = models.FloatField(default=0.0)

    class Meta:
        verbose_name = _("Purchase Order Draft")
        verbose_name_plural = _("Purchase Order Drafts")
        ordering = ['-created_on']

class PurchaseOrderDraftItem(models.Model):
    """Draft purchase order items"""
    draft = models.ForeignKey(PurchaseOrderDraft, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.FloatField()
    estimated_rate = models.FloatField(default=0.0)
    estimated_total = models.FloatField(default=0.0)

    # Context information
    forecast_quantity = models.FloatField(null=True, blank=True)
    current_stock = models.FloatField()
    min_stock_threshold = models.FloatField()
    shortage_quantity = models.FloatField(default=0.0)
    reorder_reason = models.TextField(blank=True, null=True)
    auto_generated = models.BooleanField(default=True)

    class Meta:
        verbose_name = _("Purchase Order Draft Item")
        verbose_name_plural = _("Purchase Order Draft Items")
        ordering = ['product__sort_order']

class SalesOrder(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    buyer = models.ForeignKey(
        Buyer, on_delete=models.SET_NULL, null=True, blank=True)
    previous_balance = models.FloatField(default=0.0)
    bill_amount = models.FloatField(default=0.0)
    receivable_amount = models.FloatField(default=0.0)
    received_amount = models.FloatField(default=0.0)
    current_balance = models.FloatField(default=0.0)
    date = models.DateField(null=True,blank=True)

    class Meta:
        verbose_name = _("Sales Invoice")
        verbose_name_plural = _("Sales Invoices")
        ordering=['id']
        
    def __str__(self):
        return str(self.id)+"-->"+str(self.date)


class SalesOrderItem(models.Model):
    sales_order = models.ForeignKey(
        SalesOrder, on_delete=models.CASCADE, related_name="sales_order_items")
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="sales_order_product",null=True)
    weights = models.CharField(max_length=250,null=True,blank=True)
    rate = models.FloatField(default=0.0)
    tax_amount = models.FloatField(default=0.0)
    weight = models.FloatField(default=0.0)
    no = models.FloatField(default=0.0)
    line_total = models.FloatField(default=0.0)

    class Meta:
        verbose_name = _("SalesInvoiceItem")
        verbose_name_plural = _("SalesInvoiceItems")
        ordering = ['product__sort_order']


class SalesInvoice(models.Model):
    ORDER_STATUS = [
        ("order", "Order Placed"),
        ("billed", "Billed"),
        ("cancelled", "Cancelled"),
    ]

    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    sales_person = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,related_name="sales_person")
    buyer = models.ForeignKey(
        Buyer, on_delete=models.SET_NULL, null=True, blank=True)
    previous_balance = models.FloatField(default=0.0)
    bill_amount = models.FloatField(default=0.0)
    receivable_amount = models.FloatField(default=0.0)
    received_amount = models.FloatField(default=0.0)
    current_balance = models.FloatField(default=0.0)
    discount = models.IntegerField(null=True,blank=True)
    discount_amount = models.FloatField(default=0.0)
    # Rounding fields
    rounding_enabled = models.BooleanField(default=True)
    rounding_adjustment = models.FloatField(default=0.0)
    gross_total = models.FloatField(default=0.0)  # Subtotal + Tax before rounding
    date = models.DateField(null=True,blank=True)
    delivery_challan = models.BooleanField(default=False)
    remarks = models.TextField(null=True,blank=True)
    headerdata = models.JSONField(null=True,blank=True)
    metadata = models.JSONField(null=True,blank=True)
    invoice_status = models.CharField(_("Invoice Status"), max_length=20, choices=ORDER_STATUS, default="billed")
    billed_by = models.CharField(max_length=100, null=True, blank=True, help_text="Name of person who billed the invoice")
    delivery_by = models.CharField(max_length=100, null=True, blank=True, help_text="Name of person who delivered the goods")
    collected_by = models.CharField(max_length=100, null=True, blank=True, help_text="Name of person who collected payment")
    
    class Meta:
        verbose_name = _("Sales Invoice")
        verbose_name_plural = _("Sales Invoices")
        ordering=['id']
        permissions = [
            ("view_tally", "Can view tally"),
            ("override_credit_limit", "Can override credit limit"),
            ("view_sales_payment", "Can view sales payment")
        ]
        
    def __str__(self):
        return str(self.id)+"-->"+str(self.date)
        
    @cached_property
    def total_tax(self):
        return round(sum(item.line_tax for item in self.sales_invoice_items.all()))


class SalesInvoiceItemManager(models.Manager):
    def update(self, **kwargs):
        items = list(self.get_queryset())  # Fetch affected items
        result = super().update(**kwargs)  # Perform the update

        # Manually trigger post_save for each updated item
        for item in items:
            for field, value in kwargs.items():
                setattr(item, field, value)
            pre_save.send(sender=SalesInvoiceItem, instance=item, created=False)
            post_save.send(sender=SalesInvoiceItem, instance=item, created=False)

        return result
    

class SalesInvoiceItem(models.Model):
    sales_invoice = models.ForeignKey(
        SalesInvoice, on_delete=models.CASCADE, related_name="sales_invoice_items")
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="sales_product",null=True)
    weights = models.CharField(max_length=250,null=True,blank=True)
    mrp = models.FloatField(default=0.0)
    rate = models.FloatField(default=0.0)
    ordered_qty = models.FloatField(default=0.0)
    confirmed_qty = models.FloatField(default=0.0)
    ordered_pcs = models.FloatField(default=0.0)
    confirmed_pcs = models.FloatField(default=0.0)
    weight = models.FloatField(_("Pcs"),default=0.0)
    no = models.FloatField(_("quantity"),default=0.0)
    discount = models.IntegerField(null=True,blank=True)
    discount_amount = models.FloatField(default=0.0)
    line_total = models.FloatField(default=0.0)
    remarks = models.TextField(null=True,blank=True)
    metadata = models.JSONField(null=True,blank=True)

    objects = SalesInvoiceItemManager()  # Assign custom manager

    class Meta:
        verbose_name = _("SalesInvoiceItem")
        verbose_name_plural = _("SalesInvoiceItems")
        ordering = ['product__sort_order']

    @cached_property
    def line_tax(self):
        if self.product and self.line_total:
            return round(self.line_total) * (self.product.tax_rate / 100)
        return 0.0


class InvoiceImage(models.Model):
    """Model to store images associated with sales invoices"""
    sales_invoice = models.ForeignKey(
        SalesInvoice, on_delete=models.CASCADE, related_name="invoice_images")
    image = models.ImageField(upload_to='invoice_images/')
    filename = models.CharField(max_length=255)
    file_size = models.IntegerField(help_text="File size in bytes")
    file_type = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = _("Invoice Image")
        verbose_name_plural = _("Invoice Images")
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"Invoice {self.sales_invoice.id} - {self.filename}"

    @property
    def file_size_mb(self):
        """Return file size in MB"""
        return round(self.file_size / (1024 * 1024), 2)
    
    


PAYMENT_TYPE = (
    ('debit','Debit'),
    ('credit','Credit')
)




class LineAccounts(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    suplier = models.ForeignKey(
        Suplier, on_delete=models.SET_NULL, null=True, blank=True)
    bill_date = models.DateField(null=True,blank=True)
    bill_amount = models.FloatField(default=0.0)
    no_3_amount = models.FloatField(default=0.0)
    paid_amount = models.FloatField(default=0.0)

    class Meta:
        verbose_name = _("Line Account")
        verbose_name_plural = _("Line Accounts")

    def __str__(self):
        return str(self.id)+"-->"+str(self.bill_date)

class PurchaseInvoice(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    suplier = models.ForeignKey(
        Suplier, on_delete=models.SET_NULL, null=True, blank=True)
    previous_balance = models.FloatField(default=0.0)
    date = models.DateField(null=True,blank=True)
    bill_amount = models.FloatField(default=0.0)
    discount = models.IntegerField(null=True,blank=True)
    discount_amount = models.FloatField(default=0.0)
    receivable_amount = models.FloatField(default=0.0)
    received_amount = models.FloatField(default=0.0)
    current_balance = models.FloatField(default=0.0)
    date = models.DateField(null=True,blank=True)
    line_settlement_date = models.DateField(auto_now_add=True,null=True,blank=True)
    remarks = models.TextField(null=True,blank=True)
    headerdata = models.JSONField(null=True,blank=True)
    metadata = models.JSONField(null=True,blank=True)

    class Meta:
        verbose_name = _("Purchase Invoice")
        verbose_name_plural = _("Purchase Invoices")
        permissions = [
            ("view_purchase_payment", "Can view purchase payment")
        ]

    def __str__(self):
        return str(self.id)+"-->"+str(self.date)


class PurchaseInvoiceItemManager(models.Manager):
    def update(self, **kwargs):
        items = list(self.get_queryset())  # Fetch affected items
        result = super().update(**kwargs)  # Perform the update

        # Manually trigger post_save for each updated item
        for item in items:
            for field, value in kwargs.items():
                setattr(item, field, value)
            pre_save.send(sender=PurchaseInvoiceItem, instance=item, created=False)
            post_save.send(sender=PurchaseInvoiceItem, instance=item, created=False)

        return result

class PurchaseInvoiceItem(models.Model):
    purchase_invoice = models.ForeignKey(
        PurchaseInvoice, on_delete=models.CASCADE, related_name="purchase_invoice_items",null=True)
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="purchase_product",null=True)
    weights = models.CharField(max_length=250,null=True,blank=True)
    mrp = models.FloatField(default=0.0,null=True,blank=True)
    rate = models.FloatField(default=0.0)
    weight = models.FloatField(default=0.0,null=True,blank=True)
    no = models.FloatField(default=0.0)
    line_total = models.FloatField(default=0.0)
    discount = models.IntegerField(null=True,blank=True)
    discount_amount = models.FloatField(default=0.0)
    remarks = models.TextField(null=True,blank=True)
    metadata = models.JSONField(null=True,blank=True)

    objects = PurchaseInvoiceItemManager()  # Assign custom manager    

    class Meta:
        verbose_name = _("PurchaseInvoiceItem")
        verbose_name_plural = _("PurchaseInvoiceItems")


class Ledger(models.Model):
    invoice = models.ForeignKey(SalesInvoice, on_delete=models.CASCADE,related_name="invoice_payment_entry",null=True,blank=True)
    purchase_invoice = models.ForeignKey(PurchaseInvoice, on_delete=models.CASCADE,related_name="purchase_invoice_payment_entry",null=True,blank=True)
    buyer = models.ForeignKey(Buyer, on_delete=models.SET_NULL,null=True,blank=True,related_name="buyer_payment_entry")
    supplier = models.ForeignKey(Suplier, on_delete=models.SET_NULL,null=True,blank=True,related_name="buyer_payment_entry")
    mode_of_payment = models.CharField(_("mode of payment"), max_length=250,null=True,blank=True)
    discount_amount = models.FloatField(default=0.0)
    amount = models.FloatField(default=0.0)
    metadata = models.JSONField(null=True,blank=True)
    remarks = models.CharField( max_length=350,blank=True,null=True)
    entry_type = models.CharField( max_length=150,choices=PAYMENT_TYPE,null=True,blank=True)
    closing_amount = models.FloatField(default=0.0,blank=True,null=True)
    ledger_file = models.FileField(upload_to='ledger',null=True,blank=True) 
    created_at = models.DateField(auto_now=False, auto_now_add=True)


    class Meta:
        verbose_name = _("ledger")
        verbose_name_plural = _("ledgers")
        ordering=['-id']

    def save(self, *args, **kwargs):
        is_update = self.pk is not None  # Check if this is an update
        if self.buyer is not None:
            ledger = Ledger.objects.filter(buyer=self.buyer)

        if self.supplier is not None:
            ledger = Ledger.objects.filter(supplier=self.supplier)
        debit = ledger.filter(entry_type='debit').aggregate(Sum('amount'))['amount__sum'] or 0.0
        credit = ledger.filter(entry_type='credit').aggregate(Sum('amount'))['amount__sum'] or 0.0

        if self.entry_type == 'debit':
            debit = debit + self.amount + float(self.discount_amount)
        if self.entry_type == 'credit':
            credit = credit + self.amount + float(self.discount_amount)
        if is_update:
            last_ledger = ledger.last()
            Ledger.objects.filter(pk=last_ledger.pk).update(closing_amount= float(debit) - float(credit))
        else:
             self.closing_amount = float(debit) - float(credit)
        if self.buyer is not None:
            buyer = self.buyer
            buyer.current_balance = self.closing_amount
            buyer.save()
        if self.supplier is not None:
            supplier = self.supplier
            supplier.current_balance = self.closing_amount
            supplier.save()
        super(Ledger,self).save(*args, **kwargs)
        if self.buyer is not None:
            if is_valid_phone_number(self.buyer.phone_no) and self.buyer.user.send_wp_message:
                sendTransaction(self)
        # if self.supplier is not None:
        #     if is_valid_phone_number(self.supplier.phone_no) and self.supplier.user.send_wp_message:
        #         sendTransaction(self)

PARTY_TYPE = (
    ('sender','Sender'),
    ('supplier','Supplier'),
    ('receiver','Receiver')
)

class Party(models.Model):
    name = models.CharField(_("Party Name"), max_length=250)
    party_type = models.CharField(_("Party Type"), max_length=150,choices=PARTY_TYPE)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = _("party")
        verbose_name_plural = _("parties")

    def __str__(self):
        return self.name

class Item(models.Model):
    code = models.CharField(_("Item Code"), max_length=250)
    name = models.CharField(_("Item Name"), max_length=250)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        verbose_name = _("item")
        verbose_name_plural = _("items")

    def __str__(self):
        return self.name



class Rate(models.Model):
    box = models.FloatField(_("box rate"),default=0.0)
    pcs = models.FloatField(_("pcs rate"),default=0.0)
    crate = models.FloatField(_("crate rate"),default=0.0)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = _("rate")
        verbose_name_plural = _("rates")

    def __str__(self):
        return self.name


class Order(models.Model):
    reciever = models.ForeignKey(Party, verbose_name=_("reciever name"), on_delete=models.SET_NULL, null=True, blank=True)
    date = models.DateField(_("order date"), auto_now=False, auto_now_add=False)
    box = models.FloatField(_("box rate"),default=0.0)
    pcs = models.FloatField(_("pcs rate"),default=0.0)
    crate = models.FloatField(_("crate rate"),default=0.0)
    amount = models.FloatField(_("amount"),default=0.0)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)


    class Meta:
        verbose_name = _("order")
        verbose_name_plural = _("orders")

    def __str__(self):
        return self.supplier.name

  
class OrderItem(models.Model):
    order = models.ForeignKey(Order, verbose_name=_("order"), on_delete=models.SET_NULL,related_name='order_items', null=True, blank=True)
    supplier = models.ForeignKey(Party, verbose_name=_("supplier name"), on_delete=models.SET_NULL, null=True, blank=True)
    item = models.ForeignKey(Item, verbose_name=_("item name"), on_delete=models.SET_NULL, null=True, blank=True)
    box = models.FloatField(_("box rate"),default=0.0)
    pcs = models.FloatField(_("pcs rate"),default=0.0)
    crate = models.FloatField(_("crate rate"),default=0.0)
    amount = models.FloatField(_("amount"),default=0.0)

    class Meta:
        verbose_name = _("order item")
        verbose_name_plural = _("order items")

class Invoice(models.Model):
    sender = models.ForeignKey(Party, on_delete=models.SET_NULL, related_name='sender',null=True,blank=True)
    reciever = models.ForeignKey(Party, on_delete=models.SET_NULL, related_name='reciever',null=True,blank=True)
    date = models.DateField()
    rate = models.FloatField(default=0)
    qty = models.IntegerField()
    amount = models.FloatField(default=0)
    balance = models.FloatField(default=0)
    commission_percent = models.FloatField(default=0,null=True,blank=True)
    commission = models.FloatField(default=0)
    rent = models.FloatField(default=0)
    wages = models.FloatField(default=0)
    total = models.FloatField(default=0)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)


class InvoiceItem(models.Model):
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='invoice_items',null=True,blank=True)
    item = models.ForeignKey(Item, on_delete=models.CASCADE, related_name='item',null=True,blank=True)
    supplier = models.ForeignKey(Party, on_delete=models.CASCADE, related_name='supplier',null=True,blank=True)
    rate = models.FloatField(default=0,null=True,blank=True)
    qty = models.IntegerField(null=True,blank=True)
    amount = models.FloatField(default=0,null=True,blank=True)

class ExpenseCategoryQuerySet(models.QuerySet):
    def active(self):
        return self.filter(is_active=True)

    def with_active_subcategory(self):
        return self.prefetch_related(
            Prefetch(
                'subcategories',
                queryset=SubCategory.objects.filter(is_active=True)
            )
        )

    def get_active_items(self):
        return self.active().with_active_subcategory()

class ExpenseCategoryManager(models.Manager):
    def get_queryset(self):
        return ExpenseCategoryQuerySet(self.model, using=self._db)

    def active(self):
        return self.get_queryset().active()

    def with_active_subcategory(self):
        return self.get_queryset().with_active_subcategory()

    def get_active_items(self):
        return self.get_queryset().get_active_items()

class ExpenseCategory(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)

    objects = ExpenseCategoryManager()

    def __str__(self):
        return self.name

class SubCategory(models.Model):
    category = models.ForeignKey(ExpenseCategory, related_name='subcategories', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ('category', 'name')

    def __str__(self):
        return self.name

class Expense(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    category = models.ForeignKey(ExpenseCategory, related_name='expenses_category', on_delete=models.SET_NULL,null=True,blank=True)
    subcategory = models.ForeignKey(SubCategory, related_name='expenses_subcategory', on_delete=models.SET_NULL,null=True,blank=True)
    mode_of_payment = models.CharField(_("mode of payment"), max_length=250,null=True,blank=True)
    amount = models.FloatField(default=0)
    notes = models.TextField(null=True, blank=True)
    expense_file = models.FileField(upload_to='expense',null=True,blank=True) 
    created_at = models.DateField(auto_now_add=True,null=True,blank=True)
    updated_at = models.DateField(auto_now=True,null=True,blank=True)

    # def __str__(self):
    #     return f"{self.category.name} - {self.subcategory.name}: {self.amount}"


class Checklist(models.Model):
    """
    Checklist model for industry-specific task management
    """
    CHECKLIST_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('overdue', 'Overdue'),
    ]

    title = models.CharField(_("Checklist Title"), max_length=250)
    description = models.TextField(_("Description"), null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="checklists")
    route = models.ForeignKey(Route, on_delete=models.SET_NULL, null=True, blank=True, related_name="checklists")
    buyer = models.ForeignKey(Buyer, on_delete=models.SET_NULL, null=True, blank=True, related_name="checklists")
    status = models.CharField(max_length=20, choices=CHECKLIST_STATUS_CHOICES, default='pending')
    priority = models.CharField(max_length=20, choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='medium')
    due_date = models.DateTimeField(_("Due Date"), null=True, blank=True)
    start_time = models.DateTimeField(_("Start Time"), null=True, blank=True)
    submit_time = models.DateTimeField(_("Submit Time"), null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("checklist")
        verbose_name_plural = _("checklists")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.status}"

    @property
    def completion_percentage(self):
        """Calculate completion percentage based on checklist items"""
        total_items = self.items.count()
        if total_items == 0:
            return 0
        completed_items = self.items.filter(is_completed=True).count()
        return round((completed_items / total_items) * 100, 2)

    @property
    def is_overdue(self):
        """Check if checklist is overdue"""
        if self.due_date and self.status != 'completed':
            from django.utils import timezone
            return timezone.now() > self.due_date
        return False


class ChecklistItem(models.Model):
    """
    Individual items within a checklist
    """
    checklist = models.ForeignKey(Checklist, on_delete=models.CASCADE, related_name="items")
    title = models.CharField(_("Item Title"), max_length=250)
    description = models.TextField(_("Description"), null=True, blank=True)
    is_mandatory = models.BooleanField(_("Is Mandatory"), default=False)
    is_completed = models.BooleanField(_("Is Completed"), default=False)
    image = models.ImageField(_("Item Image"), upload_to="checklist_items/", null=True, blank=True)
    notes = models.TextField(_("Notes"), null=True, blank=True)
    completed_at = models.DateTimeField(_("Completed At"), null=True, blank=True)
    sort_order = models.IntegerField(_("Sort Order"), default=0)

    class Meta:
        verbose_name = _("checklist item")
        verbose_name_plural = _("checklist items")
        ordering = ['sort_order', 'id']

    def __str__(self):
        return f"{self.checklist.title} - {self.title}"


class ChecklistTemplate(models.Model):
    """
    Template for creating standardized checklists
    """
    name = models.CharField(_("Template Name"), max_length=250)
    description = models.TextField(_("Description"), null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="checklist_templates")
    is_active = models.BooleanField(_("Is Active"), default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("checklist template")
        verbose_name_plural = _("checklist templates")
        ordering = ['name']

    def __str__(self):
        return self.name


class ChecklistTemplateItem(models.Model):
    """
    Template items for checklist templates
    """
    template = models.ForeignKey(ChecklistTemplate, on_delete=models.CASCADE, related_name="template_items")
    title = models.CharField(_("Item Title"), max_length=250)
    description = models.TextField(_("Description"), null=True, blank=True)
    is_mandatory = models.BooleanField(_("Is Mandatory"), default=False)
    sort_order = models.IntegerField(_("Sort Order"), default=0)

    class Meta:
        verbose_name = _("checklist template item")
        verbose_name_plural = _("checklist template items")
        ordering = ['sort_order', 'id']

    def __str__(self):
        return f"{self.template.name} - {self.title}"


class RouteSchedule(models.Model):
    """
    Model to track route schedules and billing patterns
    """
    WEEKDAY_CHOICES = [
        ('monday', 'Monday'),
        ('tuesday', 'Tuesday'),
        ('wednesday', 'Wednesday'),
        ('thursday', 'Thursday'),
        ('friday', 'Friday'),
        ('saturday', 'Saturday'),
        ('sunday', 'Sunday'),
    ]

    route = models.ForeignKey(Route, on_delete=models.CASCADE, related_name="schedules")
    weekday = models.CharField(max_length=10, choices=WEEKDAY_CHOICES)
    is_active = models.BooleanField(default=True)
    expected_billing_time = models.TimeField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("route schedule")
        verbose_name_plural = _("route schedules")
        unique_together = ['route', 'weekday']

    def __str__(self):
        return f"{self.route.name} - {self.get_weekday_display()}"


class ShopFreezerPhoto(models.Model):
    """
    Model to store freezer photos for shops/buyers
    """
    buyer = models.ForeignKey(Buyer, on_delete=models.CASCADE, related_name="freezer_photos")
    route = models.ForeignKey(Route, on_delete=models.CASCADE, related_name="freezer_photos")
    photo = models.ImageField(upload_to="freezer_photos/", null=False, blank=False)
    date_taken = models.DateField(auto_now_add=True)
    time_taken = models.TimeField(auto_now_add=True)
    notes = models.TextField(null=True, blank=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    location_latitude = models.FloatField(null=True, blank=True)
    location_longitude = models.FloatField(null=True, blank=True)

    class Meta:
        verbose_name = _("shop freezer photo")
        verbose_name_plural = _("shop freezer photos")
        ordering = ['-date_taken', '-time_taken']

    def __str__(self):
        return f"{self.buyer.name} - {self.date_taken}"


class RouteBillingSummary(models.Model):
    """
    Model to track daily billing summary by route
    """
    route = models.ForeignKey(Route, on_delete=models.CASCADE, related_name="billing_summaries")
    date = models.DateField()
    total_shops = models.IntegerField(default=0)
    billed_shops = models.IntegerField(default=0)
    unbilled_shops = models.IntegerField(default=0)
    total_amount = models.FloatField(default=0.0)
    completion_percentage = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("route billing summary")
        verbose_name_plural = _("route billing summaries")
        unique_together = ['route', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.route.name} - {self.date} ({self.completion_percentage}%)"

    @property
    def is_complete(self):
        return self.completion_percentage >= 100.0

    def calculate_summary(self):
        """Calculate billing summary for the route and date"""
        from django.db.models import Sum, Count

        # Get all buyers in this route
        route_buyers = Buyer.objects.filter(route=self.route, active=True)
        self.total_shops = route_buyers.count()

        # Get invoices for this route and date
        billed_buyers = SalesInvoice.objects.filter(
            buyer__route=self.route,
            date=self.date
        ).values_list('buyer_id', flat=True).distinct()

        self.billed_shops = len(billed_buyers)
        self.unbilled_shops = self.total_shops - self.billed_shops

        # Calculate total amount
        total_amount = SalesInvoice.objects.filter(
            buyer__route=self.route,
            date=self.date
        ).aggregate(total=Sum('bill_amount'))['total'] or 0.0

        self.total_amount = total_amount

        # Calculate completion percentage
        if self.total_shops > 0:
            self.completion_percentage = (self.billed_shops / self.total_shops) * 100
        else:
            self.completion_percentage = 0.0

        self.save()
        return self