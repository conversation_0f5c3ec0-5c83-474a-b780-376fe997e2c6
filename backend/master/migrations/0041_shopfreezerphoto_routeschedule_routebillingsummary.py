# Generated by Django 4.2.18 on 2025-06-17 07:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0040_checklist_checklisttemplate_checklisttemplateitem_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShopFreezerPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('photo', models.ImageField(upload_to='freezer_photos/')),
                ('date_taken', models.DateField(auto_now_add=True)),
                ('time_taken', models.TimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('location_latitude', models.FloatField(blank=True, null=True)),
                ('location_longitude', models.FloatField(blank=True, null=True)),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='freezer_photos', to='master.buyer')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='freezer_photos', to='master.route')),
                ('uploaded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'shop freezer photo',
                'verbose_name_plural': 'shop freezer photos',
                'ordering': ['-date_taken', '-time_taken'],
            },
        ),
        migrations.CreateModel(
            name='RouteSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weekday', models.CharField(choices=[('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday'), ('saturday', 'Saturday'), ('sunday', 'Sunday')], max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('expected_billing_time', models.TimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='master.route')),
            ],
            options={
                'verbose_name': 'route schedule',
                'verbose_name_plural': 'route schedules',
                'unique_together': {('route', 'weekday')},
            },
        ),
        migrations.CreateModel(
            name='RouteBillingSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('total_shops', models.IntegerField(default=0)),
                ('billed_shops', models.IntegerField(default=0)),
                ('unbilled_shops', models.IntegerField(default=0)),
                ('total_amount', models.FloatField(default=0.0)),
                ('completion_percentage', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='billing_summaries', to='master.route')),
            ],
            options={
                'verbose_name': 'route billing summary',
                'verbose_name_plural': 'route billing summaries',
                'ordering': ['-date'],
                'unique_together': {('route', 'date')},
            },
        ),
    ]
