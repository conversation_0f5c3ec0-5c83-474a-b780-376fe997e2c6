# Generated by Django 4.2.18 on 2025-02-25 08:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0010_alter_brand_options_alter_purchaseinvoice_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Deposit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.FloatField(blank=True, default=0.0, null=True)),
                ('date', models.DateField(blank=True, null=True)),
                ('reference', models.CharField(blank=True, max_length=225, null=True)),
            ],
            options={
                'verbose_name': 'deposit',
                'verbose_name_plural': 'deposits',
            },
        ),
        migrations.AddField(
            model_name='buyer',
            name='deposit',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.deposit'),
        ),
        migrations.AddField(
            model_name='suplier',
            name='deposit',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.deposit'),
        ),
    ]
