# Generated by Django 4.2.18 on 2025-02-18 05:21

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('role', models.CharField(blank=True, choices=[('superadmin', 'Super Admin'), ('admin', 'Admin'), ('accountant', 'Accountant'), ('manager', 'Manager'), ('customer', 'Customer'), ('buyer', 'Buyer')], default='customer', max_length=25, null=True)),
                ('gst_no', models.CharField(blank=True, max_length=225, null=True)),
                ('fssai_no', models.CharField(blank=True, max_length=225, null=True)),
                ('contact_no_left', models.CharField(blank=True, max_length=225, null=True)),
                ('contact_no_right', models.CharField(blank=True, max_length=225, null=True)),
                ('address', models.CharField(blank=True, max_length=225, null=True)),
                ('company_name', models.CharField(blank=True, max_length=225, null=True)),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='logo/')),
                ('driver_name', models.CharField(blank=True, max_length=225, null=True)),
                ('vehicle_no', models.CharField(blank=True, max_length=225, null=True)),
                ('device_token', models.CharField(blank=True, max_length=225, null=True)),
                ('send_wp_message', models.BooleanField(default=False)),
                ('wp_id', models.CharField(blank=True, max_length=225, null=True)),
                ('wp_token', models.CharField(blank=True, max_length=2250, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, null=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Company User')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Buyer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('active', models.BooleanField(default=True)),
                ('place', models.CharField(blank=True, max_length=50, null=True)),
                ('contact_person', models.CharField(blank=True, max_length=50, null=True)),
                ('phone_no', models.CharField(blank=True, max_length=50, null=True)),
                ('gst_no', models.CharField(blank=True, max_length=225, null=True)),
                ('current_balance', models.FloatField(default=0.0)),
                ('sort_order', models.IntegerField(default=0)),
                ('buyer', models.BooleanField(default=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'buyer',
                'verbose_name_plural': 'buyers',
                'ordering': ['sort_order'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('rate', models.FloatField(default=0)),
                ('qty', models.IntegerField()),
                ('amount', models.FloatField(default=0)),
                ('balance', models.FloatField(default=0)),
                ('commission_percent', models.FloatField(blank=True, default=0, null=True)),
                ('commission', models.FloatField(default=0)),
                ('rent', models.FloatField(default=0)),
                ('wages', models.FloatField(default=0)),
                ('total', models.FloatField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=250, verbose_name='Item Code')),
                ('name', models.CharField(max_length=250, verbose_name='Item Name')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'item',
                'verbose_name_plural': 'items',
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='order date')),
                ('box', models.FloatField(default=0.0, verbose_name='box rate')),
                ('pcs', models.FloatField(default=0.0, verbose_name='pcs rate')),
                ('crate', models.FloatField(default=0.0, verbose_name='crate rate')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
            ],
            options={
                'verbose_name': 'order',
                'verbose_name_plural': 'orders',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('short_code', models.CharField(blank=True, max_length=50, null=True)),
                ('hsn_code', models.CharField(blank=True, max_length=50, null=True)),
                ('rate', models.FloatField(blank=True, default=0.0, null=True)),
                ('tax_rate', models.FloatField(blank=True, default=0.0, null=True)),
                ('tax_amount', models.FloatField(blank=True, default=0.0, null=True)),
                ('margin', models.FloatField(blank=True, default=0.0, null=True)),
                ('pr_margin', models.FloatField(blank=True, default=0.0, null=True)),
                ('pr_rate', models.FloatField(blank=True, default=0.0, null=True)),
                ('mrp', models.FloatField(blank=True, default=0.0, null=True)),
                ('unit_contains', models.FloatField(blank=True, default=0.0, null=True)),
                ('active', models.BooleanField(default=True)),
                ('sort_order', models.IntegerField(blank=True, default=0, null=True)),
                ('add', models.BooleanField(blank=True, default=True, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'product',
                'verbose_name_plural': 'products',
                'ordering': ['sort_order'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_balance', models.FloatField(default=0.0)),
                ('bill_amount', models.FloatField(default=0.0)),
                ('discount', models.IntegerField(blank=True, null=True)),
                ('discount_amount', models.FloatField(default=0.0)),
                ('receivable_amount', models.FloatField(default=0.0)),
                ('received_amount', models.FloatField(default=0.0)),
                ('current_balance', models.FloatField(default=0.0)),
                ('date', models.DateField(blank=True, null=True)),
                ('line_settlement_date', models.DateField(auto_now_add=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('headerdata', models.JSONField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Purchase Invoice',
                'verbose_name_plural': 'Purchase Invoices',
            },
        ),
        migrations.CreateModel(
            name='SalesInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_balance', models.FloatField(default=0.0)),
                ('bill_amount', models.FloatField(default=0.0)),
                ('receivable_amount', models.FloatField(default=0.0)),
                ('received_amount', models.FloatField(default=0.0)),
                ('current_balance', models.FloatField(default=0.0)),
                ('discount', models.IntegerField(blank=True, null=True)),
                ('discount_amount', models.FloatField(default=0.0)),
                ('date', models.DateField(blank=True, null=True)),
                ('delivery_challan', models.BooleanField(default=False)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('headerdata', models.JSONField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('buyer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.buyer')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sales Invoice',
                'verbose_name_plural': 'Sales Invoices',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='SalesOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_balance', models.FloatField(default=0.0)),
                ('bill_amount', models.FloatField(default=0.0)),
                ('receivable_amount', models.FloatField(default=0.0)),
                ('received_amount', models.FloatField(default=0.0)),
                ('current_balance', models.FloatField(default=0.0)),
                ('date', models.DateField(blank=True, null=True)),
                ('buyer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.buyer')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sales Invoice',
                'verbose_name_plural': 'Sales Invoices',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='Suplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('active', models.BooleanField(default=True)),
                ('gst_no', models.CharField(blank=True, max_length=225, null=True)),
                ('place', models.CharField(blank=True, max_length=50, null=True)),
                ('contact_person', models.CharField(blank=True, max_length=50, null=True)),
                ('phone_no', models.CharField(blank=True, max_length=50, null=True)),
                ('current_balance', models.FloatField(default=0.0)),
                ('sort_order', models.IntegerField(default=0)),
                ('supplier', models.BooleanField(default=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Supplier',
                'verbose_name_plural': 'Suppliers',
                'ordering': ['sort_order'],
            },
        ),
        migrations.CreateModel(
            name='SubCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='master.expensecategory')),
            ],
            options={
                'unique_together': {('category', 'name')},
            },
        ),
        migrations.CreateModel(
            name='SalesOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weights', models.CharField(blank=True, max_length=250, null=True)),
                ('rate', models.FloatField(default=0.0)),
                ('tax_amount', models.FloatField(default=0.0)),
                ('weight', models.FloatField(default=0.0)),
                ('no', models.FloatField(default=0.0)),
                ('line_total', models.FloatField(default=0.0)),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sales_order_product', to='master.product')),
                ('sales_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_order_items', to='master.salesorder')),
            ],
            options={
                'verbose_name': 'SalesInvoiceItem',
                'verbose_name_plural': 'SalesInvoiceItems',
                'ordering': ['product__sort_order'],
            },
        ),
        migrations.CreateModel(
            name='SalesInvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weights', models.CharField(blank=True, max_length=250, null=True)),
                ('mrp', models.FloatField(default=0.0)),
                ('rate', models.FloatField(default=0.0)),
                ('weight', models.FloatField(default=0.0)),
                ('no', models.FloatField(default=0.0)),
                ('discount', models.IntegerField(blank=True, null=True)),
                ('discount_amount', models.FloatField(default=0.0)),
                ('line_total', models.FloatField(default=0.0)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sales_product', to='master.product')),
                ('sales_invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_invoice_items', to='master.salesinvoice')),
            ],
            options={
                'verbose_name': 'SalesInvoiceItem',
                'verbose_name_plural': 'SalesInvoiceItems',
                'ordering': ['product__sort_order'],
            },
        ),
        migrations.CreateModel(
            name='Rate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('box', models.FloatField(default=0.0, verbose_name='box rate')),
                ('pcs', models.FloatField(default=0.0, verbose_name='pcs rate')),
                ('crate', models.FloatField(default=0.0, verbose_name='crate rate')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'rate',
                'verbose_name_plural': 'rates',
            },
        ),
        migrations.CreateModel(
            name='PurchaseInvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weights', models.CharField(blank=True, max_length=250, null=True)),
                ('rate', models.FloatField(default=0.0)),
                ('weight', models.FloatField(default=0.0)),
                ('no', models.FloatField(default=0.0)),
                ('line_total', models.FloatField(default=0.0)),
                ('discount', models.IntegerField(blank=True, null=True)),
                ('discount_amount', models.FloatField(default=0.0)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='purchase_product', to='master.product')),
                ('purchase_invoice', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='purchase_invoice_items', to='master.purchaseinvoice')),
            ],
            options={
                'verbose_name': 'PurchaseInvoiceItem',
                'verbose_name_plural': 'PurchaseInvoiceItems',
            },
        ),
        migrations.AddField(
            model_name='purchaseinvoice',
            name='suplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.suplier'),
        ),
        migrations.AddField(
            model_name='purchaseinvoice',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Party',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=250, verbose_name='Party Name')),
                ('party_type', models.CharField(choices=[('sender', 'Sender'), ('supplier', 'Supplier'), ('receiver', 'Receiver')], max_length=150, verbose_name='Party Type')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'party',
                'verbose_name_plural': 'parties',
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('box', models.FloatField(default=0.0, verbose_name='box rate')),
                ('pcs', models.FloatField(default=0.0, verbose_name='pcs rate')),
                ('crate', models.FloatField(default=0.0, verbose_name='crate rate')),
                ('amount', models.FloatField(default=0.0, verbose_name='amount')),
                ('item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.item', verbose_name='item name')),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='order_items', to='master.order', verbose_name='order')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.party', verbose_name='supplier name')),
            ],
            options={
                'verbose_name': 'order item',
                'verbose_name_plural': 'order items',
            },
        ),
        migrations.AddField(
            model_name='order',
            name='reciever',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.party', verbose_name='reciever name'),
        ),
        migrations.AddField(
            model_name='order',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='LineAccounts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bill_date', models.DateField(blank=True, null=True)),
                ('bill_amount', models.FloatField(default=0.0)),
                ('no_3_amount', models.FloatField(default=0.0)),
                ('paid_amount', models.FloatField(default=0.0)),
                ('suplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.suplier')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Line Account',
                'verbose_name_plural': 'Line Accounts',
            },
        ),
        migrations.CreateModel(
            name='Ledger',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mode_of_payment', models.CharField(blank=True, max_length=250, null=True, verbose_name='mode of payment')),
                ('discount_amount', models.FloatField(blank=True, default=0.0, null=True)),
                ('amount', models.FloatField(default=0.0)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('remarks', models.CharField(blank=True, max_length=350, null=True)),
                ('entry_type', models.CharField(blank=True, choices=[('debit', 'Debit'), ('credit', 'Credit')], max_length=150, null=True)),
                ('closing_amount', models.FloatField(blank=True, default=0.0, null=True)),
                ('ledger_file', models.FileField(blank=True, null=True, upload_to='ledger')),
                ('created_at', models.DateField(auto_now_add=True)),
                ('buyer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='buyer_payment_entry', to='master.buyer')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invoice_payment_entry', to='master.salesinvoice')),
                ('purchase_invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='purchase_invoice_payment_entry', to='master.purchaseinvoice')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='buyer_payment_entry', to='master.suplier')),
            ],
            options={
                'verbose_name': 'ledger',
                'verbose_name_plural': 'ledgers',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rate', models.FloatField(blank=True, default=0, null=True)),
                ('qty', models.IntegerField(blank=True, null=True)),
                ('amount', models.FloatField(blank=True, default=0, null=True)),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invoice_items', to='master.invoice')),
                ('item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='item', to='master.item')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='supplier', to='master.party')),
            ],
        ),
        migrations.AddField(
            model_name='invoice',
            name='reciever',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reciever', to='master.party'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='sender',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sender', to='master.party'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mode_of_payment', models.CharField(blank=True, max_length=250, null=True, verbose_name='mode of payment')),
                ('amount', models.FloatField(default=0)),
                ('notes', models.TextField(blank=True, null=True)),
                ('expense_file', models.FileField(blank=True, null=True, upload_to='expense')),
                ('created_at', models.DateField(auto_now_add=True, null=True)),
                ('updated_at', models.DateField(auto_now=True, null=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expenses_category', to='master.expensecategory')),
                ('subcategory', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expenses_subcategory', to='master.subcategory')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='BuyerUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('buyer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.buyer')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
