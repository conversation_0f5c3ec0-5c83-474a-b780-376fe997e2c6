# Generated by Django 4.2.18 on 2025-02-25 11:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0018_alter_buyer_credit_limit'),
    ]

    operations = [
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('asset_type', models.CharField(blank=True, max_length=2250, null=True)),
                ('serial_no', models.CharField(blank=True, max_length=2250, null=True)),
                ('capacity', models.CharField(blank=True, max_length=2250, null=True)),
                ('model', models.CharField(blank=True, max_length=2250, null=True)),
                ('asset_file', models.FileField(blank=True, null=True, upload_to='asset')),
            ],
            options={
                'verbose_name': 'deposit',
                'verbose_name_plural': 'deposits',
            },
        ),
        migrations.AddField(
            model_name='buyer',
            name='asset',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.asset'),
        ),
    ]
