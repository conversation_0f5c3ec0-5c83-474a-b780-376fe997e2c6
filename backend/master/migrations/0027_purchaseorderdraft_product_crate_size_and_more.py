# Generated by Django 4.2.18 on 2025-04-29 11:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0026_alter_purchaseinvoiceitem_weight'),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrderDraft',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(default='Draft', max_length=20)),
            ],
        ),
        migrations.AddField(
            model_name='product',
            name='crate_size',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='product',
            name='is_crate_based',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='min_stock_threshold',
            field=models.PositiveIntegerField(default=10),
        ),
        migrations.AddField(
            model_name='suplier',
            name='contact_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='preferred_language',
            field=models.CharField(default='english', max_length=20),
        ),
        migrations.CreateModel(
            name='PurchaseOrderDraftItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.FloatField()),
                ('forecast_quantity', models.FloatField()),
                ('current_stock', models.FloatField()),
                ('min_stock_threshold', models.FloatField()),
                ('slm_reason', models.TextField(blank=True, null=True)),
                ('auto_generated', models.BooleanField(default=True)),
                ('draft', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='master.purchaseorderdraft')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='master.product')),
            ],
        ),
        migrations.AddField(
            model_name='purchaseorderdraft',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='master.suplier'),
        ),
        migrations.CreateModel(
            name='ProductForecast',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('forecast_quantity', models.FloatField()),
                ('generated_on', models.DateField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='master.product')),
            ],
        ),
        migrations.CreateModel(
            name='InventoryInsight',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('insight', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='master.product')),
            ],
        ),
    ]
