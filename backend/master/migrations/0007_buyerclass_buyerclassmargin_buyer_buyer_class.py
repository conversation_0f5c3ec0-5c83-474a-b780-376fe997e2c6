# Generated by Django 4.2.18 on 2025-02-20 04:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0006_alter_user_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='BuyerClass',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=250, null=True)),
            ],
            options={
                'verbose_name': 'buyer class',
                'verbose_name_plural': 'buyer clases',
            },
        ),
        migrations.CreateModel(
            name='BuyerClassMargin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('margin', models.FloatField(blank=True, default=0.0, null=True)),
                ('buyer_class', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='buyer_class', to='master.buyerclass')),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='buyer_class_margin', to='master.product')),
            ],
            options={
                'verbose_name': 'buyer class margin',
                'verbose_name_plural': 'buyer class margins',
            },
        ),
        migrations.AddField(
            model_name='buyer',
            name='buyer_class',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.buyerclass'),
        ),
    ]
