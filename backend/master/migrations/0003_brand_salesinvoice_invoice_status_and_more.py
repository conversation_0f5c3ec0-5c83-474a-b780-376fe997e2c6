# Generated by Django 4.2.18 on 2025-02-18 08:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0002_alter_ledger_discount_amount'),
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                'verbose_name': 'brand',
                'verbose_name_plural': 'brands',
            },
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='invoice_status',
            field=models.CharField(blank=True, default='billed', max_length=2550, null=True, verbose_name='Invoice Status'),
        ),
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='ordered_pcs',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='ordered_qty',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='salesinvoiceitem',
            name='no',
            field=models.FloatField(default=0.0, verbose_name='Pcs'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceitem',
            name='weight',
            field=models.FloatField(default=0.0, verbose_name='quantity'),
        ),
        migrations.AddField(
            model_name='product',
            name='brand',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='master.brand', verbose_name='Brand Name'),
        ),
    ]
