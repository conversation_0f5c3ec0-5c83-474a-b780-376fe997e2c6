# Generated by Django 4.2.18 on 2025-07-03 06:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0042_invoiceimage'),
    ]

    operations = [
        migrations.CreateModel(
            name='BuyerBrand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='brand_buyers', to='master.brand')),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='buyer_brands', to='master.buyer')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Buyer Brand',
                'verbose_name_plural': 'Buyer Brands',
                'ordering': ['brand__name'],
                'unique_together': {('buyer', 'brand')},
            },
        ),
    ]
