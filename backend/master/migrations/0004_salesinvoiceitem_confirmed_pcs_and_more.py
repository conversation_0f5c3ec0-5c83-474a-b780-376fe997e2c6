# Generated by Django 4.2.18 on 2025-02-18 09:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0003_brand_salesinvoice_invoice_status_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='confirmed_pcs',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='confirmed_qty',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='salesinvoice',
            name='invoice_status',
            field=models.CharField(choices=[('order', 'Order Placed'), ('billed', 'Billed'), ('cancelled', 'Cancelled')], default='billed', max_length=20, verbose_name='Invoice Status'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceitem',
            name='no',
            field=models.FloatField(default=0.0, verbose_name='quantity'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='salesinvoiceitem',
            name='weight',
            field=models.FloatField(default=0.0, verbose_name='Pcs'),
        ),
    ]
