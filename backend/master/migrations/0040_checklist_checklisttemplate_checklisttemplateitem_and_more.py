# Generated by Django 4.2.18 on 2025-06-17 07:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0039_salesinvoice_billed_by_salesinvoice_collected_by_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Checklist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=250, verbose_name='Checklist Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('overdue', 'Overdue')], default='pending', max_length=20)),
                ('priority', models.Char<PERSON><PERSON>(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='medium', max_length=20)),
                ('due_date', models.DateTimeField(blank=True, null=True, verbose_name='Due Date')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='Start Time')),
                ('submit_time', models.DateTimeField(blank=True, null=True, verbose_name='Submit Time')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('buyer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='checklists', to='master.buyer')),
                ('route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='checklists', to='master.route')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checklists', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'checklist',
                'verbose_name_plural': 'checklists',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChecklistTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=250, verbose_name='Template Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checklist_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'checklist template',
                'verbose_name_plural': 'checklist templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ChecklistTemplateItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250, verbose_name='Item Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_mandatory', models.BooleanField(default=False, verbose_name='Is Mandatory')),
                ('sort_order', models.IntegerField(default=0, verbose_name='Sort Order')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='template_items', to='master.checklisttemplate')),
            ],
            options={
                'verbose_name': 'checklist template item',
                'verbose_name_plural': 'checklist template items',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='ChecklistItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250, verbose_name='Item Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_mandatory', models.BooleanField(default=False, verbose_name='Is Mandatory')),
                ('is_completed', models.BooleanField(default=False, verbose_name='Is Completed')),
                ('image', models.ImageField(blank=True, null=True, upload_to='checklist_items/', verbose_name='Item Image')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('sort_order', models.IntegerField(default=0, verbose_name='Sort Order')),
                ('checklist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='master.checklist')),
            ],
            options={
                'verbose_name': 'checklist item',
                'verbose_name_plural': 'checklist items',
                'ordering': ['sort_order', 'id'],
            },
        ),
    ]
