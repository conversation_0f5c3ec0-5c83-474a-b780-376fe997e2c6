# Generated by Django 4.2.18 on 2025-02-25 03:32

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0009_route_buyer_route'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='brand',
            options={'permissions': [('view_reports', 'Can view reports')], 'verbose_name': 'brand', 'verbose_name_plural': 'brands'},
        ),
        migrations.AlterModelOptions(
            name='purchaseinvoice',
            options={'permissions': [('view_purchase_payment', 'Can view purchase payment')], 'verbose_name': 'Purchase Invoice', 'verbose_name_plural': 'Purchase Invoices'},
        ),
        migrations.AlterModelOptions(
            name='salesinvoice',
            options={'ordering': ['id'], 'permissions': [('view_tally', 'Can view tally'), ('view_sales_payment', 'Can view sales payment')], 'verbose_name': 'Sales Invoice', 'verbose_name_plural': 'Sales Invoices'},
        ),
    ]
