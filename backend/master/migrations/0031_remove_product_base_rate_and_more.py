# Generated by Django 4.2.18 on 2025-06-13 11:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0030_rate_based_pricing'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='base_rate',
        ),
        migrations.RemoveField(
            model_name='purchaseinvoiceitem',
            name='base_rate',
        ),
        migrations.RemoveField(
            model_name='purchaseinvoiceitem',
            name='selling_rate',
        ),
        migrations.RemoveField(
            model_name='purchaseinvoiceitem',
            name='tax_amount',
        ),
        migrations.RemoveField(
            model_name='purchaseinvoiceitem',
            name='tax_rate_percent',
        ),
        migrations.RemoveField(
            model_name='salesinvoiceitem',
            name='base_rate',
        ),
        migrations.RemoveField(
            model_name='salesinvoiceitem',
            name='selling_rate',
        ),
        migrations.RemoveField(
            model_name='salesinvoiceitem',
            name='tax_amount',
        ),
        migrations.RemoveField(
            model_name='salesinvoiceitem',
            name='tax_rate_percent',
        ),
        migrations.AlterField(
            model_name='product',
            name='margin',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='mrp',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='pr_margin',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='pr_rate',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='rate',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='tax_amount',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='tax_rate',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceitem',
            name='mrp',
            field=models.FloatField(blank=True, default=0.0, null=True),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceitem',
            name='rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='salesinvoiceitem',
            name='mrp',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='salesinvoiceitem',
            name='rate',
            field=models.FloatField(default=0.0),
        ),
    ]
