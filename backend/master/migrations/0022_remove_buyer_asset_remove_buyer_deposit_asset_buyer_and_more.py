# Generated by Django 4.2.18 on 2025-03-03 07:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0021_alter_buyer_credit_limit'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='buyer',
            name='asset',
        ),
        migrations.RemoveField(
            model_name='buyer',
            name='deposit',
        ),
        migrations.AddField(
            model_name='asset',
            name='buyer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='buyer_asset', to='master.buyer'),
        ),
        migrations.AddField(
            model_name='deposit',
            name='buyer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='buyer_deposit', to='master.buyer'),
        ),
    ]
