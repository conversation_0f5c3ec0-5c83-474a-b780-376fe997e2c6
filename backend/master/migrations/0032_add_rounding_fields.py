# Generated by Django 4.2.18 on 2025-06-13 13:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0031_remove_product_base_rate_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='salesinvoice',
            name='rounding_adjustment',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='rounding_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='rounding_method',
            field=models.CharField(choices=[('none', 'No Rounding'), ('nearest', 'Round to Nearest Rupee'), ('up', 'Round Up'), ('down', 'Round Down')], default='none', max_length=20, verbose_name='Rounding Method'),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='subtotal_before_rounding',
            field=models.FloatField(default=0.0),
        ),
    ]
