# Generated by Django 4.2.18 on 2025-06-13 10:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0028_add_enhanced_product_fields'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='additional_margin',
        ),
        migrations.RemoveField(
            model_name='product',
            name='ordered_quantity',
        ),
        migrations.RemoveField(
            model_name='product',
            name='price_per_uom',
        ),
        migrations.RemoveField(
            model_name='product',
            name='uom',
        ),
        migrations.AddField(
            model_name='product',
            name='base_rate',
            field=models.FloatField(blank=True, default=0.0, help_text='A: Base rate without tax', null=True),
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='base_rate',
            field=models.FloatField(default=0.0, help_text='A: Base rate without tax'),
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='selling_rate',
            field=models.FloatField(default=0.0, help_text='C: Selling rate (A + B)'),
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='tax_amount',
            field=models.FloatField(default=0.0, help_text='B: Tax amount'),
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='tax_rate_percent',
            field=models.FloatField(default=0.0, help_text='B: Tax percentage'),
        ),
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='base_rate',
            field=models.FloatField(default=0.0, help_text='A: Base rate without tax'),
        ),
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='selling_rate',
            field=models.FloatField(default=0.0, help_text='C: Selling rate (A + B)'),
        ),
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='tax_amount',
            field=models.FloatField(default=0.0, help_text='B: Tax amount'),
        ),
        migrations.AddField(
            model_name='salesinvoiceitem',
            name='tax_rate_percent',
            field=models.FloatField(default=0.0, help_text='B: Tax percentage'),
        ),
        migrations.AlterField(
            model_name='product',
            name='margin',
            field=models.FloatField(blank=True, default=0.0, help_text='Legacy: Sales margin', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='mrp',
            field=models.FloatField(blank=True, default=0.0, help_text='Legacy: MRP for reference', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='pr_margin',
            field=models.FloatField(blank=True, default=0.0, help_text='Legacy: Purchase margin', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='pr_rate',
            field=models.FloatField(blank=True, default=0.0, help_text='Legacy: Purchase rate', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='rate',
            field=models.FloatField(blank=True, default=0.0, help_text='Legacy: Selling rate (C)', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='tax_amount',
            field=models.FloatField(blank=True, default=0.0, help_text='B: Tax amount calculated', null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='tax_rate',
            field=models.FloatField(blank=True, default=0.0, help_text='B: Tax percentage', null=True),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceitem',
            name='mrp',
            field=models.FloatField(blank=True, default=0.0, help_text='Legacy: MRP', null=True),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceitem',
            name='rate',
            field=models.FloatField(default=0.0, help_text='Legacy: Rate field'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceitem',
            name='mrp',
            field=models.FloatField(default=0.0, help_text='Legacy: MRP'),
        ),
        migrations.AlterField(
            model_name='salesinvoiceitem',
            name='rate',
            field=models.FloatField(default=0.0, help_text='Legacy: Rate field'),
        ),
    ]
