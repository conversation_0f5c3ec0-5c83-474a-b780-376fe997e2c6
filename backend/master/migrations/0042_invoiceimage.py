# Generated by Django 4.2.18 on 2025-07-03 05:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0041_shopfreezerphoto_routeschedule_routebillingsummary'),
    ]

    operations = [
        migrations.CreateModel(
            name='InvoiceImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='invoice_images/')),
                ('filename', models.CharField(max_length=255)),
                ('file_size', models.IntegerField(help_text='File size in bytes')),
                ('file_type', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('sales_invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoice_images', to='master.salesinvoice')),
                ('uploaded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Invoice Image',
                'verbose_name_plural': 'Invoice Images',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
