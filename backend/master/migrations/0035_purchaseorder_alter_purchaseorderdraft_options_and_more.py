# Generated by Django 4.2.18 on 2025-06-14 10:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0034_enable_rounding_by_default'),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('po_number', models.CharField(max_length=50, unique=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('sent_to_supplier', 'Sent to Supplier'), ('partially_received', 'Partially Received'), ('fully_received', 'Fully Received'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('auto_generated', models.BooleanField(default=False)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('approved_date', models.DateTimeField(blank=True, null=True)),
                ('sent_date', models.DateTimeField(blank=True, null=True)),
                ('total_amount', models.FloatField(default=0.0)),
                ('tax_amount', models.FloatField(default=0.0)),
                ('discount_amount', models.FloatField(default=0.0)),
                ('net_amount', models.FloatField(default=0.0)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('terms_and_conditions', models.TextField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_pos', to=settings.AUTH_USER_MODEL)),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='master.suplier')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Purchase Order',
                'verbose_name_plural': 'Purchase Orders',
                'ordering': ['-created_date'],
            },
        ),
        migrations.AlterModelOptions(
            name='purchaseorderdraft',
            options={'ordering': ['-created_on'], 'verbose_name': 'Purchase Order Draft', 'verbose_name_plural': 'Purchase Order Drafts'},
        ),
        migrations.AlterModelOptions(
            name='purchaseorderdraftitem',
            options={'ordering': ['product__sort_order'], 'verbose_name': 'Purchase Order Draft Item', 'verbose_name_plural': 'Purchase Order Draft Items'},
        ),
        migrations.RenameField(
            model_name='purchaseorderdraftitem',
            old_name='slm_reason',
            new_name='reorder_reason',
        ),
        migrations.AddField(
            model_name='product',
            name='abc_classification',
            field=models.CharField(choices=[('A', 'A - High Value'), ('B', 'B - Medium Value'), ('C', 'C - Low Value')], default='C', max_length=1),
        ),
        migrations.AddField(
            model_name='product',
            name='lead_time_days',
            field=models.PositiveIntegerField(default=7),
        ),
        migrations.AddField(
            model_name='product',
            name='reorder_point',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='product',
            name='safety_stock',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='purchaseorderdraft',
            name='auto_generated',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='purchaseorderdraft',
            name='forecast_based',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='purchaseorderdraft',
            name='total_estimated_amount',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='purchaseorderdraft',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='purchaseorderdraftitem',
            name='estimated_rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='purchaseorderdraftitem',
            name='estimated_total',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='purchaseorderdraftitem',
            name='shortage_quantity',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='purchaseorderdraft',
            name='status',
            field=models.CharField(default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='purchaseorderdraft',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='draft_orders', to='master.suplier'),
        ),
        migrations.AlterField(
            model_name='purchaseorderdraftitem',
            name='forecast_quantity',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ordered_quantity', models.FloatField()),
                ('received_quantity', models.FloatField(default=0.0)),
                ('pending_quantity', models.FloatField(default=0.0)),
                ('unit_rate', models.FloatField(default=0.0)),
                ('line_total', models.FloatField(default=0.0)),
                ('tax_rate', models.FloatField(default=0.0)),
                ('tax_amount', models.FloatField(default=0.0)),
                ('forecast_quantity', models.FloatField(blank=True, null=True)),
                ('current_stock', models.FloatField(blank=True, null=True)),
                ('min_stock_threshold', models.FloatField(blank=True, null=True)),
                ('reorder_reason', models.TextField(blank=True, null=True)),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='po_items', to='master.product')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='master.purchaseorder')),
            ],
            options={
                'verbose_name': 'Purchase Order Item',
                'verbose_name_plural': 'Purchase Order Items',
                'ordering': ['product__sort_order'],
            },
        ),
        migrations.CreateModel(
            name='SupplierPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_preferred', models.BooleanField(default=False)),
                ('lead_time_days', models.PositiveIntegerField(default=7)),
                ('minimum_order_quantity', models.FloatField(default=1.0)),
                ('packaging_constraint', models.CharField(blank=True, max_length=100, null=True)),
                ('priority', models.PositiveIntegerField(default=1)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supplier_preferences', to='master.product')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_preferences', to='master.suplier')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Supplier Preference',
                'verbose_name_plural': 'Supplier Preferences',
                'ordering': ['priority'],
                'unique_together': {('product', 'supplier')},
            },
        ),
        migrations.CreateModel(
            name='SalesForecast',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('forecast_date', models.DateField()),
                ('forecast_month', models.PositiveIntegerField()),
                ('forecast_year', models.PositiveIntegerField()),
                ('last_year_same_month_sales', models.FloatField(default=0.0)),
                ('last_3_months_avg_sales', models.FloatField(default=0.0)),
                ('growth_factor', models.FloatField(default=0.1)),
                ('internal_forecast_quantity', models.FloatField(default=0.0)),
                ('ai_confirmed_quantity', models.FloatField(blank=True, null=True)),
                ('ai_confidence_score', models.FloatField(blank=True, null=True)),
                ('ai_analysis_comment', models.TextField(blank=True, null=True)),
                ('ai_processed', models.BooleanField(default=False)),
                ('ai_processed_at', models.DateTimeField(blank=True, null=True)),
                ('final_forecast_quantity', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='forecasts', to='master.product')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sales Forecast',
                'verbose_name_plural': 'Sales Forecasts',
                'ordering': ['-forecast_date'],
                'unique_together': {('product', 'forecast_month', 'forecast_year')},
            },
        ),
    ]
