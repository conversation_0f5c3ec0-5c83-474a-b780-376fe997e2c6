# Generated by Django 4.2.18 on 2025-06-13 09:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0027_purchaseorderdraft_product_crate_size_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='additional_margin',
            field=models.FloatField(blank=True, default=0.0, help_text='Additional margin percentage to add to primary margin', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='ordered_quantity',
            field=models.FloatField(blank=True, default=0.0, help_text='Quantity being ordered', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='price_per_uom',
            field=models.FloatField(blank=True, default=0.0, help_text='Selling price per unit of measure', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='uom',
            field=models.CharField(blank=True, default='pieces', help_text='Unit of Measure (pieces, boxes, kg, etc.)', max_length=20, null=True),
        ),
    ]
