# Generated by Django 4.2.18 on 2025-03-03 08:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0022_remove_buyer_asset_remove_buyer_deposit_asset_buyer_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='asset',
            name='asset_file',
        ),
        migrations.CreateModel(
            name='AssetFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('asset_file', models.FileField(upload_to='uploads/')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='master.asset')),
            ],
        ),
    ]
