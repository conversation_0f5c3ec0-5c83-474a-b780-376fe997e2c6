# Generated by Django 4.2.18 on 2025-06-15 07:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master', '0035_purchaseorder_alter_purchaseorderdraft_options_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='purchaseorderitem',
            name='expected_delivery_date',
        ),
        migrations.CreateModel(
            name='SupplierBrand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='brand_suppliers', to='master.brand')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supplier_brands', to='master.suplier')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Supplier Brand',
                'verbose_name_plural': 'Supplier Brands',
                'ordering': ['brand__name'],
                'unique_together': {('supplier', 'brand')},
            },
        ),
    ]
