from django.shortcuts import render, get_object_or_404
from .serializer import *
from .models import *
from django.contrib.auth import authenticate
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import login
from django.db.models.aggregates import Sum
from django.db.models import Sum as model_sum, F, ExpressionWrapper, DecimalField,Q,OuterRef, Subquery, Value, FloatField,Case, When, Value, Count
from django.core.exceptions import ObjectDoesNotExist
from django.db.models.functions import Cast,Coalesce, TruncMonth, TruncQuarter, TruncYear
import random
from rest_framework import status
from rest_framework.authentication import TokenAuthentication, BasicAuthentication
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from django.template.loader import get_template
from django.http import HttpResponse
from xhtml2pdf import pisa
from rest_framework import viewsets
from rest_framework.permissions import DjangoModelPermissions
import csv
import io
from itertools import chain
from django.db import transaction
from django.apps import apps
from drf_spectacular.utils import extend_schema, OpenApiParameter
from django.core.paginator import Paginator
from .utils import ProductStockManager,calculate_salesman_incentive
from django.apps import apps
import io
from rest_framework.decorators import action, api_view
from django.utils.translation import gettext as _
from django.utils import translation

def get_current_financial_year_dates():
    """
    Calculate current financial year start and end dates
    Financial year in India typically runs from April 1st to March 31st
    """
    current_date = timezone.now().date()
    current_year = current_date.year
    
    # If current date is before April 1st, financial year started in previous year
    if current_date.month < 4:
        financial_year_start = datetime(current_year - 1, 4, 1).date()
        financial_year_end = datetime(current_year, 3, 31).date()
    else:
        financial_year_start = datetime(current_year, 4, 1).date()
        financial_year_end = datetime(current_year + 1, 3, 31).date()
    
    return financial_year_start, financial_year_end

def fetch_success(data=None):
    fetch_success = {
        'success': True,
        'message': 'Data retrieved successfully'
    }
    if data is not None:
        fetch_success['data'] = data
    return fetch_success


def save_success(data=None):
    save_success = {
        'success': True,
        'message': 'Data saved successfully'
    }
    if data is not None:
        save_success['data'] = data
    return save_success


def edit_success(data=None):
    edit_success = {
        'success': True,
        'message': 'Data edited successfully'
    }
    if data is not None:
        edit_success['data'] = data
    return edit_success


def delete_success(data=None):
    delete_success = {
        'success': True,
        'message': 'Data deleted successfully'
    }
    if data is not None:
        delete_success['data'] = data
    return delete_success

def serializer_msg(msg):
    return {
        'success': True,
        'message': msg
    }


invalid_user = {
    'success': False,
    'message': 'invalid user account'
}

def page_not_available(data=None):
    msg = {
        'success': False,
        'message': "Page not available",
        'code': 500
    }
    if data is not None:
        msg['message'] = data
    return msg

def paginate_queryset(data, request, serializer_class):
    """
    Common function to paginate any queryset.
    """
    response_data = dict()
    page_size = request.query_params.get("page_size",10)
    page_number = request.query_params.get("page_number",1)
    paginator = Paginator(data, page_size)
    if int(page_number) not in [*paginator.page_range]:
        return page_not_available()
    response_data['data'] = serializer_class(paginator.page(page_number), many=True).data
    response_data['count'] = data.count()
    return response_data

    
class LoginView(APIView):
    authentication_classes = [BasicAuthentication, ]

    def post(self, request, format='json'):
        data = request.data
        username = data["username"]
        password = data["password"]
        user = authenticate(request, username=username, password=password)
        # Check if authentication successful
        if user is not None:
            login(request, user)
            token, created = Token.objects.get_or_create(user=user)
            userData = UserSerializer(user).data
            metadata = user.company.metadata if user.company is not None else user.metadata
            return Response({'success': True, 'message':  'lOGGED IN SUCESSFULLY', 'token': token.key, 'user': userData,'metadata':metadata})
        else:
            return Response({'success': False, 'message': 'Only Customer Creditionals Allowed'})

class BrandView(APIView):

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Brand.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = Brand.objects.filter(user=request.user.company if request.user.company else request.user)
        return Response(fetch_success(BrandSerializer(data, many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        name = data.get('name') 
        if name is None or name == '' :
            return Response({"success":False,"message":"Data can\'t be blank"})
        Brand.objects.create(**data, user=request.user.company if request.user.company else request.user)
        return Response(save_success())

class RouteView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Brand.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = Route.objects.filter(user=request.user.company if request.user.company else request.user)
        return Response(fetch_success(RouteSerializer(data, many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        name = data.get('name')
        if name is None or name == '' :
            return Response({"success":False,"message":"Data can\'t be blank"})
        Route.objects.create(**data, user=request.user.company if request.user.company else request.user)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        route_id = data.get('id')
        name = data.get('name')

        if not route_id:
            return Response({"success": False, "message": "Route ID is required"})
        if name is None or name == '':
            return Response({"success": False, "message": "Route name can't be blank"})

        try:
            route = Route.objects.get(id=route_id, user=request.user.company if request.user.company else request.user)
            route.name = name
            route.save()
            return Response(edit_success())
        except Route.DoesNotExist:
            return Response({"success": False, "message": "Route not found"})

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        route_id = data.get('id')

        if not route_id:
            return Response({"success": False, "message": "Route ID is required"})

        try:
            route = Route.objects.get(id=route_id, user=request.user.company if request.user.company else request.user)
            route.delete()
            return Response(delete_success())
        except Route.DoesNotExist:
            return Response({"success": False, "message": "Route not found"})

class ClosingStockView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Product.objects.all()

    def get(self, request, format=None):
        from_date = self.request.query_params.get("from_date",None)
        to_date = self.request.query_params.get("to_date",None)
        if from_date is None or to_date is None:
            return Response({"success":False,"message":"Please select the date"})
        return Response(fetch_success(ProductStockManager.get_closing_stock(from_date, to_date,Product.objects.filter(user=request.user.company if request.user.company else request.user))))


class ProductView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Product.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        search = self.request.query_params.get('search',None)
        request_params = {}
        request_params['user']=request.user.company if request.user.company else request.user
        if search is not None:
            request_params['name__icontains']=search
        data = Product.objects.filter(**request_params)
        # ProductSerializer(data, many=True).data
        response = paginate_queryset(data,request,ProductSerializer)
        if isinstance(response, dict) and 'success' in response and not response['success']:
            return Response(response)
        return Response(fetch_success(response))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data.copy()
        brand_id = data.pop('brand_id', None)
        brand = Brand.objects.get(id=brand_id) if brand_id else None
        Product.objects.create(**data, user=request.user.company if request.user.company else request.user, brand=brand)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data.copy()
        product_id = data.pop('id')
        brand_id = data.pop('brand_id', None)
        if brand_id:
            data['brand'] = Brand.objects.get(id=brand_id)
        Product.objects.filter(id=product_id).update(**data)
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Product.objects.filter(id=data['id']).delete()
        return Response(delete_success())

class MetadataView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = User.objects.all()

    def get(self,request,format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        return Response(fetch_success(request.user.company if request.user.company else request.user))
    
    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        user=request.user.company if request.user.company else request.user
        user.metadata = data
        user.save()
        return Response(save_success())

class BuyerClassView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = BuyerClass.objects.all()

    def get(self,request,format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = BuyerClass.objects.filter(user=request.user.company if request.user.company else request.user)
        return Response(fetch_success(BuyerClassSerializer(data, many=True).data))
    
    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        BuyerClass.objects.create(**data, user=request.user.company if request.user.company else request.user)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        BuyerClass.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        buyer_class_id = self.request.query_params.get('buyer_class_id',None)
        BuyerClass.objects.filter(id=buyer_class_id).delete()
        return Response(delete_success())

class BuyerClassMarginView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = BuyerClassMargin.objects.all()

    def get(self,request,format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        buyer_class_id = self.request.query_params.get('buyer_class_id',None)

        # Get subquery for margin ID
        margin_subquery = BuyerClassMargin.objects.filter(
            buyer_class_id=buyer_class_id,
            product_id=OuterRef("id")
        ).values("id")[:1]  # Fetch only one ID per product

        # Get subquery for margin value
        margin_value_subquery = BuyerClassMargin.objects.filter(
            buyer_class_id=buyer_class_id,
            product_id=OuterRef("id")
        ).values("margin")[:1]  # Fetch margin if exists

        # Join Product with BuyerClassMargin and get `id` and `margin`
        products_with_margins = Product.objects.filter(user=request.user.company if request.user.company else request.user).annotate(
            buyer_class_margin_id=Subquery(margin_subquery),  # Get margin ID
            final_margin=Coalesce(Subquery(margin_value_subquery), F("margin"), Value(0.0))  # Get margin or default
        ).values("id","buyer_class_margin_id", "margin","final_margin","brand","brand__name","short_code")

        return Response(fetch_success(products_with_margins))
    
    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data

        if not isinstance(data, list):
            return Response({"error": "Invalid data format, expected a list"}, status=400)
        buyer_class_id = self.request.query_params.get("buyer_class_id")
        if not buyer_class_id:
            return Response({"error": "buyer_class_id is required"}, status=400)

        # Fetch BuyerClass once
        try:
            buyer_class = BuyerClass.objects.get(id=buyer_class_id)
        except BuyerClass.DoesNotExist:
            return Response({"error": "BuyerClass not found"}, status=404)

        # Fetch all required Products in one query
        product_ids = [entry["id"] for entry in data]
        products = {p.id: p for p in Product.objects.filter(id__in=product_ids)}

        margins_to_update = []
        margins_to_create = []

        for entry in data:
            product = products.get(entry["id"])
            if not product:
                continue  # Skip invalid products

            if entry.get("buyer_class_margin_id"):  # Update existing margin
                margins_to_update.append(
                    BuyerClassMargin(
                        id=entry["buyer_class_margin_id"],
                        product=product,
                        buyer_class=buyer_class,
                        margin=entry["final_margin"],
                    )
                )
            else:  # Create new margin
                margins_to_create.append(
                    BuyerClassMargin(
                        product=product,
                        buyer_class=buyer_class,
                        margin=entry["final_margin"],
                    )
                )

        # Perform bulk operations in a transaction
        with transaction.atomic():
            if margins_to_create:
                BuyerClassMargin.objects.bulk_create(margins_to_create)
            if margins_to_update:
                BuyerClassMargin.objects.bulk_update(margins_to_update, ["margin"])  # Specify fields to update

        return Response(save_success())

class BuyerClassMarginImportExportView(APIView):
    """Custom Import/Export for BuyerClassMargin with buyer_class filtering"""

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    queryset = BuyerClassMargin.objects.all()

    def get(self, request, format=None):
        """Export BuyerClassMargin data as CSV for specific buyer class"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        buyer_class_id = self.request.query_params.get('buyer_class_id', None)
        if not buyer_class_id:
            return Response({"error": "buyer_class_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            buyer_class = BuyerClass.objects.get(
                id=buyer_class_id,
                user=request.user.company if request.user.company else request.user
            )
        except BuyerClass.DoesNotExist:
            return Response({"error": "Buyer class not found"}, status=status.HTTP_404_NOT_FOUND)

        # Create HTTP response with CSV file
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename=buyer_class_margins_{buyer_class.name}.csv'

        writer = csv.writer(response)

        # Write headers
        headers = ['id', 'product_id', 'product_name', 'product_short_code', 'brand', 'margin', 'action']
        writer.writerow(headers)

        # Get subquery for margin ID and value
        margin_subquery = BuyerClassMargin.objects.filter(
            buyer_class_id=buyer_class_id,
            product_id=OuterRef("id")
        ).values("id")[:1]

        margin_value_subquery = BuyerClassMargin.objects.filter(
            buyer_class_id=buyer_class_id,
            product_id=OuterRef("id")
        ).values("margin")[:1]

        # Get products with their margins
        products_with_margins = Product.objects.filter(
            user=request.user.company if request.user.company else request.user
        ).annotate(
            buyer_class_margin_id=Subquery(margin_subquery),
            final_margin=Coalesce(Subquery(margin_value_subquery), F("margin"), Value(0.0))
        ).values("id", "buyer_class_margin_id", "margin", "final_margin", "brand", "short_code")

        # Write data rows
        for product in products_with_margins:
            row = [
                product['buyer_class_margin_id'] or '',  # id (empty for new records)
                product['id'],  # product_id
                product['short_code'],  # product_name
                product['short_code'],  # product_short_code
                product['brand'] or '',  # brand
                product['final_margin'],  # margin
                ''  # action (empty by default)
            ]
            writer.writerow(row)

        return response

    def post(self, request, format=None):
        """Import CSV data for BuyerClassMargin"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        buyer_class_id = self.request.query_params.get('buyer_class_id', None)
        if not buyer_class_id:
            return Response({"error": "buyer_class_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            buyer_class = BuyerClass.objects.get(
                id=buyer_class_id,
                user=request.user.company if request.user.company else request.user
            )
        except BuyerClass.DoesNotExist:
            return Response({"error": "Buyer class not found"}, status=status.HTTP_404_NOT_FOUND)

        if 'csv_file' not in request.FILES:
            return Response({"error": "No file uploaded"}, status=status.HTTP_400_BAD_REQUEST)

        csv_file = request.FILES['csv_file']
        decoded_file = io.TextIOWrapper(csv_file.file, encoding='utf-8')
        reader = csv.DictReader(decoded_file)

        errors = []
        created_count = 0
        updated_count = 0
        deleted_count = 0

        for row in reader:
            margin_id = row.get('id', '').strip()
            product_id = row.get('product_id', '').strip()
            margin_value = row.get('margin', '').strip()
            action = row.get('action', '').strip().lower()

            if not product_id:
                errors.append("Missing product_id in row")
                continue

            try:
                product = Product.objects.get(
                    id=product_id,
                    user=request.user.company if request.user.company else request.user
                )
            except Product.DoesNotExist:
                errors.append(f"Product with ID {product_id} not found")
                continue

            if margin_id:  # Update or delete existing margin
                try:
                    margin_obj = BuyerClassMargin.objects.get(id=margin_id, buyer_class=buyer_class)

                    if action == 'delete':
                        margin_obj.delete()
                        deleted_count += 1
                    else:
                        if margin_value:
                            try:
                                margin_obj.margin = float(margin_value)
                                margin_obj.save()
                                updated_count += 1
                            except ValueError:
                                errors.append(f"Invalid margin value '{margin_value}' for product {product_id}")

                except BuyerClassMargin.DoesNotExist:
                    errors.append(f"Margin record with ID {margin_id} not found")

            else:  # Create new margin
                if margin_value:
                    try:
                        margin_float = float(margin_value)
                        margin_obj, created = BuyerClassMargin.objects.get_or_create(
                            buyer_class=buyer_class,
                            product=product,
                            defaults={'margin': margin_float}
                        )
                        if created:
                            created_count += 1
                        else:
                            margin_obj.margin = margin_float
                            margin_obj.save()
                            updated_count += 1
                    except ValueError:
                        errors.append(f"Invalid margin value '{margin_value}' for product {product_id}")

        message = f"Import completed: {created_count} created, {updated_count} updated, {deleted_count} deleted"
        if errors:
            return Response({
                "message": message,
                "errors": errors
            }, status=status.HTTP_207_MULTI_STATUS)

        return Response({"message": message}, status=status.HTTP_200_OK)


class ImportExportView(APIView):
    """API View for Importing and Exporting CSV with Authentication & Permissions"""

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, DjangoModelPermissions]

    def get_model(self, app_label, model_name):
        """Retrieve Django model dynamically"""
        return apps.get_model(app_label, model_name)

    def get_queryset(self):
        """DjangoModelPermissions requires a valid queryset"""
        app_label = self.kwargs.get('app_label')
        model_name = self.kwargs.get('model_name')

        if app_label and model_name:
            model = self.get_model(app_label, model_name)
            return model.objects.all()

        return None  # Return None if no valid queryset

    def get(self, request, app_label, model_name):
        """Export model data as CSV (Include ForeignKey ID and String Representation)"""
        model = self.get_model(app_label, model_name)

        # Check if user has permission to view this model
        if not request.user.has_perm(f"{model._meta.app_label}.view_{model._meta.model_name}"):
            return Response({"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN)

        fields = {field.name: field for field in model._meta.fields}  # Get field metadata

        # Create HTTP response with CSV file
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename={model_name}.csv'

        writer = csv.writer(response)

        # Prepare headers (Include both ForeignKey ID and string representation)
        headers = []
        for field_name, field in fields.items():
            headers.append(field_name)  # Add the actual field
            if isinstance(field, models.ForeignKey):  # Also add a readable column
                headers.append(f"{field_name}_name")

        writer.writerow(headers)  # Write header row

        # Write data
        for obj in model.objects.filter(user=request.user.company if request.user.company else request.user):
            row = []
            for field_name, field in fields.items():
                value = getattr(obj, field_name)
                if isinstance(field, models.ForeignKey):
                    row.append(value.id if value else '')  # ForeignKey ID
                    row.append(str(value) if value else '')  # ForeignKey string representation
                else:
                    row.append(value)
            writer.writerow(row)  # Write row data

        return response


    def post(self, request, app_label, model_name):
        """Import CSV data for model update and delete, ignoring ForeignKey string representation"""
        model = self.get_model(app_label, model_name)

        # Check permissions
        if not request.user.has_perm(f"{model._meta.app_label}.change_{model._meta.model_name}"):
            return Response({"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN)

        fields = {field.name: field for field in model._meta.fields}  # Get field metadata

        if 'csv_file' not in request.FILES:
            return Response({"error": "No file uploaded"}, status=status.HTTP_400_BAD_REQUEST)

        csv_file = request.FILES['csv_file']
        decoded_file = io.TextIOWrapper(csv_file.file, encoding='utf-8')
        reader = csv.DictReader(decoded_file)

        errors = []
        for row in reader:
            row_id = row.get('id')  # ID column for updates/deletes
            action = row.get('action', '').strip().lower()  # Action column

            if row_id:  # If ID exists, update or delete
                try:
                    obj = model.objects.get(id=row_id)

                    if action == 'delete':
                        if not request.user.has_perm(f"{model._meta.app_label}.delete_{model._meta.model_name}"):
                            errors.append(f"Permission denied for deleting record ID {row_id}")
                            continue
                        obj.delete()  # Delete record
                    else:
                        for field_name, field in fields.items():
                            if field_name in row and field_name != 'id' and not field_name.endswith('_name'):
                                setattr(obj, field_name, self.parse_field_value(field, row[field_name]))
                        obj.save()

                except ObjectDoesNotExist:
                    errors.append(f"Record with ID {row_id} not found.")

            else:  # If no ID, create a new record
                if not request.user.has_perm(f"{model._meta.app_label}.add_{model._meta.model_name}"):
                    errors.append("Permission denied for adding new records.")
                    continue

                obj_data = {}
                for field_name, field in fields.items():
                    if field_name in row and field_name != 'id' and not field_name.endswith('_name'):
                        obj_data[field_name] = self.parse_field_value(field, row[field_name])

                model.objects.create(**obj_data)

        if errors:
            return Response({"message": "Import completed with errors", "errors": errors}, status=status.HTTP_207_MULTI_STATUS)

        return Response({"message": "CSV data imported successfully"}, status=status.HTTP_200_OK)


    def parse_field_value(self, field, value):
        """Convert CSV field values to appropriate data types, handling ForeignKeys safely."""
        if isinstance(field, models.ForeignKey):  # Handle ForeignKey fields
            related_model = field.related_model

            # If value is empty or invalid, return None
            if not value or not value.strip().isdigit():
                return None  

            try:
                return related_model.objects.get(id=int(value))  # Convert value to int before querying
            except related_model.DoesNotExist:
                return None  # Return None if ForeignKey ID does not exist

        elif isinstance(field, models.BooleanField):  # Convert "true"/"false" strings
            return value.lower() in ['true', '1', 'yes']

        elif isinstance(field, models.IntegerField):  # Convert to integer safely
            return int(value) if value.strip().isdigit() else None

        elif isinstance(field, models.FloatField):  # Convert to float safely
            try:
                return float(value) if value else None
            except ValueError:
                return None

        return value  # Default case, return as is

class BuyerView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Buyer.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        buyer_id = self.request.query_params.get('id',None)
        if buyer_id is not None:
            data = Buyer.objects.get(id=buyer_id)
            return Response(fetch_success(BuyerDetailsSerializer(data).data))
        
        # Get all buyers for the user
        user = request.user.company if request.user.company else request.user
        buyers = Buyer.objects.filter(user=user)
        
        # Order by active status first (active=True comes first), then by sort_order
        data = buyers.order_by('-active', 'sort_order')
        return Response(fetch_success(BuyerSerializer(data, many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        asset = self.request.query_params.get('asset',None)
        deposit = self.request.query_params.get('deposit',None)
        buyer_id = self.request.query_params.get('id',None)
        if asset is not None:
            asset = Asset.objects.create(asset_type=request.data.get("asset_type"),serial_no=request.data.get("serial_no"),capacity=request.data.get("capacity"),model=request.data.get("model"),)
            asset.buyer =  Buyer.objects.get(id=buyer_id)
            asset.save()

            asset_files = request.FILES.getlist("asset_file")
            for file in asset_files:
                AssetFile.objects.create(asset=asset, asset_file=file)

            return Response(save_success())
        if deposit is not None:
            deposit = Deposit(
                buyer = Buyer.objects.get(id=buyer_id),
                amount = float(request.data.get('amount')),
                date = request.data.get('date'),
                reference = request.data.get('reference'),
                reference_file = request.FILES.get('reference_file'),
            )
            deposit.save()
            return Response(save_success())
        data['user'] = request.user.company if request.user.company else request.user
        # Handle route and buyer_class only if they are provided
        if 'route_id' in data and data['route_id']:
            data['route'] = Route.objects.get(id=data['route_id'])
            data.pop("route_id")
        if 'buyer_class_id' in data and data['buyer_class_id']:
            data['buyer_class'] = BuyerClass.objects.get(id=data['buyer_class_id'])
            data.pop("buyer_class_id")
        Buyer.objects.create(**data)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        asset = self.request.query_params.get('asset',None)
        deposit = self.request.query_params.get('deposit',None)
        if asset is not None:
            asset_files = request.FILES.getlist("asset_file")
            asset=Asset.objects.get(id=data['id'])
            for file in asset_files:
                AssetFile.objects.create(asset=asset, asset_file=file)
            Asset.objects.filter(id=data['id']).update(asset_type=request.data.get("asset_type"),serial_no=request.data.get("serial_no"),capacity=request.data.get("capacity"),model=request.data.get("model"))
        elif deposit is not None:
            Deposit.objects.filter(id=data['id']).update(
                amount = float(request.data.get('amount')),
                date = request.data.get('date'),
                reference = request.data.get('reference'),
                reference_file = request.FILES.get('reference_file'),
            )
        else:
            # Handle route and buyer_class only if they are provided
            if 'route_id' in data and data['route_id']:
                data['route'] = Route.objects.get(id=data['route_id'])
                data.pop("route_id")
            if 'buyer_class_id' in data and data['buyer_class_id']:
                data['buyer_class'] = BuyerClass.objects.get(id=data['buyer_class_id'])
                data.pop("buyer_class_id")
            Buyer.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        asset = self.request.query_params.get('asset',None)
        deposit = self.request.query_params.get('deposit',None)
        id_ = self.request.query_params.get('id',None)
        if asset is not None:
            Asset.objects.filter(id=id_).delete()
        elif deposit is not None:
            Deposit.objects.filter(id=id_).delete()
        else:
            data = request.data
            Buyer.objects.filter(id=data['id']).delete()
        return Response(delete_success())


class SuplierView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Suplier.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = Suplier.objects.filter(user=request.user.company if request.user.company else request.user)
        return Response(fetch_success(SuplierSerializer(data, many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Suplier.objects.create(**data, user=request.user.company if request.user.company else request.user)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Suplier.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Suplier.objects.filter(id=data['id']).delete()
        return Response(delete_success())


class SupplierBrandView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    queryset = SupplierBrand.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        supplier_id = self.request.query_params.get('supplier_id', None)
        brand_id = self.request.query_params.get('brand_id', None)

        filter_params = {'user': request.user.company if request.user.company else request.user}

        if supplier_id:
            filter_params['supplier_id'] = supplier_id
        if brand_id:
            filter_params['brand_id'] = brand_id

        data = SupplierBrand.objects.filter(**filter_params)
        return Response(fetch_success(SupplierBrandSerializer(data, many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        supplier_id = data.get('supplier_id')
        brand_id = data.get('brand_id')

        if not supplier_id or not brand_id:
            return Response({'error': 'Both supplier_id and brand_id are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if relationship already exists
        existing = SupplierBrand.objects.filter(
            supplier_id=supplier_id,
            brand_id=brand_id,
            user=request.user.company if request.user.company else request.user
        ).first()

        if existing:
            return Response({'error': 'Supplier-Brand relationship already exists'}, status=status.HTTP_400_BAD_REQUEST)

        SupplierBrand.objects.create(
            supplier_id=supplier_id,
            brand_id=brand_id,
            user=request.user.company if request.user.company else request.user
        )
        return Response(save_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        supplier_brand_id = data.get('id')

        if not supplier_brand_id:
            return Response({'error': 'Supplier-Brand ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        SupplierBrand.objects.filter(
            id=supplier_brand_id,
            user=request.user.company if request.user.company else request.user
        ).delete()
        return Response(delete_success())


class BuyerBrandView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    queryset = BuyerBrand.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        buyer_id = self.request.query_params.get('buyer_id', None)
        brand_id = self.request.query_params.get('brand_id', None)

        filter_params = {'user': request.user.company if request.user.company else request.user}

        if buyer_id:
            filter_params['buyer_id'] = buyer_id
        if brand_id:
            filter_params['brand_id'] = brand_id

        data = BuyerBrand.objects.filter(**filter_params)
        return Response(fetch_success(BuyerBrandSerializer(data, many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        buyer_id = data.get('buyer_id')
        brand_id = data.get('brand_id')

        if not buyer_id or not brand_id:
            return Response({'error': 'Both buyer_id and brand_id are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if relationship already exists
        existing = BuyerBrand.objects.filter(
            buyer_id=buyer_id,
            brand_id=brand_id,
            user=request.user.company if request.user.company else request.user
        ).first()

        if existing:
            return Response({'error': 'Buyer-Brand relationship already exists'}, status=status.HTTP_400_BAD_REQUEST)

        BuyerBrand.objects.create(
            buyer_id=buyer_id,
            brand_id=brand_id,
            user=request.user.company if request.user.company else request.user
        )
        return Response(save_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        buyer_brand_id = data.get('id')

        if not buyer_brand_id:
            return Response({'error': 'Buyer-Brand ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        BuyerBrand.objects.filter(
            id=buyer_brand_id,
            user=request.user.company if request.user.company else request.user
        ).delete()
        return Response(delete_success())


class PurchasePaymentView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = PurchaseInvoice.objects.all()

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        if data['supplier'] == []:
            return Response(fetch_success(PurchasePaymentSerializer(PurchaseInvoice.objects.filter(user=request.user.company if request.user.company else request.user, date__gte=data['from_date'], date__lte=data['to_date']), many=True).data))
        return Response(fetch_success(PurchasePaymentSerializer(PurchaseInvoice.objects.filter(user=request.user.company if request.user.company else request.user, suplier__id__in=data['supplier'], date__gte=data['from_date'], date__lte=data['to_date']), many=True).data))


class SalesPaymentView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = SalesInvoice.objects.all()

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        if data['buyer'] == []:
            return Response(fetch_success(SalesPaymentSerializer(SalesInvoice.objects.filter(invoice_status='billed',user=request.user.company if request.user.company else request.user, date__gte=data['from_date'], date__lte=data['to_date']), many=True).data))

        return Response(fetch_success(SalesPaymentSerializer(SalesInvoice.objects.filter(invoice_status='billed',user=request.user.company if request.user.company else request.user, buyer__id__in=data['buyer'], date__gte=data['from_date'], date__lte=data['to_date']), many=True).data))

class IncentiveView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = SalesInvoice.objects.all()

    def get(self, request, format=None):
        from_date = self.request.query_params.get("from_date",None)
        to_date = self.request.query_params.get("to_date",None)
        sales_person = self.request.query_params.get("sales_person",None)
        if sales_person is None:
            return Response(fetch_success(User.objects.filter(role='sales_officer',company=request.user.company if request.user.company else request.user).values('id','first_name')))
        if from_date is None or to_date is None:
            return Response({"success":False,"message":"Please select the date"})
        return Response(fetch_success(calculate_salesman_incentive(sales_person,from_date, to_date)))

class AssetView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Asset.objects.all()

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        deposit = Asset(**data)
        deposit.save()
        if data['buyer_id']:
            buyer = Buyer.objects.get(id=data['buyer_id'])
            buyer.deposit = deposit
            buyer.save()
        
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Asset.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Asset.objects.filter(id=data['id']).delete()
        return Response(delete_success())

class DepositView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Deposit.objects.all()

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        deposit = Deposit(**data)
        deposit.save()
        if data['buyer_id']:
            buyer = Buyer.objects.get(id=data['buyer_id'])
            buyer.deposit = deposit
            buyer.save()
        if data['supplier_id']:
            supplier = Suplier.objects.get(id=data['supplier_id'])
            supplier.deposit = deposit
            supplier.save()
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Deposit.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Deposit.objects.filter(id=data['id']).delete()
        return Response(delete_success())

class SalesOrderView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = SalesOrder.objects.all()

    def get(self, request, format=None):
        create_invoice = self.request.query_params.get('create_invoice', None)
        buyer_id = self.request.query_params.get('buyer_id', None)
        invoice_id = self.request.query_params.get('invoice_id', None)
        date = self.request.query_params.get('date', None)
        if request.user.is_anonymous:
            return Response(invalid_user)
        fetch_success_local = fetch_success()
        if invoice_id is not None:
            data = SalesOrder.objects.get(id=invoice_id)
            fetch_success_local['data'] = SalesOrderSerializer(data).data
            product_data = Product.objects.filter(user=request.user.company if request.user.company else request.user)
            fetch_success_local['product_data'] = ProductSerializer(
                product_data, many=True).data
        if buyer_id is not None:
            data = SalesOrder.objects.filter(buyer__id=buyer_id).last()
            fetch_success_local['data'] = SalesOrderSerializer(data).data
        if create_invoice is not None:
            buyer_data = Buyer.objects.filter(user=request.user.company if request.user.company else request.user).order_by('-active', 'sort_order')
            fetch_success_local['buyer_data'] = BuyerSerializer(
                buyer_data, many=True).data
            product_data = Product.objects.filter(user=request.user.company if request.user.company else request.user)
            fetch_success_local['product_data'] = ProductSerializer(
                product_data, many=True).data
        if create_invoice is None and buyer_id is None and invoice_id is None:
            data = SalesOrder.objects.filter(user=request.user.company if request.user.company else request.user, date=date)
            fetch_success_local['data'] = SalesOrderSerializer(
                data, many=True).data
        return Response(fetch_success_local)

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        buyer = Buyer.objects.get(id=data['invoice']['buyer_id'])
        sales_invoice = SalesOrder.objects.create(
            **data['invoice'], buyer=buyer, user=request.user.company if request.user.company else request.user)
        for item in data['invoice_item']:
            product = Product.objects.get(id=item['product_id'])
            SalesOrderItem.objects.create(
                **item, sales_invoice=sales_invoice, product=product)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        sales_invoice = SalesOrder.objects.get(id=data['invoice']['id'])
        SalesOrder.objects.filter(
            id=data['invoice']['id']).update(**data['invoice'])
        for item in data['invoice_item']:
            if 'id' in item.keys():
                if item['id'] is not None:
                    SalesOrderItem.objects.filter(id=item['id']).update(**item)
            if 'product_id' in item.keys():
                if item['product_id'] is not None:
                    product = Product.objects.get(id=item['product_id'])
                    SalesOrderItem.objects.create(
                        **item, sales_invoice=sales_invoice, product=product)
        SalesOrderItem.objects.filter(
            sales_invoice=sales_invoice, weight=0, no=0).delete()
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        SalesOrder.objects.filter(id=data['id']).delete()
        return Response(delete_success())


class ReportView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Ledger.objects.all()

    def get(self,request,format=None):
        report_type = self.request.query_params.get('report_type', None)
        from_date = self.request.query_params.get(
            'from_date', timezone.now() - timedelta(days=30))
        to_date = self.request.query_params.get('to_date', timezone.now())
        mode_of_payment = self.request.query_params.get('mode_of_payment', None)
        if report_type == "mode_of_payment":
            query = Q()
            if request.user.company:
                query |= Q(buyer__user=request.user.company)
                query |= Q(supplier__user=request.user.company)
            else:
                query |= Q(buyer__user=request.user)
                query |= Q(supplier__user=request.user)
            # Fetch and combine Ledger and Expense entries, ordered by date
            ledger_entries = Ledger.objects.filter(query,created_at__lte=to_date, created_at__gte=from_date,mode_of_payment=mode_of_payment).values(
                "created_at", "amount", "remarks", "entry_type"
            ).annotate(source_type=Sum(1))  # Tag for source identification
            expense_entries = Expense.objects.filter(user=request.user.company if request.user.company else request.user,created_at__lte=to_date, created_at__gte=from_date,mode_of_payment=mode_of_payment).values(
                "created_at", "amount", "notes"
            ).annotate(source_type=Sum(2))  # Tag for source identification

            combined_entries = sorted(
                chain(ledger_entries, expense_entries), key=lambda x: x["created_at"]
            )

            # Prepare report data
            report_data = []
            closing_amount = 0.0

            for entry in combined_entries:
                if entry["source_type"] == 1:  # Ledger entry
                    credit = entry["amount"] if entry["entry_type"] == "credit" else 0.0
                    debit = entry["amount"] if entry["entry_type"] == "debit" else 0.0
                elif entry["source_type"] == 2:  # Expense entry
                    credit = 0.0
                    debit = entry["amount"]

                closing_amount += credit - debit

                report_data.append({
                    "date": entry["created_at"],
                    "debit": debit if debit else "",
                    "credit": credit if credit else "",
                    "remarks": entry.get("remarks") or entry.get("notes"),
                    "closing_amount": closing_amount
                })
            report_data.append({
                    "date": "Total Closing",
                    "debit": '',
                    "credit": '',
                    "remarks": '',
                    "closing_amount": closing_amount
                })
            return Response(fetch_success(report_data))
        if report_type == "expense":
            expense_entries = Expense.objects.filter(user=request.user.company if request.user.company else request.user,created_at__lte=to_date, created_at__gte=from_date)
            report = [
                {
                    "Date": entry.created_at,
                    "Category": entry.category.name if entry.category else "",
                    "Subcategory": entry.subcategory.name if entry.subcategory else "",
                    "Amount": round(entry.amount),                               
                    "Mode of payment": entry.mode_of_payment if entry.mode_of_payment else "",
                    "Remarks": entry.notes
                }
                for entry in expense_entries
            ]
        if report_type == 'pay-in':
            payin_entries = Ledger.objects.filter(buyer__user=request.user.company if request.user.company else request.user,created_at__lte=to_date, created_at__gte=from_date,buyer__isnull=False, invoice__isnull=False)
            report = [
                {
                    "Date": entry.created_at,
                    "Buyer": entry.buyer.name if entry.buyer else "",
                    "Invoice": entry.invoice.id,
                    "Debit": round(entry.amount+entry.discount_amount) if entry.entry_type  == "debit" else "",
                    "Credit": round(entry.amount+entry.discount_amount) if entry.entry_type  == "credit" else "",                                     
                    # "Discount": entry.discount_amount,
                    "Mode of payment": entry.mode_of_payment if entry.mode_of_payment else "",
                    "Closing": round(entry.closing_amount),
                    "Remarks": entry.remarks
                }
                for entry in payin_entries
            ]
        if report_type == 'pay-out':
            payout_entries = Ledger.objects.filter(supplier__user=request.user.company if request.user.company else request.user,created_at__lte=to_date, created_at__gte=from_date,supplier__isnull=False, purchase_invoice__isnull=False)
            report = [
                {
                    "Date": entry.created_at,
                    "Supplier": entry.supplier.name if entry.supplier else "",
                    "Invoice": entry.purchase_invoice.id,
                    "Debit": round(entry.amount+entry.discount_amount) if entry.entry_type  == "debit" else "",
                    "Credit": round(entry.amount+entry.discount_amount) if entry.entry_type  == "credit" else "",                                     
                    # "Discount": entry.discount_amount,
                    "Mode of payment": entry.mode_of_payment if entry.mode_of_payment else "",
                    "Closing": round(entry.closing_amount),
                    "Remarks": entry.remarks
                }
                for entry in payout_entries
            ]
        
        # Brand-wise Sales Report
        if report_type == 'brand_wise_sales':
            brand_ids = self.request.query_params.getlist('brand_ids', [])
            include_zero_sales = self.request.query_params.get('include_zero_sales', 'true').lower() == 'true'

            # Get buyers mapped to the selected brands
            from master.models import BuyerBrand
            mapped_buyer_ids = BuyerBrand.objects.filter(
                brand_id__in=brand_ids,
                is_active=True
            ).values_list('buyer_id', flat=True)

            # Base query for sales invoice items, filtered by mapped buyers
            base_query = SalesInvoiceItem.objects.filter(
                sales_invoice__user=request.user.company if request.user.company else request.user,
                sales_invoice__date__gte=from_date,
                sales_invoice__date__lte=to_date,
                sales_invoice__invoice_status='billed',
                product__brand__isnull=False,
                sales_invoice__buyer__id__in=mapped_buyer_ids
            )
            if brand_ids:
                base_query = base_query.filter(product__brand__id__in=brand_ids)
            if not include_zero_sales:
                base_query = base_query.filter(line_total__gt=0)

            # Group by buyer and aggregate
            buyer_sales = base_query.values(
                'sales_invoice__buyer__id',
                'sales_invoice__buyer__name',
            ).annotate(
                invoices=Count('sales_invoice', distinct=True),
                line_total=Sum('line_total'),
            ).order_by('-line_total')

            # Output only Buyer, Invoices, Line Total
            report = []
            for row in buyer_sales:
                report.append({
                    'Buyer': row['sales_invoice__buyer__name'] or 'Unknown Buyer',
                    'Invoices': row['invoices'],
                    'Line Total': round(row['line_total'], 2) if row['line_total'] else 0.0
                })

            # Add summary row
            total_invoices = sum(r['Invoices'] for r in report)
            total_line_total = sum(r['Line Total'] for r in report)
            report.append({
                'Buyer': 'TOTAL',
                'Invoices': total_invoices,
                'Line Total': round(total_line_total, 2)
            })

        # General Sales Report
        if report_type == 'general_sales':
            include_zero_sales = self.request.query_params.get('include_zero_sales', 'true').lower() == 'true'
            
            # Base query for sales invoices (not items)
            base_query = SalesInvoice.objects.filter(
                user=request.user.company if request.user.company else request.user,
                date__gte=from_date,
                date__lte=to_date,
                invoice_status='billed'
            ).select_related('buyer')
            
            # Filter out zero sales if requested
            if not include_zero_sales:
                base_query = base_query.filter(bill_amount__gt=0)
            
            # Group by buyer and aggregate
            buyer_sales = base_query.values(
                'buyer__id',
                'buyer__name',
                'buyer__place'
            ).annotate(
                invoices=Count('id', distinct=True),
                line_total=Sum('bill_amount'),
            ).order_by('-line_total')
            
            # Create consolidated report grouped by buyer
            report = []
            
            for sale_data in buyer_sales:
                buyer_name = sale_data['buyer__name'] or "Unknown Buyer"
                buyer_place = sale_data['buyer__place'] or ""
                
                report.append({
                    "Buyer": buyer_name,
                    "Invoices": sale_data['invoices'],
                    "Line Total": round(sale_data['line_total'], 2) if sale_data['line_total'] else 0.0
                })
            
            # Add overall summary
            total_invoices = sum(r['Invoices'] for r in report)
            total_line_total = sum(r['Line Total'] for r in report)
            report.append({
                "Buyer": "TOTAL",
                "Invoices": total_invoices,
                "Line Total": round(total_line_total, 2)
            })
        
        return Response(fetch_success(report))


class SalesInvoicePaymentView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Ledger.objects.all()

    def get(self, request, format=None):
        """Get payment history for a specific sales invoice"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        invoice_id = self.request.query_params.get('invoice_id', None)
        if not invoice_id:
            return Response({'error': 'Invoice ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            invoice = SalesInvoice.objects.get(id=invoice_id, user=request.user.company if request.user.company else request.user)
            payments = Ledger.objects.filter(invoice=invoice, entry_type='credit').order_by('-created_at')

            # Calculate outstanding balance
            total_payments = sum(payment.amount for payment in payments)
            outstanding_balance = invoice.bill_amount - total_payments

            payment_data = {
                'invoice_id': invoice.id,
                'bill_amount': invoice.bill_amount,
                'total_payments': total_payments,
                'outstanding_balance': outstanding_balance,
                'payments': LedgerSerializer(payments, many=True).data
            }

            return Response(fetch_success(payment_data))
        except SalesInvoice.DoesNotExist:
            return Response({'error': 'Invoice not found'}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request, format=None):
        """Create a new payment receipt for a sales invoice"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        invoice_id = data.get('invoice_id')
        amount = data.get('amount')
        mode_of_payment = data.get('mode_of_payment')
        reference_number = data.get('reference_number', '')
        notes = data.get('notes', '')

        # Validation
        if not invoice_id or not amount:
            return Response({'error': 'Invoice ID and amount are required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            amount = float(amount)
            if amount <= 0:
                return Response({'error': 'Amount must be greater than 0'}, status=status.HTTP_400_BAD_REQUEST)
        except ValueError:
            return Response({'error': 'Invalid amount format'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            invoice = SalesInvoice.objects.get(id=invoice_id, user=request.user.company if request.user.company else request.user)

            # Calculate current outstanding balance (for informational purposes only)
            existing_payments = Ledger.objects.filter(invoice=invoice, entry_type='credit')
            total_payments = sum(payment.amount for payment in existing_payments)
            outstanding_balance = invoice.bill_amount - total_payments

            # Note: Payment amount validation removed to allow advance payments and overpayments

            # Create payment receipt entry
            remarks = f"Payment Receipt for Invoice {invoice.id}"
            if reference_number:
                remarks += f" - Ref: {reference_number}"
            if notes:
                remarks += f" - {notes}"

            # Create ledger entry (the model's save method will handle closing amount calculation)
            ledger = Ledger.objects.create(
                invoice=invoice,
                buyer=invoice.buyer,
                amount=amount,
                mode_of_payment=mode_of_payment,
                entry_type='credit',
                remarks=remarks
            )

            # Update invoice received amount and current balance
            invoice.received_amount = (invoice.received_amount or 0) + amount
            invoice.current_balance = invoice.bill_amount - invoice.received_amount
            invoice.save()

            return Response(save_success())

        except SalesInvoice.DoesNotExist:
            return Response({'error': 'Invoice not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LedgerView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = Ledger.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        from_date = self.request.query_params.get(
            'from_date', timezone.now() - timedelta(days=30))
        to_date = self.request.query_params.get('to_date', timezone.now())
        buyer = self.request.query_params.get('buyer', None)
        buyer_id = self.request.query_params.get('buyer_id', None)
        supplier_id = self.request.query_params.get('supplier_id', None)
        if buyer_id is not None and from_date is not None and to_date is not None:
            data = Ledger.objects.filter(created_at__lte=to_date, created_at__gte=from_date, buyer__id=buyer_id).order_by('-created_at', '-id')
            paginated_data = paginate_queryset(data, request, LedgerSerializer)
            if isinstance(paginated_data, dict) and 'success' in paginated_data and not paginated_data['success']:
                return Response(paginated_data)
            return Response(fetch_success(paginated_data))
        if supplier_id is not None and from_date is not None and to_date is not None:
            data = Ledger.objects.filter(created_at__lte=to_date, created_at__gte=from_date, supplier__id=supplier_id).order_by('-created_at', '-id')
            paginated_data = paginate_queryset(data, request, LedgerSerializer)
            if isinstance(paginated_data, dict) and 'success' in paginated_data and not paginated_data['success']:
                return Response(paginated_data)
            return Response(fetch_success(paginated_data))
        response_data={} 
        ledger_subquery = Ledger.objects.filter(buyer=OuterRef('id')).order_by('-id').values('closing_amount')[:1]
        filter_params = {}
        filter_params['user']=request.user.company if request.user.company else request.user
        filter_params['active']=True
        search = self.request.query_params.get('search',None)
        if search:
            filter_params['name__icontains']=search
        if buyer:  
            data =Buyer.objects.filter(**filter_params).annotate(balance_amount=Coalesce(Subquery(ledger_subquery, output_field=models.DecimalField()),Value(0.0, output_field=models.DecimalField()))).order_by('-balance_amount')
            paginated_data = paginate_queryset(data,request,BuyerLedgerListSerializer)
            if isinstance(paginated_data, dict) and 'success' in paginated_data and not paginated_data['success']:
                return Response(paginated_data)
            response_data['data'] = paginated_data
        else:
            data =Suplier.objects.filter(**filter_params).annotate(balance_amount=Coalesce(Subquery(ledger_subquery, output_field=models.DecimalField()),Value(0.0, output_field=models.DecimalField()))).order_by('-balance_amount')
            paginated_data = paginate_queryset(data,request,SupplierLedgerListSerializer)
            if isinstance(paginated_data, dict) and 'success' in paginated_data and not paginated_data['success']:
                return Response(paginated_data)
            response_data['data'] = paginated_data

        totals = data.aggregate(
            pending_amount=Sum(Case(When(balance_amount__gt=0, then='balance_amount'), default=Value(0), output_field=DecimalField())),
            advance_amount=Sum(Case(When(balance_amount__lt=0, then='balance_amount'), default=Value(0), output_field=DecimalField()))
        )
        response_data['pending_amount']=totals['pending_amount'] or 0
        response_data['advance_amount']=totals['advance_amount'] or 0
        return Response(fetch_success(response_data))
    

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        buyer = self.request.query_params.get("buyer",False)
        supplier = self.request.query_params.get("supplier",False)
        party_transfer = self.request.query_params.get("party_transfer",False)
        if party_transfer:
            from_ = self.request.query_params.get("from",None)
            try:
                buyer = Buyer.objects.get(id=data['from_party__id'] if from_ == "buyer" else data['to_party__id'])
            except Buyer.DoesNotExist:
                return Response({'error': 'Buyer not found'}, status=status.HTTP_404_NOT_FOUND)
            try:
                supplier = Suplier.objects.get(id=data['to_party__id']  if from_ == "buyer" else data['from_party__id'])
            except Suplier.DoesNotExist:
                return Response({'error': 'Buyer not found'}, status=status.HTTP_404_NOT_FOUND)
            
            if from_ == "buyer":
                ledger = Ledger(
                    buyer=buyer,
                    amount=float(data['amount']),
                    remarks=data['remarks'],
                    mode_of_payment="transfer",
                    entry_type="debit"
                )
            
                if 'file' in request.FILES and request.FILES['file'] is not None:
                    ledger.ledger_file = request.FILES['file']
                
                ledger.save()
                ledger = Ledger(
                    supplier = supplier ,
                    amount=float(data['amount']),
                    remarks=data['remarks'],
                    mode_of_payment="transfer",
                    entry_type="credit"
                )
                if 'file' in request.FILES and request.FILES['file'] is not None:
                    ledger.ledger_file = request.FILES['file']
            
                ledger.save()
            if from_ == "supplier":
                ledger = Ledger(
                    supplier = supplier ,
                    amount=float(data['amount']),
                    remarks=data['remarks'],
                    mode_of_payment="transfer",
                    entry_type="debit"
                )
            
                if 'file' in request.FILES and request.FILES['file'] is not None:
                    ledger.ledger_file = request.FILES['file']
                
                ledger.save()
                ledger = Ledger(
                    buyer=buyer,
                    amount=float(data['amount']),
                    remarks=data['remarks'],
                    mode_of_payment="transfer",
                    entry_type="credit"
                )
                if 'file' in request.FILES and request.FILES['file'] is not None:
                    ledger.ledger_file = request.FILES['file']
            
                ledger.save()
            return Response(save_success())

            
        if buyer:
            try:
                buyer = Buyer.objects.get(id=data['party__id'])
            except Buyer.DoesNotExist:
                return Response({'error': 'Buyer not found'}, status=status.HTTP_404_NOT_FOUND)
            
            ledger = Ledger(
                buyer=buyer,
                amount=float(data['amount']),
                remarks=data['remarks'],
                mode_of_payment=data['mode_of_payment'],
                discount_amount=data['discount_amount'],
                entry_type=data['entry_type']
            )
            
            if 'file' in request.FILES and request.FILES['file'] is not None:
                ledger.ledger_file = request.FILES['file']
            
            ledger.save()
            return Response(save_success())

        if supplier:
            try:
                supplier = Suplier.objects.get(id=data['party__id'])
            except Suplier.DoesNotExist:
                return Response({'error': 'Supplier not found'}, status=status.HTTP_404_NOT_FOUND)
            
            ledger = Ledger(
                supplier=supplier,
                amount=float(data['amount']),
                remarks=data['remarks'],
                mode_of_payment=data['mode_of_payment'],
                discount_amount=data['discount_amount'],
                entry_type=data['entry_type']
            )
            
            if 'file' in request.FILES and request.FILES['file'] is not None:
                ledger.ledger_file = request.FILES['file']
            
            ledger.save()
            return Response(save_success())

    def put(self, request, format=None):
        data = request.data
        ledger = Ledger.objects.get(id=data['id'])
        ledger.amount= data['amount']
        ledger.discount_amount= data['discount_amount']
        ledger.entry_type= data['entry_type']
        ledger.remarks= data['remarks']
        ledger.save()
        return Response(edit_success())


class SalesInvoicePrintView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = SalesInvoice.objects.all()

    def get(self, request, format=None):
        invoice_id = self.request.query_params.get('invoice_id', None)
        page_type = self.request.query_params.get('page_type', None)
        invoice = SalesInvoice.objects.get(id=invoice_id)
        invoice_item = SalesInvoiceItem.objects.filter(sales_invoice=invoice)
       
        data = {
            'invoice': SalesInvoiceSerializer(invoice).data,
            'user': UserSerializer(invoice.user).data,
            'total_qty': invoice_item.count(),
            'total_box': int(invoice_item.aggregate(Sum('no'))['no__sum']),
            'total_pcs': int(invoice_item.aggregate(Sum('weight'))['weight__sum']),
            # 'tax_amount': invoice_item.aggregate(Sum('tax_amount'))['tax_amount__sum'],
            # 'sub_total': invoice.bill_amount - float(invoice_item.aggregate(Sum('tax_amount'))['tax_amount__sum']),
            # 'tax_seperation': invoice_item.annotate(tax_amount=ExpressionWrapper(F('line_total') * F('product__tax_rate') / (100 + F('product__tax_rate')), output_field=DecimalField())).values('product__tax_rate').annotate(total_tax_amount=Sum('tax_amount')).order_by('product__tax_rate'),
            'logo': "https://dev-veg.kingwizard.in/media/"+str(invoice.user.company_logo)
        }
        if page_type == '1':
            template = get_template('bill.html')
        else:
            template = get_template('a4bill.html')
        html_content = template.render(data)

        # Create the PDF
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="invocie"' + \
            invoice_id + '".pdf"'

        pisa.CreatePDF(html_content, dest=response)
        return response


class SalesInvoiceView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = SalesInvoice.objects.all()

    def get(self, request, format=None):
        create_invoice = self.request.query_params.get('create_invoice', None)
        buyer_id = self.request.query_params.get('buyer_id', None)
        invoice_id = self.request.query_params.get('invoice_id', None)
        date = self.request.query_params.get('date', None)
        sales_order = self.request.query_params.get('sales_order', None)
        if request.user.is_anonymous:
            return Response(invalid_user)
        fetch_success_local = fetch_success()
        if invoice_id is not None:
            data = SalesInvoice.objects.get(id=invoice_id)
            fetch_success_local['data'] = SalesInvoiceSerializer(data).data
            buyer_class_margin_subquery = BuyerClassMargin.objects.filter(product=OuterRef('id'),product__user=request.user.company if request.user.company else request.user,buyer_class=data.buyer.buyer_class).values('margin')[:1]

            # Query with optimized annotation
            product_data = Product.objects.filter(user=request.user.company if request.user.company else request.user).annotate(final_margin=Coalesce(Subquery(buyer_class_margin_subquery, output_field=FloatField()),F('margin'))).order_by('sort_order')
            fetch_success_local['brand'] = BrandSerializer(Brand.objects.filter(user=request.user.company if request.user.company else request.user), many=True).data
            fetch_success_local['product_data'] = ProductSerializer(
                product_data, many=True).data
        if buyer_id is not None:
            data = Ledger.objects.filter(buyer__id=buyer_id).order_by(
                'created_at', 'id').last()
            fetch_success_local['data'] = LedgerSerializer(data).data
            # Removed product_data and brand from here - they will be fetched via BuyerProductsView API
   
        if create_invoice is not None:
            buyer_data = Buyer.objects.filter(user=request.user.company if request.user.company else request.user).order_by('-active', 'sort_order')
            fetch_success_local['buyer_data'] = BuyerSerializer(
                buyer_data, many=True).data
            product_data = Product.objects.filter(user=request.user.company if request.user.company else request.user)
            fetch_success_local['product_data'] = ProductSerializer(
                product_data, many=True).data
        if create_invoice is None and buyer_id is None and invoice_id is None:
            if sales_order is not None:
                sales_officer = request.query_params.get('sales_officer', None)
                route = request.query_params.get('route', None)
                sales_person = request.query_params.get('sales_person', None)
                user_company = request.user.company if request.user.company else request.user
                filter_params = {"invoice_status": "order","user": user_company}
                fetch_success_local['sales_person'] = User.objects.filter(role='sales_officer',company=request.user).values_list('first_name', flat=True)
                if sales_officer:
                    filter_params["sales_person"] = request.user
                if route:
                    filter_params["buyer__route__id"] = route
                if sales_person:
                    filter_params["sales_person__name"] = sales_person
                data = SalesInvoice.objects.filter(**filter_params)
            else:    
                data = SalesInvoice.objects.filter(invoice_status='billed',user=request.user.company if request.user.company else request.user, date=date)
            fetch_success_local['data'] = SalesInvoiceSerializer(
                data, many=True).data
        return Response(fetch_success_local)

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        status = self.request.query_params.get('status',None)
        data = request.data
        buyer = Buyer.objects.get(id=data['invoice']['buyer_id'])
        current_balance = float(data['invoice']['current_balance'])
        bill_amount = float(data['invoice']['bill_amount'])
        if not (abs(current_balance - bill_amount) <= 100) and  buyer.credit_limit is not None and current_balance > buyer.credit_limit:
            if not request.user.has_perm('master.override_credit_limit'):
                return Response({"Success":False,"message":"Credit Limit exceeded"})
        sales_invoice = SalesInvoice.objects.create(
            **data['invoice'], buyer=buyer, user=request.user.company if request.user.company else request.user,invoice_status=status if status is not None else 'billed',sales_person=request.user if status is not None else None)
        for item in data['invoice_item']:
            product = Product.objects.get(id=item['product_id'])
            if status is not None:
                item['ordered_qty']=item['no']
                item['ordered_pcs']=item['weight']
            if status is None:
                item['confirmed_qty']=item['no']
                item['confirmed_pcs']=item['weight']
            
            SalesInvoiceItem.objects.create(
                **item, sales_invoice=sales_invoice, product=product)

        SalesInvoiceItem.objects.filter(
            sales_invoice=sales_invoice, weight=0, no=0).delete()
        if status is None:
            if data['invoice']['received_amount'] is not None and data['invoice']['received_amount'] > 0:
                Ledger.objects.create(invoice=sales_invoice, buyer=buyer,mode_of_payment=data['mode_of_payment'],
                                    amount=data['invoice']['received_amount'], entry_type='credit', remarks="Invoice Credit "+str(sales_invoice.id))
            Ledger.objects.create(invoice=sales_invoice, buyer=buyer, amount=sales_invoice.bill_amount,
                                entry_type='debit', remarks="Invoice Debit "+str(sales_invoice.id))
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        sales_invoice = SalesInvoice.objects.get(id=data['invoice']['id'])
        credit_limit = sales_invoice.buyer.credit_limit
        if credit_limit is not None and float(data['invoice']['current_balance']) > credit_limit:
            if not request.user.has_perm('master.override_credit_limit'):
                return Response({"Success":False,"message":"Credit Limit exceeded"})
        if sales_invoice.invoice_status == "billed": 
            difference_amount = sales_invoice.bill_amount - \
                data['invoice']['bill_amount']
            difference_recieved_amount = data['invoice']['received_amount'] - \
                sales_invoice.received_amount
            if difference_recieved_amount > 0:
                Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer, amount=difference_recieved_amount,mode_of_payment=data['mode_of_payment'],
                                    entry_type='credit', remarks="Invoice Credit adjustment for edit"+str(sales_invoice.id))
            if difference_recieved_amount < 0:
                Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer, amount=-(difference_recieved_amount),mode_of_payment=data['mode_of_payment'],
                                    entry_type='debit', remarks="Invoice debit adjustment for edit"+str(sales_invoice.id))
            if difference_amount > 0:
                Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer, amount=difference_amount,
                                    entry_type='credit', remarks="Invoice credit adjustment for edit "+str(sales_invoice.id))
            if difference_amount < 0:
                Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer, amount=-(difference_amount),
                                  entry_type='debit', remarks="Invoice debit adjustment for edit  "+str(sales_invoice.id))
        
        if sales_invoice.invoice_status == "order": 
            if data['invoice']['received_amount'] is not None and data['invoice']['received_amount'] > 0:
                Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer,mode_of_payment=data['mode_of_payment'],
                                    amount=data['invoice']['received_amount'], entry_type='credit', remarks="Invoice Credit "+str(sales_invoice.id))
            Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer, amount=data['invoice']['bill_amount'],
                                entry_type='debit', remarks="Invoice Debit "+str(sales_invoice.id))
            data['invoice']["invoice_status"] = "billed"

        SalesInvoice.objects.filter(
            id=data['invoice']['id']).update(**data['invoice'])
        for item in data['invoice_item']:
            if 'id' in item.keys():
                if item['id'] is not None:
                    item['confirmed_qty']=item['no']
                    item['confirmed_pcs']=item['weight']
                    SalesInvoiceItem.objects.filter(
                        id=item['id']).update(**item)
            if 'product_id' in item.keys():
                if item['product_id'] is not None:
                    product = Product.objects.get(id=item['product_id'])
                    item['confirmed_qty']=item['no']
                    item['confirmed_pcs']=item['weight']
                    SalesInvoiceItem.objects.create(
                        **item, sales_invoice=sales_invoice, product=product)
        SalesInvoiceItem.objects.filter(
            id__in=data['deletable_products']).delete()
        SalesInvoiceItem.objects.filter(
            sales_invoice=sales_invoice, weight=0, no=0).delete()
        return Response(edit_success())
    
    
    def patch(self,request,format=None):
        invoice_id = self.request.query_params.get('invoice_type',None)
        invoice_status = self.request.query_params.get('invoice_status',None)
        if invoice_id is not None:
            if invoice_status is None:
                sales_invoice = SalesInvoice.objects.get(id=invoice_id)
                sales_invoice.delivery_challan = not sales_invoice.delivery_challan 
                sales_invoice.save()
                if not sales_invoice.delivery_challan :
                    if sales_invoice.received_amount is not None and sales_invoice.received_amount > 0:
                        Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer,mode_of_payment=sales_invoice.mode_of_payment,
                                            amount=sales_invoice.received_amount, entry_type='credit', remarks="Invoice Credit "+str(sales_invoice.id))
                    Ledger.objects.create(invoice=sales_invoice, buyer=sales_invoice.buyer, amount=sales_invoice.bill_amount,
                                        entry_type='debit', remarks="Invoice Debit "+str(sales_invoice.id))
                if  sales_invoice.delivery_challan : 
                    Ledger.objects.filter(invoice=sales_invoice).delete()
            if invoice_status is not None:
                sales_invoice = SalesInvoice.objects.get(id=invoice_id)
                sales_invoice.invoice_status = invoice_status
                sales_invoice.save()

        return Response(edit_success())


    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        SalesInvoice.objects.filter(id=data['id']).delete()
        return Response(delete_success())

class SalesInvoiceReportView(APIView):
    # authentication_classes = [BasicAuthentication]
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = SalesInvoice.objects.all()

    def get(self, request, format=None):
        from_date = self.request.query_params.get('from_date', None)
        to_date = self.request.query_params.get('to_date', None)

        # Fetch the data including related invoice items
        # invoices = SalesInvoice.objects.filter(date__gte=from_date, date__lte=to_date).exclude(delivery_challan=True).prefetch_related('sales_invoice_items')
        invoices = SalesInvoice.objects.filter(invoice_status='billed',date__gte=from_date, date__lte=to_date, user=request.user.company if request.user.company else request.user).exclude(delivery_challan=True).prefetch_related('sales_invoice_items')

        # Initialize the response object
        response = HttpResponse(content_type='text/csv')
        file_name = f"sales_report-{from_date}-{to_date}.csv"
        response['Content-Disposition'] = f'attachment; filename={file_name}'

        # Create CSV writer
        csv_writer = csv.writer(response)

        # Write CSV header
        csv_writer.writerow([
            'Invoice ID', 'Buyer Name','GST No', 'Date', 'Base Price',
            'CGST', 'SGST', 'Total Tax', 'Total Discount', 'Net Amount', 
            'Product', 'Box','Pcs', 'Rate', 'Tax Rate', 'CGST Tax','SGST Tax', 'Line Total'
        ])

        # Write CSV rows
        for invoice in invoices:
            total_tax = invoice.total_tax
            cgst = total_tax / 2
            sgst = total_tax / 2
            base_price = invoice.bill_amount - (invoice.discount_amount + total_tax)
            net_amount = invoice.bill_amount - invoice.discount_amount

            # Write the invoice-level data
            csv_writer.writerow([
                invoice.id, invoice.buyer.name,invoice.buyer.gst_no, invoice.date, base_price,
                cgst, sgst, total_tax, invoice.discount_amount, net_amount,
                '', '', '', '', '', ''  # Placeholder for item details
            ])

            # Write item-level data
            for item in invoice.sales_invoice_items.all():
                csv_writer.writerow([
                    '', '', '', '', '', '', '', '', '','',
                    item.product.name,item.no,  item.weight, item.rate, item.product.tax_rate, 
                    item.line_tax/2,item.line_tax/2, item.line_total
                ])

        return response

class PurchaseInvoiceView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = PurchaseInvoice.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        list_view = self.request.query_params.get('list', None)
        invoice_id = self.request.query_params.get('invoice_id', None)
        create_invoice = self.request.query_params.get('create_invoice', None)
        fetch_success_local = fetch_success()
        if invoice_id is not None:
            data = PurchaseInvoice.objects.get(id=invoice_id)
            fetch_success_local['data'] = PurchaseInvoiceSerializer(data).data
            product_data = Product.objects.filter(user=request.user.company if request.user.company else request.user)
            fetch_success_local['brand'] = BrandSerializer(Brand.objects.filter(user=request.user.company if request.user.company else request.user), many=True).data
            fetch_success_local['product_data'] = ProductSerializer(
                product_data, many=True).data
        if list_view is not None:
            data = PurchaseInvoice.objects.filter(
                user=request.user.company if request.user.company else request.user, date=list_view)
            fetch_success_local['data'] = PurchaseInvoiceListSerializer(
                data, many=True).data
        if create_invoice is not None:
            supplier_data = Suplier.objects.filter(user=request.user.company if request.user.company else request.user)
            fetch_success_local['supplier_data'] = SuplierSerializer(
                supplier_data, many=True).data
            product_data = Product.objects.filter(user=request.user.company if request.user.company else request.user)
            fetch_success_local['brand'] = BrandSerializer(Brand.objects.filter(user=request.user.company if request.user.company else request.user), many=True).data
            fetch_success_local['product_data'] = ProductSerializer(
                product_data, many=True).data
        return Response(fetch_success_local)

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        suplier = Suplier.objects.get(id=data['invoice']['suplier_id'])
        purchase_invoice = PurchaseInvoice.objects.create(
            **data['invoice'], suplier=suplier, user=request.user.company if request.user.company else request.user)
        for item in data['invoice_item']:
            product = Product.objects.get(id=item['product_id'])
            PurchaseInvoiceItem.objects.create(
                **item, purchase_invoice=purchase_invoice, product=product)
        PurchaseInvoiceItem.objects.filter(
            purchase_invoice=purchase_invoice, weight=0, no=0).delete()
        if data['invoice']['received_amount'] is not None and data['invoice']['received_amount'] > 0:
            Ledger.objects.create(purchase_invoice=purchase_invoice, supplier=suplier,mode_of_payment=data['mode_of_payment'],
                                  amount=data['invoice']['received_amount'], entry_type='debit', remarks="Purchase Invoice Credit "+str(purchase_invoice.id))
        Ledger.objects.create(purchase_invoice=purchase_invoice,  supplier=suplier, amount=purchase_invoice.bill_amount,
                              entry_type='credit', remarks="Invoice Debit "+str(purchase_invoice.id))
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        purchase_invoice = PurchaseInvoice.objects.get(
            id=data['invoice']['id'])
        difference_amount = purchase_invoice.bill_amount - \
            data['invoice']['bill_amount']
        difference_recieved_amount = data['invoice']['received_amount'] - \
            purchase_invoice.received_amount
        if difference_recieved_amount > 0:
            Ledger.objects.create(purchase_invoice=purchase_invoice, supplier=purchase_invoice.suplier, amount=difference_recieved_amount,mode_of_payment=data['mode_of_payment'],
                                  entry_type='debit', remarks="Invoice Debit adjustment for edit"+str(purchase_invoice.id))
        if difference_recieved_amount < 0:
            Ledger.objects.create(purchase_invoice=purchase_invoice, supplier=purchase_invoice.suplier, amount=-(difference_recieved_amount),mode_of_payment=data['mode_of_payment'],
                                  entry_type='credit', remarks="Invoice Credit adjustment for edit"+str(purchase_invoice.id))
        if difference_amount < 0:
            Ledger.objects.create(purchase_invoice=purchase_invoice, supplier=purchase_invoice.suplier, amount=difference_amount,
                                  entry_type='credit', remarks="Invoice credit adjustment for edit "+str(purchase_invoice.id))
        if difference_amount > 0:
            Ledger.objects.create(purchase_invoice=purchase_invoice, supplier=purchase_invoice.suplier, amount=-(difference_amount),
                                  entry_type='debit', remarks="Invoice debit adjustment for edit  "+str(purchase_invoice.id))

        PurchaseInvoice.objects.filter(
            id=data['invoice']['id']).update(**data['invoice'])
        for item in data['invoice_item']:
            if 'id' in item.keys():
                if item['id'] is not None:
                    PurchaseInvoiceItem.objects.filter(
                        id=item['id']).update(**item)
            if 'product_id' in item.keys():
                if item['product_id'] is not None:
                    product = Product.objects.get(id=item['product_id'])
                    PurchaseInvoiceItem.objects.create(
                        **item, purchase_invoice=purchase_invoice, product=product)
        PurchaseInvoiceItem.objects.filter(
            id__in=data['deletable_products']).delete()
        PurchaseInvoiceItem.objects.filter(
            purchase_invoice=purchase_invoice, weight=0, no=0).delete()
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        purchase_invoice_id = self.request.query_params.get(
            'purchase_invoice_id', None)
        PurchaseInvoice.objects.filter(id=purchase_invoice_id).delete()
        return Response(delete_success())


class LineAccountsView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = LineAccounts.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        purchase_bill = self.request.query_params.get('purchase_bill', None)
        list_view = self.request.query_params.get('list', None)
        if list_view is not None:
            data = LineAccountsSerializer(LineAccounts.objects.filter(
                user=request.user.company if request.user.company else request.user, bill_date=list_view), many=True).data
            return Response(fetch_success(data))
        if purchase_bill is not None:
            data = PurchaseInvoiceSerializer(PurchaseInvoice.objects.filter(
                user=request.user.company if request.user.company else request.user, suplier__id=purchase_bill).last()).data
            return Response(fetch_success(data))
        if purchase_bill is None and list_view is None:
            data = Suplier.objects.filter(user=request.user.company if request.user.company else request.user)
            fdata = SuplierSerializer(data, many=True).data
            return Response(fetch_success(fdata))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        suplier = Suplier.objects.get(id=data['suplier_id'])
        LineAccounts.objects.create(**data, suplier=suplier, user=request.user.company if request.user.company else request.user)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        LineAccounts.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        purchase_invoice_id = self.request.query_params.get(
            'purchase_invoice_id', None)
        purchase_invoice = LineAccounts.objects.get(id=purchase_invoice_id)
        purchase_invoice.delete()
        return Response(delete_success())


class TallyView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated,DjangoModelPermissions]
    queryset = PurchaseInvoiceItem.objects.all()

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        from_date = self.request.query_params.get('from_date', None)
        to_date = self.request.query_params.get('to_date', None)
        fetch_success_local = fetch_success()
        fetch_success_local['purchaseInvoiceItem'] = PurchaseInvoiceItem.objects.filter(purchase_invoice__user=request.user.company if request.user.company else request.user, purchase_invoice__date__gte=from_date, purchase_invoice__date__lte=to_date).values(
            'product').annotate(Sum('no'), Sum('weight'), Sum('line_total')).values('product', 'no__sum', 'weight__sum', 'line_total__sum')
        fetch_success_local['salesInvoiceItem'] = SalesInvoiceItem.objects.filter(sales_invoice__user=request.user.company if request.user.company else request.user, sales_invoice__date__gte=from_date, sales_invoice__date__lte=to_date).values(
            'product').annotate(Sum('no'), Sum('weight'), Sum('line_total')).values('product', 'product__pr_rate', 'product__unit_contains', 'no__sum', 'weight__sum', 'line_total__sum')
        fetch_success_local['product'] = Product.objects.filter(
            user=request.user.company if request.user.company else request.user,).values('name', 'id')
        purchase = PurchaseInvoice.objects.filter(
            user=request.user.company if request.user.company else request.user, date__gte=from_date, date__lte=to_date)
        sales = SalesInvoice.objects.filter(invoice_status='billed',
            user=request.user.company if request.user.company else request.user, date__gte=from_date, date__lte=to_date)
        fetch_success_local['purchaseInvoice'] = purchase.values(
            'suplier__name', 'bill_amount')
        fetch_success_local['purchaseInvoiceTotal'] = purchase.aggregate(
            Sum('bill_amount'))['bill_amount__sum']
        fetch_success_local['salesInvoice'] = sales.values('buyer__id').annotate(bill_amount=Sum('bill_amount')).values('buyer__name', 'bill_amount').order_by('-bill_amount')
        fetch_success_local['salesInvoiceTotal'] = sales.aggregate(Sum('bill_amount'))[
            'bill_amount__sum']

        fetch_success_local['expenseTotal'] = Expense.objects.filter(created_at__gte=from_date, created_at__lte=to_date).aggregate(Sum('amount'))['amount__sum']

        return Response(fetch_success_local)


class PartyView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        return Response(fetch_success(PartySerializer(Party.objects.filter(user=request.user.company if request.user.company else request.user), many=True).data))

    def post(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        party = Party(**data, user=request.user.company if request.user.company else request.user)
        party.save()
        return Response(save_success())

    def put(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Party.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        party_id = self.request.query_params.get('party_id', None)
        if party_id is None:
            return Response({'succeess': False, 'message': 'Pass valid party id'})
        Party.objects.filter(id=party_id).delete()
        return Response(delete_success())


class ItemView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        return Response(fetch_success(ItemSerializer(Item.objects.filter(user=request.user.company if request.user.company else request.user), many=True).data))

    def post(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        item = Item(**request.data, user=request.user.company if request.user.company else request.user)
        item.save()
        return Response(save_success())

    def put(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Item.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        item_id = self.request.query_params.get('item_id', None)
        if item_id is None:
            return Response({'succeess': False, 'message': 'Pass valid item id'})
        Item.objects.filter(id=item_id).delete()
        return Response(delete_success())


class RateView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        return Response(fetch_success(RateSerializer(Rate.objects.filter(user=request.user.company if request.user.company else request.user).first()).data))

    def post(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        rate = Rate(**request.data, user=request.user.company if request.user.company else request.user)
        rate.save()
        return Response(save_success())

    def put(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        Rate.objects.filter(id=data['id']).update(**data)
        return Response(edit_success())

    def delete(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        rate_id = self.request.query_params.get('rate_id', None)
        if rate_id is None:
            return Response({'succeess': False, 'message': 'Pass valid rate id'})
        Rate.objects.filter(id=rate_id).delete()
        return Response(delete_success())


class OrderView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        order_id = self.request.query_params.get('order_id', None)
        date = self.request.query_params.get('date', None)
        if order_id is not None:
            return Response(fetch_success(OrderSerializer(Order.objects.get(id=order_id)).data))
        return Response(fetch_success(OrderListSerializer(Order.objects.filter(date=date), many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        reciever = Party.objects.get(id=data['order']['reciever_id'])
        order = Order.objects.create(
            **data['order'], reciever=reciever, user=request.user.company if request.user.company else request.user)
        for data in data['order_item']:
            item = Item.objects.get(id=data['item_id'])
            supplier = Party.objects.get(id=data['supplier_id'])
            OrderItem.objects.create(
                **data, order=order, item=item, supplier=supplier)
        return Response(save_success())

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        order = Order.objects.get(id=data['order']['id'])
        Order.objects.filter(id=data['order']['id']).update(**data['order'])
        for data in data['order_item']:
            if 'id' in data.keys():
                if data['id'] is not None:
                    OrderItem.objects.filter(id=data['id']).update(**data)
            if 'item_id' in data.keys():
                if data['item_id'] is not None:
                    item = Item.objects.get(id=data['item_id'])
                    supplier = Party.objects.get(id=data['supplier_id'])
                    OrderItem.objects.create(
                        **data, order=order, item=item, supplier=supplier)
        OrderItem.objects.filter(order=order, box=0, pcs=0, crate=0).delete()
        return Response(edit_success())

    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        order_id = self.request.query_params.get('order_id', None)
        if order_id is None:
            return Response({'succeess': False, 'message': 'Pass valid order id'})
        Order.objects.filter(id=order_id).delete()
        return Response(delete_success())


class InvoiceView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        invoice_id = self.request.query_params.get('invoice_id', None)
        date = self.request.query_params.get('date', None)
        if invoice_id is not None:
            return Response(fetch_success(InvoiceSerializer(Invoice.objects.get(id=invoice_id)).data))
        return Response(fetch_success(InvoiceListSerializer(Invoice.objects.filter(date=date), many=True).data))

    def post(self, request, format=None):
        if request.user.is_anonymous:
            return Response({'error': 'Invalid user'}, status=401)

        data = request.data.copy()  # Make a mutable copy
        invoice_data = data['invoice']
        
        # Fetch related objects
        invoice_data['reciever'] = get_object_or_404(Party, id=invoice_data['reciever_id'])
        invoice_data['sender'] = get_object_or_404(Party, id=invoice_data['sender_id'])
        invoice_data['user'] = request.user.company if request.user.company else request.user

        if 'sales_person_id' in invoice_data:
            invoice_data['sales_person'] = get_object_or_404(User, id=invoice_data['sales_person_id'])

        # Create Invoice
        with transaction.atomic():  # Ensure atomicity
            invoice = Invoice.objects.create(**invoice_data)

            for item_data in data['invoice_item']:
                item = get_object_or_404(Item, id=item_data['item_id'])
                supplier = get_object_or_404(Party, id=item_data['supplier_id'])

                InvoiceItem.objects.create(
                    invoice=invoice,
                    item=item,
                    supplier=supplier,
                    qty=item_data.get('qty', 1),  # Default to 1 if qty is missing
                    rate=item_data.get('rate', 0),  # Default to 0 if rate is missing
                )

        return Response({'message': 'Invoice saved successfully'}, status=201)

    def put(self, request, format=None):
        if request.user.is_anonymous:
            return Response({'error': 'Invalid user'}, status=401)

        data = request.data.copy()  # Make a mutable copy
        invoice_data = data['invoice']

        # Fetch existing invoice
        invoice = get_object_or_404(Invoice, id=invoice_data['id'])
        
        if 'sales_person_id' in invoice_data:
            invoice_data['sales_person'] = get_object_or_404(User, id=invoice_data['sales_person_id'])

        # Update Invoice
        Invoice.objects.filter(id=invoice.id).update(**invoice_data)
        print(invoice_data['sales_person'])
        # Process Invoice Items
        with transaction.atomic():
            for item_data in data['invoice_item']:
                if 'id' in item_data and item_data['id']:
                    InvoiceItem.objects.filter(id=item_data['id']).update(**item_data)
                else:
                    item = get_object_or_404(Item, id=item_data['item_id'])
                    supplier = get_object_or_404(Party, id=item_data['supplier_id'])

                    InvoiceItem.objects.create(
                        invoice=invoice,
                        item=item,
                        supplier=supplier,
                        qty=item_data.get('qty', 1),
                        rate=item_data.get('rate', 0),
                    )

        # Remove items with qty=0
        InvoiceItem.objects.filter(invoice=invoice, qty=0).delete()

        return Response({'message': 'Invoice updated successfully'}, status=200)


class InvoiceImageView(APIView):
    """API view for handling invoice images"""
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    queryset = InvoiceImage.objects.all()
    
    def get(self, request, format=None):
        """Get images for a specific invoice"""
        if request.user.is_anonymous:
            return Response(invalid_user)
        
        invoice_id = request.query_params.get('invoice_id')
        if not invoice_id:
            return Response({'error': 'Invoice ID is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            invoice = SalesInvoice.objects.get(
                id=invoice_id, 
                user=request.user.company if request.user.company else request.user
            )
            images = InvoiceImage.objects.filter(sales_invoice=invoice)
            serializer = InvoiceImageSerializer(images, many=True, context={'request': request})
            return Response(fetch_success(serializer.data))
        except SalesInvoice.DoesNotExist:
            return Response({'error': 'Invoice not found'}, status=status.HTTP_404_NOT_FOUND)
    
    def post(self, request, format=None):
        """Upload an image for an invoice"""
        if request.user.is_anonymous:
            return Response(invalid_user)
        
        invoice_id = request.data.get('invoice_id')
        image_file = request.FILES.get('image')
        description = request.data.get('description', '')
        
        if not invoice_id or not image_file:
            return Response({'error': 'Invoice ID and image file are required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            invoice = SalesInvoice.objects.get(
                id=invoice_id, 
                user=request.user.company if request.user.company else request.user
            )
            
            # Validate file size (5MB limit)
            if image_file.size > 5 * 1024 * 1024:
                return Response({'error': 'Image size should be less than 5MB'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate file type
            if not image_file.content_type.startswith('image/'):
                return Response({'error': 'Please upload a valid image file'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Create invoice image
            invoice_image = InvoiceImage.objects.create(
                sales_invoice=invoice,
                image=image_file,
                filename=image_file.name,
                file_size=image_file.size,
                file_type=image_file.content_type,
                description=description,
                uploaded_by=request.user
            )
            
            serializer = InvoiceImageSerializer(invoice_image, context={'request': request})
            return Response(save_success(serializer.data))
            
        except SalesInvoice.DoesNotExist:
            return Response({'error': 'Invoice not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, format=None):
        """Delete an invoice image"""
        if request.user.is_anonymous:
            return Response(invalid_user)
        
        image_id = request.query_params.get('image_id')
        if not image_id:
            return Response({'error': 'Image ID is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            image = InvoiceImage.objects.get(
                id=image_id,
                sales_invoice__user=request.user.company if request.user.company else request.user
            )
            image.delete()
            return Response(delete_success())
        except InvoiceImage.DoesNotExist:
            return Response({'error': 'Image not found'}, status=status.HTTP_404_NOT_FOUND)
        
    def delete(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        invoice_id = self.request.query_params.get('invoice_id', None)
        if invoice_id is None:
            return Response({'succeess': False, 'message': 'Pass valid order id'})
        Invoice.objects.filter(id=invoice_id).delete()
        return Response(delete_success())


class ExpenseCategoryView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        return Response(fetch_success(ExpenseCategorySerializer(ExpenseCategory.objects.filter(user=request.user.company if request.user.company else request.user).get_active_items(), many=True).data))


    def post(self,request,format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        serializer = ExpenseCategorySerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user=request.user.company if request.user.company else request.user)
            return Response(save_success(serializer.data))
        return Response({'succeess': False, 'message':  serializer_msg(serializer.errors)})


    def put(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        try:
            item = ExpenseCategory.objects.get(id=data['id'])
        except ExpenseCategory.DoesNotExist:
            return Response({'succeess': False, 'message':  "Item not found"})


        serializer = ExpenseCategorySerializer(item, data=data)
        if serializer.is_valid():
            serializer.save()
            return Response(edit_success(serializer.data))
        return Response({'succeess': False, 'message':  serializer_msg(serializer.errors)})



    def delete(self,request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        category_id = self.request.query_params.get('category_id', None)
        try:
            item = ExpenseCategory.objects.get(id=category_id)
        except ExpenseCategory.DoesNotExist:
            return Response({'succeess': False, 'message':  "Item not found"})

        ExpenseCategory.objects.filter(id=category_id).delete()
        return Response(delete_success())


class ExpenseSubCategoryView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        return Response(fetch_success(ExpenseCategorySerializer(SubCategory.objects.filter(category__id=self.request.query_params.get('category',None)), many=True).data))


    def post(self,request,format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        serializer = SubCategorySerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(save_success(serializer.data))
        return Response({'succeess': False, 'message':  serializer_msg(serializer.errors)})



    def put(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        try:
            item = SubCategory.objects.get(id=data['id'])
        except SubCategory.DoesNotExist:
            return Response({'succeess': False, 'message':  "Item not found"})


        serializer = SubCategorySerializer(item, data=data)
        if serializer.is_valid():
            serializer.save()
            return Response(edit_success(serializer.data))
        return Response({'succeess': False, 'message':  serializer_msg(serializer.errors)})


    def delete(self,request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        category_id = self.request.query_params.get('category_id', None)
        try:
            item = SubCategory.objects.get(id=category_id)
        except SubCategory.DoesNotExist:
            return Response({'succeess': False, 'message':  "Item not found"})

        SubCategory.objects.filter(id=category_id).delete()
        return Response(delete_success())


class ExpenseView(APIView):
    authentication_classes = [TokenAuthentication]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)
        from_date = self.request.query_params.get('from_date', timezone.now() - timedelta(days=30))
        to_date = self.request.query_params.get('to_date', timezone.now())
        return Response(fetch_success(ExpenseListSerializer(Expense.objects.filter(created_at__lte=to_date, created_at__gte=from_date,user=request.user.company if request.user.company else request.user), many=True).data))


    def post(self, request):
        serializer = ExpenseSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user=request.user.company if request.user.company else request.user)
            return Response(save_success())
        return Response({'succeess': False, 'message':  serializer_msg(serializer.errors)})


    def put(self, request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        data = request.data
        try:
            item = Expense.objects.get(id=data['id'])
        except Expense.DoesNotExist:
            return Response({'succeess': False, 'message':  "Item not found"})

        serializer = ExpenseSerializer(item, data=data)
        if serializer.is_valid():
            serializer.save()
            return Response(edit_success())
        return Response({'succeess': False, 'message':  serializer_msg(serializer.errors)})





    def delete(self,request):
        if request.user.is_anonymous:
            return Response(invalid_user)
        expense_id = self.request.query_params.get('expense_id', None)
        try:
            item = Expense.objects.get(id=expense_id)
        except Expense.DoesNotExist:
            return Response({'succeess': False, 'message':  "Item not found"})

        Expense.objects.filter(id=expense_id).delete()
        return Response(delete_success())


class ProductSummaryView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        user = request.user.company if request.user.company else request.user

        # Get global product summary (independent of any filters)
        try:
            total_products = Product.objects.filter(user=user).count()
            active_products = Product.objects.filter(user=user, active=True).count()
            inactive_products = total_products - active_products

            # Get products by category or other groupings if needed
            # This is a placeholder - you can expand based on your needs

            summary_data = {
                'total_products': total_products,
                'active_products': active_products,
                'inactive_products': inactive_products,
                'generated_at': timezone.now().isoformat()
            }

            return Response(fetch_success(summary_data))

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error calculating product summary: {str(e)}'
            }, status=500)


class BuyerSummaryView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        user = request.user.company if request.user.company else request.user

        # Get global buyer summary (independent of any filters)
        try:
            total_buyers = Buyer.objects.filter(user=user).count()
            active_buyers = Buyer.objects.filter(user=user, active=True).count()
            inactive_buyers = total_buyers - active_buyers

            # Outstanding balances
            total_receivables = Buyer.objects.filter(user=user).aggregate(
                total=Sum('current_balance'))['total'] or 0

            summary_data = {
                'total_buyers': total_buyers,
                'active_buyers': active_buyers,
                'inactive_buyers': inactive_buyers,
                'total_receivables': float(total_receivables),
                'generated_at': timezone.now().isoformat()
            }

            return Response(fetch_success(summary_data))

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error calculating buyer summary: {str(e)}'
            }, status=500)


class DashboardView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        # Check if this is a sales trend by brand request
        sales_trend_by_brand = request.query_params.get('sales_trend_by_brand', None)
        if sales_trend_by_brand:
            return self.get_sales_trend_by_brand(request)

        user = request.user.company if request.user.company else request.user

        # Calculate global summary metrics (independent of any filters)
        try:
            # Product metrics
            total_products = Product.objects.filter(user=user).count()
            active_products = Product.objects.filter(user=user, active=True).count()

            # Customer/Buyer metrics
            total_buyers = Buyer.objects.filter(user=user).count()
            active_buyers = Buyer.objects.filter(user=user, active=True).count()

            # Supplier metrics
            total_suppliers = Suplier.objects.filter(user=user).count()
            active_suppliers = Suplier.objects.filter(user=user, active=True).count()

            # Get current financial year dates
            financial_year_start, financial_year_end = get_current_financial_year_dates()
            
            # Financial metrics - current financial year
            total_sales_revenue = SalesInvoice.objects.filter(
                user=user, 
                date__gte=financial_year_start, 
                date__lte=financial_year_end
            ).aggregate(total=Sum('bill_amount'))['total'] or 0
            
            total_purchase_amount = PurchaseInvoice.objects.filter(
                user=user, 
                date__gte=financial_year_start, 
                date__lte=financial_year_end
            ).aggregate(total=Sum('bill_amount'))['total'] or 0
            
            total_expenses = Expense.objects.filter(
                user=user, 
                created_at__gte=financial_year_start, 
                created_at__lte=financial_year_end
            ).aggregate(total=Sum('amount'))['total'] or 0

            # Current month metrics
            current_month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            monthly_sales = SalesInvoice.objects.filter(
                user=user, date__gte=current_month_start).aggregate(
                total=Sum('bill_amount'))['total'] or 0
            monthly_purchases = PurchaseInvoice.objects.filter(
                user=user, date__gte=current_month_start).aggregate(
                total=Sum('bill_amount'))['total'] or 0
            monthly_expenses = Expense.objects.filter(
                user=user, created_at__gte=current_month_start).aggregate(
                total=Sum('amount'))['total'] or 0

            # Outstanding balances
            total_receivables = Buyer.objects.filter(user=user).aggregate(
                total=Sum('current_balance'))['total'] or 0
            total_payables = Suplier.objects.filter(user=user).aggregate(
                total=Sum('current_balance'))['total'] or 0

            # Recent activity counts (last 30 days)
            thirty_days_ago = timezone.now() - timedelta(days=30)
            recent_sales_count = SalesInvoice.objects.filter(
                user=user, date__gte=thirty_days_ago).count()
            recent_purchase_count = PurchaseInvoice.objects.filter(
                user=user, date__gte=thirty_days_ago).count()

            # Top customers by sales amount (last 30 days)
            top_customers = SalesInvoice.objects.filter(
                user=user, date__gte=thirty_days_ago
            ).values('buyer__name').annotate(
                total_amount=Sum('bill_amount')
            ).order_by('-total_amount')[:5]

            # Low stock products (assuming products with no recent sales as low stock indicator)
            # This is a simplified approach - you might want to add actual stock fields
            products_with_recent_sales = SalesInvoiceItem.objects.filter(
                sales_invoice__user=user,
                sales_invoice__date__gte=thirty_days_ago
            ).values_list('product_id', flat=True).distinct()

            low_stock_count = Product.objects.filter(
                user=user, active=True
            ).exclude(id__in=products_with_recent_sales).count()

            dashboard_data = {
                'summary_metrics': {
                    'total_products': total_products,
                    'active_products': active_products,
                    'total_buyers': total_buyers,
                    'active_buyers': active_buyers,
                    'total_suppliers': total_suppliers,
                    'active_suppliers': active_suppliers,
                    'low_stock_items': low_stock_count
                },
                'financial_overview': {
                    'total_revenue': float(total_sales_revenue),
                    'total_purchases': float(total_purchase_amount),
                    'total_expenses': float(total_expenses),
                    'gross_profit': float(total_sales_revenue - total_purchase_amount),
                    'net_profit': float(total_sales_revenue - total_purchase_amount - total_expenses),
                    'monthly_sales': float(monthly_sales),
                    'monthly_purchases': float(monthly_purchases),
                    'monthly_expenses': float(monthly_expenses),
                    'monthly_profit': float(monthly_sales - monthly_purchases - monthly_expenses),
                    'financial_year_start': financial_year_start.isoformat(),
                    'financial_year_end': financial_year_end.isoformat(),
                    'financial_year_label': f"FY {financial_year_start.year}-{str(financial_year_end.year)[-2:]}"
                },
                'outstanding_balances': {
                    'total_receivables': float(total_receivables),
                    'total_payables': float(total_payables),
                    'net_position': float(total_receivables - total_payables)
                },
                'activity_summary': {
                    'recent_sales_count': recent_sales_count,
                    'recent_purchase_count': recent_purchase_count,
                    'total_transactions': recent_sales_count + recent_purchase_count
                },
                'top_customers': list(top_customers),
                'generated_at': timezone.now().isoformat()
            }

            return Response(fetch_success(dashboard_data))

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error calculating dashboard metrics: {str(e)}'
            }, status=500)

    def get_sales_trend_by_brand(self, request):
        """
        Get sales trend analytics grouped by brand with time period filtering
        """
        try:
            user = request.user.company if request.user.company else request.user

            # Get query parameters
            from_date = request.query_params.get('from_date', None)
            to_date = request.query_params.get('to_date', None)
            time_period = request.query_params.get('time_period', 'monthly')  # monthly, quarterly, yearly
            brand_ids = request.query_params.getlist('brand_ids', [])

            # Set default date range if not provided (last 6 months)
            if not from_date or not to_date:
                to_date = timezone.now().date()
                from_date = to_date - timedelta(days=180)  # 6 months
            else:
                from_date = datetime.strptime(from_date, '%Y-%m-%d').date()
                to_date = datetime.strptime(to_date, '%Y-%m-%d').date()

            # Choose truncation function based on time period
            if time_period == 'quarterly':
                trunc_func = TruncQuarter
            elif time_period == 'yearly':
                trunc_func = TruncYear
            else:  # default to monthly
                trunc_func = TruncMonth

            # Base query for sales invoice items with brand information
            base_query = SalesInvoiceItem.objects.filter(
                sales_invoice__user=user,
                sales_invoice__date__gte=from_date,
                sales_invoice__date__lte=to_date,
                sales_invoice__invoice_status='billed',
                product__brand__isnull=False  # Only include products with brands
            ).select_related('product__brand', 'sales_invoice')

            # Filter by specific brands if provided
            if brand_ids:
                base_query = base_query.filter(product__brand__id__in=brand_ids)

            # Group by time period and brand, calculate metrics
            trend_data = base_query.annotate(
                period=trunc_func('sales_invoice__date')
            ).values(
                'period',
                'product__brand__id',
                'product__brand__name'
            ).annotate(
                total_sales_amount=Sum('line_total'),
                total_quantity=Sum('no'),
                total_pieces=Sum('weight'),
                transaction_count=Count('sales_invoice', distinct=True)
            ).order_by('period', 'product__brand__name')

            # Get brand summary data
            brand_summary = base_query.values(
                'product__brand__id',
                'product__brand__name'
            ).annotate(
                total_sales_amount=Sum('line_total'),
                total_quantity=Sum('no'),
                total_pieces=Sum('weight'),
                transaction_count=Count('sales_invoice', distinct=True)
            ).order_by('-total_sales_amount')

            # Calculate growth percentages for each brand
            brand_growth = {}
            for brand in brand_summary:
                brand_id = brand['product__brand__id']
                brand_periods = [item for item in trend_data if item['product__brand__id'] == brand_id]

                if len(brand_periods) >= 2:
                    # Sort by period to get chronological order
                    brand_periods.sort(key=lambda x: x['period'])
                    latest_period = brand_periods[-1]['total_sales_amount'] or 0
                    previous_period = brand_periods[-2]['total_sales_amount'] or 0

                    if previous_period > 0:
                        growth_percentage = ((latest_period - previous_period) / previous_period) * 100
                    else:
                        growth_percentage = 100 if latest_period > 0 else 0
                else:
                    growth_percentage = 0

                brand_growth[brand_id] = round(growth_percentage, 2)

            # Format the response data
            response_data = {
                'time_series_data': list(trend_data),
                'brand_summary': list(brand_summary),
                'brand_growth': brand_growth,
                'date_range': {
                    'from_date': from_date.isoformat(),
                    'to_date': to_date.isoformat(),
                    'time_period': time_period
                },
                'total_brands': len(brand_summary),
                'generated_at': timezone.now().isoformat()
            }

            return Response(fetch_success(response_data))

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error calculating sales trend by brand: {str(e)}'
            }, status=500)


class UserManagementView(APIView):
    """
    API View for Buyer Management based on purchasing patterns
    Determines buyer activity based on invoicing frequency rather than status flags
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    queryset = Buyer.objects.all()

    def get(self, request, format=None):
        """
        Get inactive buyers based on purchasing patterns
        """
        if request.user.is_anonymous:
            return Response(invalid_user)

        try:
            # Get the company user for filtering
            company_user = request.user.company if request.user.company else request.user

            # Get all buyers under this company
            buyers = Buyer.objects.filter(user=company_user,active=True)

            inactive_buyers = []

            for buyer in buyers:
                # Analyze purchasing patterns for this buyer
                buyer_analysis = self.analyze_buyer_purchasing_patterns(buyer, company_user)

                # If buyer is determined to be inactive based on purchasing patterns
                if not buyer_analysis['is_active']:
                    inactive_buyers.append({
                        'id': buyer.id,
                        'name': buyer.name,
                        'place': buyer.place,
                        'contact_person': buyer.contact_person,
                        'phone_no': buyer.phone_no,
                        'gst_no': buyer.gst_no,
                        'current_balance': buyer.current_balance,
                        'credit_limit': buyer.credit_limit,
                        'buyer_class': buyer.buyer_class.name if buyer.buyer_class else None,
                        'route': buyer.route.name if buyer.route else None,
                        'last_purchase_date': buyer_analysis['last_purchase_date'],
                        'expected_frequency': buyer_analysis['expected_frequency'],
                        'days_since_last_purchase': buyer_analysis['days_since_last_purchase'],
                        'total_purchases': buyer_analysis['total_purchases'],
                        'average_monthly_purchases': buyer_analysis['average_monthly_purchases'],
                        'total_purchase_amount': buyer_analysis['total_purchase_amount'],
                        'average_purchase_amount': buyer_analysis['average_purchase_amount'],
                        'is_purchasing': buyer_analysis['is_purchasing'],
                        'purchasing_status': buyer_analysis['purchasing_status'],
                        'frequency_analysis': buyer_analysis['frequency_analysis']
                    })

            return Response(fetch_success({
                'inactive_buyers': inactive_buyers,
                'total_inactive_count': len(inactive_buyers),
                'analysis_date': timezone.now().isoformat()
            }))

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error analyzing buyer management data: {str(e)}'
            }, status=500)

    def analyze_buyer_purchasing_patterns(self, buyer, company_user):
        """
        Analyze a buyer's purchasing patterns to determine activity status
        """
        now = timezone.now()

        # Get all sales invoices for this buyer from the company
        invoices = SalesInvoice.objects.filter(
            buyer=buyer,
            user=company_user
        ).order_by('-date')

        if not invoices.exists():
            return {
                'is_active': False,
                'last_purchase_date': None,
                'expected_frequency': 'unknown',
                'days_since_last_purchase': None,
                'total_purchases': 0,
                'average_monthly_purchases': 0,
                'total_purchase_amount': 0,
                'average_purchase_amount': 0,
                'is_purchasing': False,
                'purchasing_status': 'No purchase history',
                'frequency_analysis': 'No data available'
            }

        # Get the most recent invoice
        last_invoice = invoices.first()
        last_purchase_date = last_invoice.date
        days_since_last_purchase = (now.date() - last_purchase_date).days

        # Calculate total purchases and time span
        total_purchases = invoices.count()
        total_purchase_amount = sum(invoice.bill_amount for invoice in invoices)
        average_purchase_amount = total_purchase_amount / total_purchases if total_purchases > 0 else 0

        first_invoice = invoices.last()
        first_purchase_date = first_invoice.date

        # Calculate the span in months
        months_span = max(1, (last_purchase_date.year - first_purchase_date.year) * 12 +
                         (last_purchase_date.month - first_purchase_date.month) + 1)

        average_monthly_purchases = total_purchases / months_span

        # Determine expected frequency based on historical patterns
        expected_frequency = self.determine_expected_frequency(average_monthly_purchases)

        # Determine if buyer is currently active based on expected frequency
        is_active = self.is_buyer_currently_active(
            days_since_last_purchase,
            expected_frequency,
            average_monthly_purchases
        )

        # Generate purchasing status description
        purchasing_status = self.generate_purchasing_status(
            is_active,
            days_since_last_purchase,
            expected_frequency
        )

        # Generate frequency analysis
        frequency_analysis = self.generate_frequency_analysis(
            average_monthly_purchases,
            expected_frequency,
            total_purchases,
            months_span
        )

        return {
            'is_active': is_active,
            'last_purchase_date': last_purchase_date.isoformat() if last_purchase_date else None,
            'expected_frequency': expected_frequency,
            'days_since_last_purchase': days_since_last_purchase,
            'total_purchases': total_purchases,
            'average_monthly_purchases': round(average_monthly_purchases, 2),
            'total_purchase_amount': round(total_purchase_amount, 2),
            'average_purchase_amount': round(average_purchase_amount, 2),
            'is_purchasing': is_active,
            'purchasing_status': purchasing_status,
            'frequency_analysis': frequency_analysis
        }

    def determine_expected_frequency(self, average_monthly_invoices):
        """
        Determine expected invoicing frequency based on historical patterns
        """
        if average_monthly_invoices >= 4:  # 4+ invoices per month
            return 'weekly'
        elif average_monthly_invoices >= 1:  # 1-4 invoices per month
            return 'monthly'
        elif average_monthly_invoices >= 0.25:  # 1 invoice per quarter
            return 'quarterly'
        else:  # Less than quarterly
            return 'yearly'

    def is_buyer_currently_active(self, days_since_last_purchase, expected_frequency, average_monthly_purchases):
        """
        Determine if buyer is currently active based on their expected frequency
        """
        if expected_frequency == 'weekly':
            return days_since_last_purchase <= 14  # Allow 2 weeks grace period
        elif expected_frequency == 'monthly':
            return days_since_last_purchase <= 45  # Allow 1.5 months grace period
        elif expected_frequency == 'quarterly':
            return days_since_last_purchase <= 120  # Allow 4 months grace period
        elif expected_frequency == 'yearly':
            return days_since_last_purchase <= 450  # Allow 15 months grace period
        else:
            return False

    def generate_purchasing_status(self, is_active, days_since_last_purchase, expected_frequency):
        """
        Generate a human-readable purchasing status description
        """
        if is_active:
            return f"Active - purchasing {expected_frequency} as expected"
        else:
            if days_since_last_purchase is None:
                return "No purchase activity"
            elif days_since_last_purchase <= 7:
                return "Recently active"
            elif days_since_last_purchase <= 30:
                return f"Inactive for {days_since_last_purchase} days (expected {expected_frequency})"
            elif days_since_last_purchase <= 90:
                return f"Inactive for {days_since_last_purchase} days - may need attention"
            else:
                return f"Inactive for {days_since_last_purchase} days - requires immediate attention"

    def generate_frequency_analysis(self, average_monthly_purchases, expected_frequency, total_purchases, months_span):
        """
        Generate frequency analysis description
        """
        return (f"Averages {average_monthly_purchases:.1f} purchases per month over {months_span} months. "
                f"Total of {total_purchases} purchases. Expected frequency: {expected_frequency}.")


class PurchaseOrderView(APIView):
    """Purchase Order management API"""
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        po_id = self.request.query_params.get('po_id', None)
        status_filter = self.request.query_params.get('status', None)
        supplier_id = self.request.query_params.get('supplier_id', None)
        auto_generated = self.request.query_params.get('auto_generated', None)

        fetch_success_local = fetch_success()

        if po_id is not None:
            # Get specific purchase order
            try:
                po = PurchaseOrder.objects.get(
                    id=po_id,
                    user=request.user.company if request.user.company else request.user
                )
                fetch_success_local['data'] = PurchaseOrderSerializer(po).data

                # Include related data for editing
                suppliers = Suplier.objects.filter(
                    user=request.user.company if request.user.company else request.user,
                    active=True
                )
                products = Product.objects.filter(
                    user=request.user.company if request.user.company else request.user,
                    active=True
                )
                brands = Brand.objects.filter(
                    user=request.user.company if request.user.company else request.user
                )
                fetch_success_local['suppliers'] = SuplierSerializer(suppliers, many=True).data
                fetch_success_local['products'] = ProductSerializer(products, many=True).data
                fetch_success_local['brands'] = BrandSerializer(brands, many=True).data

            except PurchaseOrder.DoesNotExist:
                return Response({'error': 'Purchase Order not found'}, status=status.HTTP_404_NOT_FOUND)
        else:
            # List purchase orders with filters
            filter_params = {
                'user': request.user.company if request.user.company else request.user
            }

            if status_filter:
                filter_params['status'] = status_filter
            if supplier_id:
                filter_params['supplier_id'] = supplier_id
            if auto_generated is not None:
                filter_params['auto_generated'] = auto_generated.lower() == 'true'

            pos = PurchaseOrder.objects.filter(**filter_params).order_by('-created_date')
            fetch_success_local['data'] = PurchaseOrderListSerializer(pos, many=True).data

            # Include summary statistics
            fetch_success_local['summary'] = {
                'total_pos': pos.count(),
                'draft_count': pos.filter(status='draft').count(),
                'approved_count': pos.filter(status='approved').count(),
                'pending_approval_count': pos.filter(status='pending_approval').count(),
            }

        return Response(fetch_success_local)

    def post(self, request, format=None):
        """Create new purchase order"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data

        try:
            with transaction.atomic():
                # Get supplier
                supplier = Suplier.objects.get(
                    id=data['supplier_id'],
                    user=request.user.company if request.user.company else request.user
                )

                # Create purchase order
                po_data = {
                    'user': request.user.company if request.user.company else request.user,
                    'supplier': supplier,
                    'expected_delivery_date': data.get('expected_delivery_date'),
                    'remarks': data.get('remarks', ''),
                    'terms_and_conditions': data.get('terms_and_conditions', ''),
                    'auto_generated': data.get('auto_generated', False)
                }

                po = PurchaseOrder.objects.create(**po_data)

                # Add items
                total_amount = 0
                tax_amount = 0

                for item_data in data.get('items', []):
                    product = Product.objects.get(id=item_data['product_id'])

                    item = PurchaseOrderItem.objects.create(
                        purchase_order=po,
                        product=product,
                        ordered_quantity=item_data['ordered_quantity'],
                        box_quantity=item_data.get('box_quantity', 0),
                        pieces_quantity=item_data.get('pieces_quantity', 0),
                        unit_rate=item_data.get('unit_rate', product.pr_rate or 0),
                        tax_rate=item_data.get('tax_rate', product.tax_rate or 0),
                        forecast_quantity=item_data.get('forecast_quantity'),
                        current_stock=item_data.get('current_stock'),
                        min_stock_threshold=item_data.get('min_stock_threshold'),
                        reorder_reason=item_data.get('reorder_reason', '')
                    )

                    total_amount += item.line_total
                    tax_amount += item.tax_amount

                # Update PO totals
                po.total_amount = total_amount
                po.tax_amount = tax_amount
                po.net_amount = total_amount + tax_amount
                po.save()

                return Response(save_success({'po_id': po.id, 'po_number': po.po_number}))

        except Suplier.DoesNotExist:
            return Response({'error': 'Supplier not found'}, status=status.HTTP_404_NOT_FOUND)
        except Product.DoesNotExist:
            return Response({'error': 'Product not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, format=None):
        """Update purchase order"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        po_id = data.get('id')

        if not po_id:
            return Response({'error': 'Purchase Order ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                po = PurchaseOrder.objects.get(
                    id=po_id,
                    user=request.user.company if request.user.company else request.user
                )

                # Update PO fields
                po.expected_delivery_date = data.get('expected_delivery_date', po.expected_delivery_date)
                po.remarks = data.get('remarks', po.remarks)
                po.terms_and_conditions = data.get('terms_and_conditions', po.terms_and_conditions)
                po.status = data.get('status', po.status)

                # Handle approval
                if data.get('status') == 'approved' and po.status != 'approved':
                    po.approved_by = request.user
                    po.approved_date = timezone.now()

                po.save()

                # Update items if provided
                if 'items' in data:
                    # Clear existing items
                    po.items.all().delete()

                    total_amount = 0
                    tax_amount = 0

                    for item_data in data['items']:
                        product = Product.objects.get(id=item_data['product_id'])

                        item = PurchaseOrderItem.objects.create(
                            purchase_order=po,
                            product=product,
                            ordered_quantity=item_data['ordered_quantity'],
                            box_quantity=item_data.get('box_quantity', 0),
                            pieces_quantity=item_data.get('pieces_quantity', 0),
                            unit_rate=item_data.get('unit_rate', product.pr_rate or 0),
                            tax_rate=item_data.get('tax_rate', product.tax_rate or 0),
                            forecast_quantity=item_data.get('forecast_quantity'),
                            current_stock=item_data.get('current_stock'),
                            min_stock_threshold=item_data.get('min_stock_threshold'),
                            reorder_reason=item_data.get('reorder_reason', '')
                        )

                        total_amount += item.line_total
                        tax_amount += item.tax_amount

                    # Update PO totals
                    po.total_amount = total_amount
                    po.tax_amount = tax_amount
                    po.net_amount = total_amount + tax_amount
                    po.save()

                return Response(edit_success())

        except PurchaseOrder.DoesNotExist:
            return Response({'error': 'Purchase Order not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, format=None):
        """Delete purchase order"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        po_id = data.get('id')

        if not po_id:
            return Response({'error': 'Purchase Order ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            po = PurchaseOrder.objects.get(
                id=po_id,
                user=request.user.company if request.user.company else request.user
            )

            # Only allow deletion of draft orders
            if po.status not in ['draft', 'cancelled']:
                return Response({
                    'error': 'Only draft or cancelled purchase orders can be deleted'
                }, status=status.HTTP_400_BAD_REQUEST)

            po.delete()
            return Response(delete_success())

        except PurchaseOrder.DoesNotExist:
            return Response({'error': 'Purchase Order not found'}, status=status.HTTP_404_NOT_FOUND)


class PurchaseOrderDraftView(APIView):
    """Purchase Order Draft management API"""
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        draft_id = self.request.query_params.get('draft_id', None)

        if draft_id is not None:
            try:
                draft = PurchaseOrderDraft.objects.get(
                    id=draft_id,
                    user=request.user.company if request.user.company else request.user
                )
                return Response(fetch_success(PurchaseOrderDraftSerializer(draft).data))
            except PurchaseOrderDraft.DoesNotExist:
                return Response({'error': 'Draft not found'}, status=status.HTTP_404_NOT_FOUND)
        else:
            drafts = PurchaseOrderDraft.objects.filter(
                user=request.user.company if request.user.company else request.user
            ).order_by('-created_on')
            return Response(fetch_success(PurchaseOrderDraftSerializer(drafts, many=True).data))

    def post(self, request, format=None):
        """Convert draft to purchase order"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        draft_id = data.get('draft_id')

        if not draft_id:
            return Response({'error': 'Draft ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                draft = PurchaseOrderDraft.objects.get(
                    id=draft_id,
                    user=request.user.company if request.user.company else request.user
                )

                # Create purchase order from draft
                po = PurchaseOrder.objects.create(
                    user=draft.user,
                    supplier=draft.supplier,
                    auto_generated=draft.auto_generated,
                    remarks=f"Generated from draft {draft.id}",
                    expected_delivery_date=data.get('expected_delivery_date')
                )

                total_amount = 0
                tax_amount = 0

                # Convert draft items to PO items
                for draft_item in draft.items.all():
                    item = PurchaseOrderItem.objects.create(
                        purchase_order=po,
                        product=draft_item.product,
                        ordered_quantity=draft_item.quantity,
                        unit_rate=draft_item.estimated_rate,
                        tax_rate=draft_item.product.tax_rate or 0,
                        forecast_quantity=draft_item.forecast_quantity,
                        current_stock=draft_item.current_stock,
                        min_stock_threshold=draft_item.min_stock_threshold,
                        reorder_reason=draft_item.reorder_reason
                    )

                    total_amount += item.line_total
                    tax_amount += item.tax_amount

                # Update PO totals
                po.total_amount = total_amount
                po.tax_amount = tax_amount
                po.net_amount = total_amount + tax_amount
                po.save()

                # Delete the draft
                draft.delete()

                return Response(save_success({
                    'po_id': po.id,
                    'po_number': po.po_number,
                    'message': 'Purchase Order created successfully from draft'
                }))

        except PurchaseOrderDraft.DoesNotExist:
            return Response({'error': 'Draft not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, format=None):
        """Delete purchase order draft"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        draft_id = data.get('id')

        if not draft_id:
            return Response({'error': 'Draft ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            draft = PurchaseOrderDraft.objects.get(
                id=draft_id,
                user=request.user.company if request.user.company else request.user
            )
            draft.delete()
            return Response(delete_success())

        except PurchaseOrderDraft.DoesNotExist:
            return Response({'error': 'Draft not found'}, status=status.HTTP_404_NOT_FOUND)


class SupplierProductsView(APIView):
    """API to get products filtered by supplier brands"""
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        supplier_id = self.request.query_params.get('supplier_id', None)

        if not supplier_id:
            return Response({'error': 'supplier_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get supplier brands
            supplier_brands = SupplierBrand.objects.filter(
                supplier_id=supplier_id,
                user=request.user.company if request.user.company else request.user,
                is_active=True
            ).values_list('brand_id', flat=True)

            if not supplier_brands:
                # If no brands are associated, return empty list
                return Response(fetch_success({
                    'products': [],
                    'brands': [],
                    'message': 'No brands associated with this supplier'
                }))

            # Get products that belong to supplier's brands
            products = Product.objects.filter(
                user=request.user.company if request.user.company else request.user,
                brand_id__in=supplier_brands,
                active=True
            ).order_by('sort_order')

            # Get the brands for reference
            brands = Brand.objects.filter(
                id__in=supplier_brands,
                user=request.user.company if request.user.company else request.user
            )

            return Response(fetch_success({
                'products': ProductSerializer(products, many=True).data,
                'brands': BrandSerializer(brands, many=True).data,
                'supplier_id': supplier_id
            }))

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BuyerProductsView(APIView):
    """API to get products filtered by buyer brands"""
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        if request.user.is_anonymous:
            return Response(invalid_user)

        buyer_id = self.request.query_params.get('buyer_id', None)
        all_products = self.request.query_params.get('all_products', 'false').lower() == 'true'

        if not buyer_id:
            return Response({'error': 'buyer_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            if all_products:
                # Return all brands and all products for the user
                products = Product.objects.filter(
                    user=request.user.company if request.user.company else request.user,
                    active=True
                ).order_by('sort_order')
                brands = Brand.objects.filter(
                    user=request.user.company if request.user.company else request.user
                )
                return Response(fetch_success({
                    'products': ProductSerializer(products, many=True).data,
                    'brands': BrandSerializer(brands, many=True).data,
                    'buyer_id': buyer_id,
                    'message': 'All products and brands for this user'
                }))

            # Get buyer brands
            buyer_brands = BuyerBrand.objects.filter(
                buyer_id=buyer_id,
                user=request.user.company if request.user.company else request.user,
                is_active=True
            ).values_list('brand_id', flat=True)

            if not buyer_brands:
                # If no brands are associated, return all brands and products
                buyer = Buyer.objects.get(id=buyer_id)
                buyer_class_margin_subquery = BuyerClassMargin.objects.filter(
                    product=OuterRef('id'),
                    product__user=request.user.company if request.user.company else request.user,
                    buyer_class=buyer.buyer_class
                ).values('margin')[:1]
                products = Product.objects.filter(
                    user=request.user.company if request.user.company else request.user,
                    active=True
                ).annotate(
                    final_margin=Coalesce(Subquery(buyer_class_margin_subquery, output_field=FloatField()), F('margin'))
                ).order_by('sort_order')
                brands = Brand.objects.filter(
                    user=request.user.company if request.user.company else request.user
                )
                return Response(fetch_success({
                    'products': ProductSerializer(products, many=True).data,
                    'brands': BrandSerializer(brands, many=True).data,
                    'buyer_id': buyer_id,
                    'message': 'No brands associated with this buyer, showing all products'
                }))

            # Get products that belong to buyer's brands
            products = Product.objects.filter(
                user=request.user.company if request.user.company else request.user,
                brand_id__in=buyer_brands,
                active=True
            ).order_by('sort_order')
            brands = Brand.objects.filter(
                id__in=buyer_brands,
                user=request.user.company if request.user.company else request.user
            )
            return Response(fetch_success({
                'products': ProductSerializer(products, many=True).data,
                'brands': BrandSerializer(brands, many=True).data,
                'buyer_id': buyer_id
            }))
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChecklistView(APIView):
    """
    API View for Checklist management
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    queryset = Checklist.objects.all()

    def get(self, request, format=None):
        """Get checklists for the authenticated user"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        company_user = request.user.company if request.user.company else request.user
        status_filter = request.query_params.get('status', None)
        route_filter = request.query_params.get('route_id', None)
        buyer_filter = request.query_params.get('buyer_id', None)

        checklists = Checklist.objects.filter(user=company_user)

        if status_filter:
            checklists = checklists.filter(status=status_filter)
        if route_filter:
            checklists = checklists.filter(route_id=route_filter)
        if buyer_filter:
            checklists = checklists.filter(buyer_id=buyer_filter)

        checklists = checklists.order_by('-created_at')

        return Response(fetch_success(ChecklistSerializer(checklists, many=True).data))

    def post(self, request, format=None):
        """Create a new checklist"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data.copy()
        company_user = request.user.company if request.user.company else request.user
        data['user'] = company_user.id

        serializer = ChecklistSerializer(data=data)
        if serializer.is_valid():
            checklist = serializer.save(user=company_user)

            # Create checklist items if provided
            items_data = request.data.get('items', [])
            for item_data in items_data:
                item_data['checklist'] = checklist.id
                item_serializer = ChecklistItemSerializer(data=item_data)
                if item_serializer.is_valid():
                    item_serializer.save()

            return Response(save_success())
        return Response({'success': False, 'message': serializer_msg(serializer.errors)})

    def put(self, request, format=None):
        """Update an existing checklist"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        try:
            checklist = Checklist.objects.get(id=data['id'])
        except Checklist.DoesNotExist:
            return Response({'success': False, 'message': "Checklist not found"})

        serializer = ChecklistSerializer(checklist, data=data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(edit_success())
        return Response({'success': False, 'message': serializer_msg(serializer.errors)})

    def delete(self, request, format=None):
        """Delete a checklist"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        try:
            checklist = Checklist.objects.get(id=data['id'])
            checklist.delete()
            return Response(delete_success())
        except Checklist.DoesNotExist:
            return Response({'success': False, 'message': "Checklist not found"})


class ChecklistItemView(APIView):
    """
    API View for Checklist Item management
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, format=None):
        """Create or update checklist item"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data

        # Handle image upload
        if 'image' in request.FILES:
            data = data.copy()
            data['image'] = request.FILES['image']

        if 'id' in data and data['id']:
            # Update existing item
            try:
                item = ChecklistItem.objects.get(id=data['id'])
                serializer = ChecklistItemSerializer(item, data=data, partial=True)
            except ChecklistItem.DoesNotExist:
                return Response({'success': False, 'message': "Checklist item not found"})
        else:
            # Create new item
            serializer = ChecklistItemSerializer(data=data)

        if serializer.is_valid():
            item = serializer.save()

            # Update completion time if item is marked as completed
            if data.get('is_completed') and not item.completed_at:
                from django.utils import timezone
                item.completed_at = timezone.now()
                item.save()

            return Response(save_success())
        return Response({'success': False, 'message': serializer_msg(serializer.errors)})

    def put(self, request, format=None):
        """Update checklist item completion status"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        try:
            item = ChecklistItem.objects.get(id=data['id'])

            # Update completion status
            item.is_completed = data.get('is_completed', item.is_completed)
            item.notes = data.get('notes', item.notes)

            if item.is_completed and not item.completed_at:
                from django.utils import timezone
                item.completed_at = timezone.now()
            elif not item.is_completed:
                item.completed_at = None

            item.save()

            # Update parent checklist status if all mandatory items are completed
            checklist = item.checklist
            mandatory_items = checklist.items.filter(is_mandatory=True)
            completed_mandatory = mandatory_items.filter(is_completed=True)

            if mandatory_items.count() > 0 and mandatory_items.count() == completed_mandatory.count():
                if checklist.status == 'pending':
                    checklist.status = 'in_progress'
                    checklist.save()

            return Response(edit_success())
        except ChecklistItem.DoesNotExist:
            return Response({'success': False, 'message': "Checklist item not found"})


class RouteBillingSummaryView(APIView):
    """
    API View for Route Billing Summary management
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        """Get route billing summaries"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        company_user = request.user.company if request.user.company else request.user
        date_filter = request.query_params.get('date', None)
        route_filter = request.query_params.get('route_id', None)
        weekday_filter = request.query_params.get('weekday', None)

        # Default to today if no date provided
        if not date_filter:
            from datetime import date
            date_filter = date.today().strftime('%Y-%m-%d')

        # Get or create billing summaries for the date
        routes = Route.objects.filter(user=company_user)

        if route_filter:
            routes = routes.filter(id=route_filter)

        summaries = []
        for route in routes:
            # Check if we should include this route based on weekday filter
            if weekday_filter:
                # Check if route has a schedule for the requested weekday
                has_schedule = RouteSchedule.objects.filter(
                    route=route,
                    weekday=weekday_filter,
                    is_active=True
                ).exists()
                if not has_schedule:
                    continue

            summary, created = RouteBillingSummary.objects.get_or_create(
                route=route,
                date=date_filter,
                defaults={'total_shops': 0, 'billed_shops': 0, 'unbilled_shops': 0}
            )

            # Recalculate summary
            summary.calculate_summary()
            summaries.append(summary)

        return Response(fetch_success(RouteBillingSummarySerializer(summaries, many=True).data))

    def post(self, request, format=None):
        """Manually refresh billing summary for a route and date"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        route_id = data.get('route_id')
        date_str = data.get('date')

        if not route_id or not date_str:
            return Response({'success': False, 'message': 'Route ID and date are required'})

        try:
            route = Route.objects.get(id=route_id)
            summary, created = RouteBillingSummary.objects.get_or_create(
                route=route,
                date=date_str,
                defaults={'total_shops': 0, 'billed_shops': 0, 'unbilled_shops': 0}
            )

            # Recalculate summary
            summary.calculate_summary()

            return Response(save_success({'summary': RouteBillingSummarySerializer(summary).data}))
        except Route.DoesNotExist:
            return Response({'success': False, 'message': 'Route not found'})


class ShopFreezerPhotoView(APIView):
    """
    API View for Shop Freezer Photo management
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        """Get freezer photos"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        company_user = request.user.company if request.user.company else request.user
        route_filter = request.query_params.get('route_id', None)
        buyer_filter = request.query_params.get('buyer_id', None)
        date_filter = request.query_params.get('date', None)

        # Filter out photos older than 30 days
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now().date() - timedelta(days=30)

        photos = ShopFreezerPhoto.objects.filter(
            buyer__user=company_user,
            date_taken__gte=thirty_days_ago  # Only photos from last 30 days
        ).order_by('-date_taken', '-time_taken')

        if route_filter:
            photos = photos.filter(route_id=route_filter)
        if buyer_filter:
            photos = photos.filter(buyer_id=buyer_filter)
        if date_filter:
            photos = photos.filter(date_taken=date_filter)

        return Response(fetch_success(ShopFreezerPhotoSerializer(photos, many=True).data))

    def post(self, request, format=None):
        """Upload freezer photo"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data

        # Validate required fields
        if 'buyer_id' not in data or 'route_id' not in data or 'photo' not in request.FILES:
            return Response({'success': False, 'message': 'Buyer ID, Route ID, and photo are required'})

        try:
            buyer = Buyer.objects.get(id=data['buyer_id'])
            route = Route.objects.get(id=data['route_id'])

            # Create freezer photo record
            photo = ShopFreezerPhoto.objects.create(
                buyer=buyer,
                route=route,
                photo=request.FILES['photo'],
                notes=data.get('notes', ''),
                uploaded_by=request.user,
                location_latitude=data.get('location_latitude'),
                location_longitude=data.get('location_longitude')
            )

            return Response(save_success({'photo_id': photo.id}))

        except Buyer.DoesNotExist:
            return Response({'success': False, 'message': 'Buyer not found'})
        except Route.DoesNotExist:
            return Response({'success': False, 'message': 'Route not found'})

    def delete(self, request, format=None):
        """Delete freezer photo"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        photo_id = data.get('id')

        if not photo_id:
            return Response({'success': False, 'message': 'Photo ID is required'})

        try:
            photo = ShopFreezerPhoto.objects.get(id=photo_id)
            photo.delete()
            return Response(delete_success())
        except ShopFreezerPhoto.DoesNotExist:
            return Response({'success': False, 'message': 'Photo not found'})


class RouteScheduleView(APIView):
    """
    API View for Route Schedule management
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        """Get route schedules"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        company_user = request.user.company if request.user.company else request.user
        route_filter = request.query_params.get('route_id', None)

        schedules = RouteSchedule.objects.filter(route__user=company_user)

        if route_filter:
            schedules = schedules.filter(route_id=route_filter)

        return Response(fetch_success(RouteScheduleSerializer(schedules, many=True).data))

    def post(self, request, format=None):
        """Create or update route schedules"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        route_id = data.get('route_id')
        weekdays = data.get('weekdays', [])  # List of weekdays

        if not route_id:
            return Response({'success': False, 'message': 'Route ID is required'})

        try:
            route = Route.objects.get(id=route_id)

            # Clear existing schedules for this route
            RouteSchedule.objects.filter(route=route).delete()

            # Create new schedules for selected weekdays
            for weekday in weekdays:
                if weekday in dict(RouteSchedule.WEEKDAY_CHOICES):
                    # Clean the data - convert empty strings to None
                    billing_time = data.get('expected_billing_time')
                    if billing_time == '':
                        billing_time = None

                    notes = data.get('notes', '')
                    if notes == '':
                        notes = None

                    RouteSchedule.objects.create(
                        route=route,
                        weekday=weekday,
                        is_active=True,
                        expected_billing_time=billing_time,
                        notes=notes
                    )

            return Response(save_success())

        except Route.DoesNotExist:
            return Response({'success': False, 'message': 'Route not found'})

    def put(self, request, format=None):
        """Update route schedule"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        schedule_id = data.get('id')

        if not schedule_id:
            return Response({'success': False, 'message': 'Schedule ID is required'})

        try:
            schedule = RouteSchedule.objects.get(id=schedule_id)

            # Update schedule fields
            schedule.is_active = data.get('is_active', schedule.is_active)

            # Handle expected_billing_time - convert empty string to None
            billing_time = data.get('expected_billing_time', schedule.expected_billing_time)
            if billing_time == '':
                billing_time = None
            schedule.expected_billing_time = billing_time

            # Handle notes - convert empty string to None
            notes = data.get('notes', schedule.notes)
            if notes == '':
                notes = None
            schedule.notes = notes

            schedule.save()

            return Response(edit_success())

        except RouteSchedule.DoesNotExist:
            return Response({'success': False, 'message': 'Schedule not found'})

    def delete(self, request, format=None):
        """Delete route schedule"""
        if request.user.is_anonymous:
            return Response(invalid_user)

        data = request.data
        schedule_id = data.get('id')

        if not schedule_id:
            return Response({'success': False, 'message': 'Schedule ID is required'})

        try:
            schedule = RouteSchedule.objects.get(id=schedule_id)
            schedule.delete()
            return Response(delete_success())
        except RouteSchedule.DoesNotExist:
            return Response({'success': False, 'message': 'Schedule not found'})


class LanguageSwitchView(APIView):
    """
    API View for Language switching between English and Tamil
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, format=None):
        """Get available languages and current language"""
        if request.user.is_anonymous:
            return Response(invalid_user)
            
        current_language = translation.get_language()
        available_languages = [
            {'code': 'en', 'name': 'English', 'native_name': 'English'},
            {'code': 'ta', 'name': 'Tamil', 'native_name': 'தமிழ்'}
        ]
        
        return Response(fetch_success({
            'current_language': current_language,
            'available_languages': available_languages,
            'user_preferred_language': request.user.preferred_language
        }))

    def post(self, request, format=None):
        """Switch language for the current user"""
        if request.user.is_anonymous:
            return Response(invalid_user)
            
        language_code = request.data.get('language_code')
        
        if language_code not in ['en', 'ta']:
            return Response({
                'success': False, 
                'message': _('Invalid language code. Available options: en, ta')
            })
        
        # Update user's preferred language
        request.user.preferred_language = language_code
        request.user.save()
        
        # Activate the language for the current session
        translation.activate(language_code)
        
        language_names = {
            'en': 'English',
            'ta': 'Tamil (தமிழ்)'
        }
        
        return Response({
            'success': True,
            'message': _('Language switched successfully to {}').format(language_names.get(language_code)),
            'current_language': language_code
        })
