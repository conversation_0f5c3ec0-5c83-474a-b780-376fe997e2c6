from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework.authtoken.models import Token
from rest_framework import status
from .models import (
    Product, Suplier, PurchaseOrder, PurchaseOrderItem,
    PurchaseOrderDraft, PurchaseOrderDraftItem, SalesForecast,
    Brand, Buyer, SalesInvoice, SalesInvoiceItem
)

User = get_user_model()

class PurchaseOrderTestCase(APITestCase):
    """Test cases for Purchase Order functionality"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

        # Create test supplier
        self.supplier = Suplier.objects.create(
            user=self.user,
            name='Test Supplier',
            phone_no='1234567890',
            place='Test Address',
            active=True
        )

        # Create test product
        self.product = Product.objects.create(
            user=self.user,
            name='Test Product',
            short_code='TP001',
            pr_rate=100.0,
            tax_rate=18.0,
            stock_quantity=50.0,
            min_stock_threshold=20,
            abc_classification='A',
            reorder_point=30.0,
            safety_stock=10.0,
            lead_time_days=7,
            active=True
        )

    def test_create_purchase_order(self):
        """Test creating a new purchase order"""
        data = {
            'supplier_id': self.supplier.id,
            'expected_delivery_date': '2024-01-15',
            'remarks': 'Test PO',
            'items': [
                {
                    'product_id': self.product.id,
                    'ordered_quantity': 100,
                    'unit_rate': 95.0,
                    'tax_rate': 18.0,
                    'current_stock': 50.0,
                    'min_stock_threshold': 20,
                    'reorder_reason': 'Stock below minimum threshold'
                }
            ]
        }

        response = self.client.post('/api/purchase_order/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify PO was created
        po = PurchaseOrder.objects.get(id=response.data['data']['po_id'])
        self.assertEqual(po.supplier, self.supplier)
        self.assertEqual(po.items.count(), 1)

        # Verify item calculations
        item = po.items.first()
        self.assertEqual(item.ordered_quantity, 100)
        self.assertEqual(item.unit_rate, 95.0)
        self.assertEqual(item.line_total, 9500.0)  # 100 * 95
        self.assertEqual(item.tax_amount, 1710.0)  # 9500 * 0.18

    def test_get_purchase_orders(self):
        """Test retrieving purchase orders"""
        # Create a test PO
        po = PurchaseOrder.objects.create(
            user=self.user,
            supplier=self.supplier,
            total_amount=9500.0,
            tax_amount=1710.0,
            net_amount=11210.0
        )

        response = self.client.get('/api/purchase_order/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)

    def test_purchase_order_draft_creation(self):
        """Test creating purchase order draft"""
        draft = PurchaseOrderDraft.objects.create(
            user=self.user,
            supplier=self.supplier,
            auto_generated=True,
            forecast_based=True
        )

        draft_item = PurchaseOrderDraftItem.objects.create(
            draft=draft,
            product=self.product,
            quantity=50,
            estimated_rate=95.0,
            estimated_total=4750.0,
            current_stock=15.0,
            min_stock_threshold=20,
            shortage_quantity=5.0,
            reorder_reason='Stock below minimum threshold'
        )

        self.assertEqual(draft.items.count(), 1)
        self.assertEqual(draft_item.shortage_quantity, 5.0)

    def test_convert_draft_to_po(self):
        """Test converting draft to purchase order"""
        # Create draft
        draft = PurchaseOrderDraft.objects.create(
            user=self.user,
            supplier=self.supplier,
            auto_generated=True
        )

        PurchaseOrderDraftItem.objects.create(
            draft=draft,
            product=self.product,
            quantity=50,
            estimated_rate=95.0,
            current_stock=15.0,
            min_stock_threshold=20
        )

        data = {
            'draft_id': draft.id,
            'expected_delivery_date': '2024-01-15'
        }

        response = self.client.post('/api/purchase_order_draft/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify draft was deleted and PO was created
        self.assertFalse(PurchaseOrderDraft.objects.filter(id=draft.id).exists())
        self.assertTrue(PurchaseOrder.objects.filter(supplier=self.supplier).exists())

class SalesForecastTestCase(TestCase):
    """Test cases for Sales Forecast functionality"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.product = Product.objects.create(
            user=self.user,
            name='Test Product',
            short_code='TP001',
            active=True
        )

    def test_sales_forecast_creation(self):
        """Test creating sales forecast"""
        forecast = SalesForecast.objects.create(
            user=self.user,
            product=self.product,
            forecast_date='2024-01-01',
            forecast_month=1,
            forecast_year=2024,
            last_year_same_month_sales=100.0,
            last_3_months_avg_sales=120.0,
            growth_factor=0.1,
            internal_forecast_quantity=132.0,  # 120 * 1.1
            final_forecast_quantity=132.0
        )

        self.assertEqual(forecast.product, self.product)
        self.assertEqual(forecast.internal_forecast_quantity, 132.0)
        self.assertFalse(forecast.ai_processed)

    def test_ai_forecast_processing(self):
        """Test AI forecast processing"""
        forecast = SalesForecast.objects.create(
            user=self.user,
            product=self.product,
            forecast_date='2024-01-01',
            forecast_month=1,
            forecast_year=2024,
            internal_forecast_quantity=132.0,
            final_forecast_quantity=132.0
        )

        # Simulate AI processing
        forecast.ai_confirmed_quantity = 140.0
        forecast.ai_confidence_score = 0.85
        forecast.ai_analysis_comment = 'Forecast looks reasonable with slight upward adjustment'
        forecast.ai_processed = True
        forecast.final_forecast_quantity = 140.0  # Use AI confirmed value
        forecast.save()

        self.assertTrue(forecast.ai_processed)
        self.assertEqual(forecast.ai_confidence_score, 0.85)
        self.assertEqual(forecast.final_forecast_quantity, 140.0)


class SalesReportTestCase(APITestCase):
    """Test cases for Sales Reports functionality"""

    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

        # Create test brand
        self.brand = Brand.objects.create(
            user=self.user,
            name='Test Brand'
        )

        # Create test buyer
        self.buyer = Buyer.objects.create(
            user=self.user,
            name='Test Buyer',
            phone_no='1234567890',
            place='Test Address',
            active=True
        )

        # Create test product
        self.product = Product.objects.create(
            user=self.user,
            name='Test Product',
            short_code='TP001',
            brand=self.brand,
            pr_rate=100.0,
            tax_rate=18.0,
            active=True
        )

        # Create test sales invoice
        self.sales_invoice = SalesInvoice.objects.create(
            user=self.user,
            buyer=self.buyer,
            date='2024-01-15',
            bill_amount=1180.0,
            invoice_status='billed'
        )

        # Create test sales invoice item
        self.sales_item = SalesInvoiceItem.objects.create(
            sales_invoice=self.sales_invoice,
            product=self.product,
            no=10.0,  # quantity
            weight=5.0,  # pieces
            rate=100.0,
            line_total=1000.0,
            remarks='Test item'
        )

    def test_brand_wise_sales_report(self):
        """Test brand-wise sales report generation"""
        from_date = '2024-01-01'
        to_date = '2024-01-31'
        
        response = self.client.get(f'/api/report/?report_type=brand_wise_sales&from_date={from_date}&to_date={to_date}&include_zero_sales=true')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Check if report data contains expected fields
        report_data = response.data['data']
        self.assertTrue(len(report_data) > 0)
        
        # Check first item has expected structure
        first_item = report_data[0]
        expected_fields = ['Date', 'Invoice ID', 'Buyer', 'Brand', 'Product', 'Quantity', 'Pieces', 'Rate', 'Line Total', 'Tax Rate', 'Remarks']
        for field in expected_fields:
            self.assertIn(field, first_item)

    def test_general_sales_report(self):
        """Test general sales report generation"""
        from_date = '2024-01-01'
        to_date = '2024-01-31'
        
        response = self.client.get(f'/api/report/?report_type=general_sales&from_date={from_date}&to_date={to_date}&include_zero_sales=true')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Check if report data contains expected fields
        report_data = response.data['data']
        self.assertTrue(len(report_data) > 0)
        
        # Check first item has expected structure
        first_item = report_data[0]
        expected_fields = ['Date', 'Invoice ID', 'Buyer', 'Brand', 'Product', 'Quantity', 'Pieces', 'Rate', 'Line Total', 'Tax Rate', 'Remarks']
        for field in expected_fields:
            self.assertIn(field, first_item)

    def test_brand_filtering(self):
        """Test brand filtering in brand-wise sales report"""
        from_date = '2024-01-01'
        to_date = '2024-01-31'
        brand_ids = [self.brand.id]
        
        response = self.client.get(f'/api/report/?report_type=brand_wise_sales&from_date={from_date}&to_date={to_date}&brand_ids={brand_ids}&include_zero_sales=true')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # All items should have the specified brand
        report_data = response.data['data']
        for item in report_data:
            if item['Brand']:  # Skip summary rows
                self.assertEqual(item['Brand'], self.brand.name)

    def test_zero_sales_exclusion(self):
        """Test excluding zero sales from reports"""
        from_date = '2024-01-01'
        to_date = '2024-01-31'
        
        response = self.client.get(f'/api/report/?report_type=general_sales&from_date={from_date}&to_date={to_date}&include_zero_sales=false')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # All items should have line_total > 0
        report_data = response.data['data']
        for item in report_data:
            if item['Line Total']:  # Skip summary rows
                self.assertGreater(float(item['Line Total']), 0)
            forecast_month=1,
            forecast_year=2024,
            internal_forecast_quantity=132.0,
            final_forecast_quantity=132.0
        )

        # Simulate AI processing
        forecast.ai_confirmed_quantity = 140.0
        forecast.ai_confidence_score = 0.85
        forecast.ai_analysis_comment = 'Forecast looks reasonable with slight upward adjustment'
        forecast.ai_processed = True
        forecast.final_forecast_quantity = 140.0  # Use AI confirmed value
        forecast.save()

        self.assertTrue(forecast.ai_processed)
        self.assertEqual(forecast.ai_confidence_score, 0.85)
        self.assertEqual(forecast.final_forecast_quantity, 140.0)
