from master.models import *
from dodla import dodla,unit_contents
sort_order = 1
user=User.objects.get(id=7)
product = Product.objects.filter(user=user).order_by('sort_order')
for p in product:
    p.sort_order = sort_order
    p.save()
    sort_order+=1
for index,d in enumerate(product):
    Product.objects.create(user=user,name="Amul Fundoo Super Mango",short_code="10 fundoo spr m AM",rate=9,margin=10,mrp=10,unit_contains=20,active=True,sort_order=42)
    sort_order+=1

from master.models import *
for b in Buyer.objects.all():
    sales_invoice = SalesInvoice.objects.filter(buyer=b)
    for s in sales_invoice:
        Ledger.objects.create(invoice=s,buyer=b,amount=s.bill_amount,entry_type='debit')
        if s.received_amount:
            Ledger.objects.create(invoice=s,buyer=b,amount=s.received_amount,entry_type='credit')