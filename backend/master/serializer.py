from rest_framework import serializers
from .models import *
from django.contrib.auth.models import Permission,Group


class CompanyUserSerializer(serializers.ModelSerializer):

    class Meta():
        model = User
        fields = ['gst_no','fssai_no','contact_no_left','contact_no_right','address','company_name','company_logo','fssai_no']


class PermissionSerializer(serializers.ModelSerializer):
    class Meta():
        model = Permission
        fields = "__all__"

class UserSerializer(serializers.ModelSerializer):
    company = CompanyUserSerializer()
    user_permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        exclude = ['password']

    def get_user_permissions(self, obj):
        # Get user-specific permissions
        user_permissions = set(obj.user_permissions.all())

        # Get all permissions from the user's assigned groups
        for group in obj.groups.all():
            user_permissions.update(group.permissions.all())  # Merge permissions

        # Return serialized distinct permissions
        return PermissionSerializer(user_permissions, many=True).data

class ProductSerializer(serializers.ModelSerializer):
    brand_name = serializers.CharField(source="brand.name",allow_null=True)
    final_margin = serializers.FloatField(read_only=True)
    
    class Meta():
        model = Product
        fields = "__all__"


class BrandSerializer(serializers.ModelSerializer):
    class Meta():
        model = Brand
        fields = "__all__"

class RouteSerializer(serializers.ModelSerializer):
    class Meta():
        model = Route
        fields = "__all__"

class BuyerClassSerializer(serializers.ModelSerializer):
    class Meta():
        model = BuyerClass
        fields = "__all__"


class BuyerClassMarginSerializer(serializers.ModelSerializer):
    class Meta():
        model = BuyerClassMargin
        fields = "__all__"

class DepositSerializer(serializers.ModelSerializer):
    class Meta():
        model = Deposit
        fields = "__all__"


class AssetFileSerializer(serializers.ModelSerializer):
    class Meta():
        model = AssetFile
        fields = "__all__"

class AssetSerializer(serializers.ModelSerializer):
    files=AssetFileSerializer(many=True)
    class Meta():
        model = Asset
        fields = "__all__"


class BuyerDetailsSerializer(serializers.ModelSerializer):
    route_name = serializers.CharField(source="route.name",allow_null=True)
    buyer_class_name = serializers.CharField(source="buyer_class.name",allow_null=True)
    buyer_deposit = DepositSerializer(many=True)
    buyer_asset = AssetSerializer(many=True)
    profile_completion = serializers.SerializerMethodField()

    class Meta():
        model = Buyer
        fields = "__all__"

    def get_profile_completion(self, obj):
        """
        Calculate profile completion status based on assets and deposits
        Returns completion status with details of what's missing
        """
        completion_data = {
            'is_complete': True,
            'completion_percentage': 0,
            'missing_modules': [],
            'completed_modules': [],
            'details': {}
        }

        # Check basic profile fields
        basic_fields = ['name', 'place', 'contact_person', 'phone_no']
        basic_complete = all(getattr(obj, field) for field in basic_fields)

        # Check assets
        has_assets = obj.buyer_asset.exists()

        # Check deposits
        has_deposits = obj.buyer_deposit.exists()

        # Calculate completion
        modules_checked = 0
        modules_complete = 0

        # Basic profile module
        modules_checked += 1
        if basic_complete:
            modules_complete += 1
            completion_data['completed_modules'].append('Basic Profile')
        else:
            completion_data['missing_modules'].append('Basic Profile')
            missing_basic = [field for field in basic_fields if not getattr(obj, field)]
            completion_data['details']['basic_profile'] = {
                'status': 'incomplete',
                'missing_fields': missing_basic
            }

        # Assets module
        modules_checked += 1
        if has_assets:
            modules_complete += 1
            completion_data['completed_modules'].append('Assets')
            completion_data['details']['assets'] = {
                'status': 'complete',
                'count': obj.buyer_asset.count()
            }
        else:
            completion_data['missing_modules'].append('Assets')
            completion_data['details']['assets'] = {
                'status': 'incomplete',
                'message': 'No assets added'
            }

        # Deposits module
        modules_checked += 1
        if has_deposits:
            modules_complete += 1
            completion_data['completed_modules'].append('Deposits')
            completion_data['details']['deposits'] = {
                'status': 'complete',
                'count': obj.buyer_deposit.count()
            }
        else:
            completion_data['missing_modules'].append('Deposits')
            completion_data['details']['deposits'] = {
                'status': 'incomplete',
                'message': 'No deposits added'
            }

        # Calculate percentage
        completion_data['completion_percentage'] = round((modules_complete / modules_checked) * 100)
        completion_data['is_complete'] = modules_complete == modules_checked

        return completion_data

class BuyerSerializer(serializers.ModelSerializer):
    route_name = serializers.CharField(source="route.name",allow_null=True)
    buyer_class_name = serializers.CharField(source="buyer_class.name",allow_null=True)
    profile_completion = serializers.SerializerMethodField()

    class Meta():
        model = Buyer
        fields = "__all__"

    def get_profile_completion(self, obj):
        """
        Calculate profile completion status based on assets and deposits
        Returns simplified completion status for list view
        """
        # Check basic profile fields
        basic_fields = ['name', 'place', 'contact_person', 'phone_no']
        basic_complete = all(getattr(obj, field) for field in basic_fields)

        # Check assets and deposits
        has_assets = obj.buyer_asset.exists()
        has_deposits = obj.buyer_deposit.exists()

        # Calculate completion
        modules_complete = sum([basic_complete, has_assets, has_deposits])
        completion_percentage = round((modules_complete / 3) * 100)

        missing_modules = []
        if not basic_complete:
            missing_modules.append('Basic Profile')
        if not has_assets:
            missing_modules.append('Assets')
        if not has_deposits:
            missing_modules.append('Deposits')

        return {
            'is_complete': modules_complete == 3,
            'completion_percentage': completion_percentage,
            'missing_modules': missing_modules,
            'modules_complete': modules_complete,
            'total_modules': 3
        }



class SuplierSerializer(serializers.ModelSerializer):
    deposit = DepositSerializer(many=True)

    class Meta():
        model = Suplier
        fields = "__all__"

class SalesOrderItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source="product.name")
    class Meta():
        model = SalesOrderItem
        fields = "__all__"

class SalesOrderSerializer(serializers.ModelSerializer):
    sales_order_items = SalesOrderItemSerializer(many=True)
    place = serializers.SerializerMethodField()
    phone_no = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        if obj.buyer is not None:
            return obj.buyer.name
    
    def get_phone_no(self, obj):
        if obj.buyer is not None:
            return obj.buyer.phone_no

    def get_place(self, obj):
        if obj.buyer is not None:
            return obj.buyer.place

    class Meta():
        model = SalesOrder
        fields = "__all__"

class SalesInvoiceItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source="product.name")
    tax_rate = serializers.IntegerField(source="product.tax_rate")
    hsn_code = serializers.CharField(source="product.hsn_code")
    box = serializers.IntegerField(source="no")
    pcs = serializers.IntegerField(source="weight")
    class Meta():
        model = SalesInvoiceItem
        fields = "__all__"

class SalesInvoiceSerializer(serializers.ModelSerializer):
    sales_invoice_items = SalesInvoiceItemSerializer(many=True)
    name = serializers.CharField(source="buyer.name",allow_null=True)
    phone_no = serializers.CharField(source="buyer.phone_no",allow_null=True)
    place = serializers.CharField(source="buyer.place",allow_null=True)
    gst_no = serializers.CharField(source="buyer.gst_no",allow_null=True)
    sales_person = serializers.CharField(source="sales_person.first_name",allow_null=True)

    class Meta():
        model = SalesInvoice
        fields = "__all__"

class SalesPaymentSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="buyer.name",allow_null=True)
    class Meta():
        model = SalesInvoice
        fields = "__all__"
class PurchasePaymentSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="suplier.name",allow_null=True)
    class Meta():
        model = PurchaseInvoice
        fields = "__all__"

class PurchaseInvoiceItemSerializer(serializers.ModelSerializer):
    class Meta():
        model = PurchaseInvoiceItem
        fields = "__all__"

class PurchaseInvoiceSerializer(serializers.ModelSerializer):
    purchase_invoice_items = PurchaseInvoiceItemSerializer(many=True)
    name = serializers.CharField(source="suplier.name")
    class Meta():
        model = PurchaseInvoice
        fields = "__all__"
    
class PurchaseInvoiceListSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="suplier.name")
    class Meta():
        model = PurchaseInvoice
        fields = "__all__"

class LineAccountsSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="suplier.name")
    class Meta():
        model = LineAccounts
        fields = "__all__"

class LedgerSerializer(serializers.ModelSerializer):
    current_balance = serializers.FloatField(source="closing_amount",allow_null=True)
    
    class Meta():
        model = Ledger
        fields = "__all__"

class BuyerLedgerListSerializer(serializers.ModelSerializer):
    balance_amount = serializers.FloatField(read_only=True,allow_null=True)

    class Meta():
        model = Buyer
        fields = ['id','name','balance_amount']

class SupplierLedgerListSerializer(serializers.ModelSerializer):
   
    balance_amount = serializers.FloatField(read_only=True,allow_null=True)

    class Meta():
        model = Suplier
        fields = ['id','name','balance_amount']

class PartySerializer(serializers.ModelSerializer):
    class Meta():
        model = Party
        fields = "__all__"


class ItemSerializer(serializers.ModelSerializer):
    class Meta():
        model = Item
        fields = "__all__"


class RateSerializer(serializers.ModelSerializer):
    class Meta():
        model = Rate
        fields = "__all__"


class OrderItemSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source="supplier.name",allow_null=True)
    item_name = serializers.CharField(source="item.name",allow_null=True)
    
    class Meta():
        model = OrderItem
        fields = "__all__"



class OrderSerializer(serializers.ModelSerializer):
    order_items = OrderItemSerializer(many=True)
    reciever_name = serializers.CharField(source="reciever.name",allow_null=True)

    class Meta():
        model = Order
        fields = "__all__"



class OrderListSerializer(serializers.ModelSerializer):
    reciever_name = serializers.CharField(source="reciever.name",allow_null=True)

    class Meta():
        model = Order
        fields = "__all__"


class InvoiceItemSerializer(serializers.ModelSerializer):
    item_name = serializers.CharField(source="item.name",allow_null=True)
    supplier_name = serializers.CharField(source="supplier.name",allow_null=True)
    class Meta:
        model = InvoiceItem
        fields = "__all__"


class InvoiceSerializer(serializers.ModelSerializer):
    invoice_items = InvoiceItemSerializer(many=True)
    reciever_name = serializers.CharField(source="reciever.name",allow_null=True)
    sender_name = serializers.CharField(source="sender.name",allow_null=True)

    class Meta:
        model = Invoice
        fields = "__all__"


class InvoiceListSerializer(serializers.ModelSerializer):
    reciever_name = serializers.CharField(source="reciever.name",allow_null=True)
    sender_name = serializers.CharField(source="sender.name",allow_null=True)
    sales_person_name = serializers.CharField(source="sales_person.first_name",allow_null=True)

    class Meta:
        model = Invoice
        fields = "__all__"


class SubCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = SubCategory
        fields = '__all__'

class ExpenseCategorySerializer(serializers.ModelSerializer):
    subcategories = SubCategorySerializer(many=True, read_only=True)

    class Meta:
        model = ExpenseCategory
        fields = '__all__'

class ExpenseListSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source="category.name")
    subcategory_name = serializers.CharField(source="subcategory.name")

    class Meta:
        model = Expense
        fields = '__all__'


class ExpenseSerializer(serializers.ModelSerializer):
    category = serializers.PrimaryKeyRelatedField(queryset=ExpenseCategory.objects.all())
    subcategory = serializers.PrimaryKeyRelatedField(queryset=SubCategory.objects.all())

    class Meta:
        model = Expense
        fields = ['id', 'category', 'subcategory', 'amount', 'notes','expense_file','mode_of_payment']

    def create(self, validated_data):
        category = validated_data.pop('category')
        subcategory = validated_data.pop('subcategory')
        expense = Expense.objects.create(category=category, subcategory=subcategory, **validated_data)
        return expense


class SupplierPreferenceSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)

    class Meta:
        model = SupplierPreference
        fields = '__all__'


class SupplierBrandSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    brand_name = serializers.CharField(source='brand.name', read_only=True)

    class Meta:
        model = SupplierBrand
        fields = '__all__'


class BuyerBrandSerializer(serializers.ModelSerializer):
    buyer_name = serializers.CharField(source='buyer.name', read_only=True)
    brand_name = serializers.CharField(source='brand.name', read_only=True)

    class Meta:
        model = BuyerBrand
        fields = '__all__'

class SalesForecastSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)

    class Meta:
        model = SalesForecast
        fields = '__all__'

class PurchaseOrderItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_short_code = serializers.CharField(source='product.short_code', read_only=True)

    class Meta:
        model = PurchaseOrderItem
        fields = '__all__'

class PurchaseOrderSerializer(serializers.ModelSerializer):
    items = PurchaseOrderItemSerializer(many=True, read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.first_name', read_only=True)

    class Meta:
        model = PurchaseOrder
        fields = '__all__'

class PurchaseOrderListSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    items_count = serializers.SerializerMethodField()
    total_items_quantity = serializers.SerializerMethodField()

    class Meta:
        model = PurchaseOrder
        fields = ['id', 'po_number', 'supplier_name', 'status', 'created_date',
                 'expected_delivery_date', 'net_amount', 'auto_generated',
                 'items_count', 'total_items_quantity']

    def get_items_count(self, obj):
        return obj.items.count()

    def get_total_items_quantity(self, obj):
        return sum(item.ordered_quantity for item in obj.items.all())

class PurchaseOrderDraftItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_short_code = serializers.CharField(source='product.short_code', read_only=True)

    class Meta:
        model = PurchaseOrderDraftItem
        fields = '__all__'

class PurchaseOrderDraftSerializer(serializers.ModelSerializer):
    items = PurchaseOrderDraftItemSerializer(many=True, read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    items_count = serializers.SerializerMethodField()

    class Meta:
        model = PurchaseOrderDraft
        fields = '__all__'

    def get_items_count(self, obj):
        return obj.items.count()


class SalesTrendByBrandSerializer(serializers.Serializer):
    """
    Serializer for sales trend by brand analytics data
    """
    period = serializers.DateField()
    brand_id = serializers.IntegerField(source='product__brand__id')
    brand_name = serializers.CharField(source='product__brand__name')
    total_sales_amount = serializers.FloatField()
    total_quantity = serializers.FloatField()
    total_pieces = serializers.FloatField()
    transaction_count = serializers.IntegerField()


class BrandSummarySerializer(serializers.Serializer):
    """
    Serializer for brand summary data
    """
    brand_id = serializers.IntegerField(source='product__brand__id')
    brand_name = serializers.CharField(source='product__brand__name')
    total_sales_amount = serializers.FloatField()
    total_quantity = serializers.FloatField()
    total_pieces = serializers.FloatField()
    transaction_count = serializers.IntegerField()


class ChecklistItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChecklistItem
        fields = "__all__"


class ChecklistSerializer(serializers.ModelSerializer):
    items = ChecklistItemSerializer(many=True, read_only=True)
    completion_percentage = serializers.ReadOnlyField()
    is_overdue = serializers.ReadOnlyField()
    route_name = serializers.CharField(source="route.name", allow_null=True, read_only=True)
    buyer_name = serializers.CharField(source="buyer.name", allow_null=True, read_only=True)

    class Meta:
        model = Checklist
        fields = "__all__"


class ChecklistTemplateItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChecklistTemplateItem
        fields = "__all__"


class ChecklistTemplateSerializer(serializers.ModelSerializer):
    template_items = ChecklistTemplateItemSerializer(many=True, read_only=True)

    class Meta:
        model = ChecklistTemplate
        fields = "__all__"


class RouteScheduleSerializer(serializers.ModelSerializer):
    route_name = serializers.CharField(source="route.name", read_only=True)

    class Meta:
        model = RouteSchedule
        fields = "__all__"


class ShopFreezerPhotoSerializer(serializers.ModelSerializer):
    buyer_name = serializers.CharField(source="buyer.name", read_only=True)
    route_name = serializers.CharField(source="route.name", read_only=True)
    uploaded_by_name = serializers.CharField(source="uploaded_by.username", read_only=True)

    class Meta:
        model = ShopFreezerPhoto
        fields = "__all__"


class RouteBillingSummarySerializer(serializers.ModelSerializer):
    route_name = serializers.CharField(source="route.name", read_only=True)
    is_complete = serializers.ReadOnlyField()
    weekday = serializers.SerializerMethodField()
    unbilled_buyers = serializers.SerializerMethodField()

    class Meta:
        model = RouteBillingSummary
        fields = "__all__"

    def get_weekday(self, obj):
        """Get the weekday name for the date"""
        return obj.date.strftime('%A').lower()

    def get_unbilled_buyers(self, obj):
        """Get list of unbilled buyers for this route and date"""
        from django.db.models import Q

        # Get all buyers in this route
        route_buyers = Buyer.objects.filter(route=obj.route, active=True)

        # Get buyers who have been billed on this date
        billed_buyer_ids = SalesInvoice.objects.filter(
            buyer__route=obj.route,
            date=obj.date
        ).values_list('buyer_id', flat=True).distinct()

        # Get unbilled buyers
        unbilled_buyers = route_buyers.exclude(id__in=billed_buyer_ids)

        return BuyerSerializer(unbilled_buyers, many=True).data


class InvoiceImageSerializer(serializers.ModelSerializer):
    """Serializer for InvoiceImage model"""
    image_url = serializers.SerializerMethodField()
    uploaded_by_name = serializers.CharField(source="uploaded_by.first_name", read_only=True)
    
    class Meta:
        model = InvoiceImage
        fields = "__all__"
    
    def get_image_url(self, obj):
        """Return the full URL for the image"""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None