"""
Celery tasks for inventory management and purchase order automation.
"""
import logging
import requests
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional

from celery import shared_task
from django.conf import settings
from django.db import transaction
from django.db.models import Sum, Avg, Q, F
from django.utils import timezone

from .models import (
    Product, SalesInvoice, SalesInvoiceItem, PurchaseOrder, PurchaseOrderItem,
    PurchaseOrderDraft, PurchaseOrderDraftItem, SalesForecast, SupplierPreference,
    Suplier, User, ShopFreezerPhoto
)

logger = logging.getLogger(__name__)


@shared_task(bind=True, queue='inventory')
def daily_inventory_check(self):
    """
    Daily task to check inventory levels and generate purchase orders for low stock items.
    Runs at 8 AM daily.
    """
    logger.info("Starting daily inventory check...")
    
    try:
        # Get all active products with stock below minimum threshold
        low_stock_products = Product.objects.filter(
            active=True,
            stock_quantity__lt=F('min_stock_threshold')
        ).select_related('user')
        
        results = {
            'total_products_checked': Product.objects.filter(active=True).count(),
            'low_stock_products': low_stock_products.count(),
            'purchase_orders_generated': 0,
            'errors': []
        }
        
        # Group by user/company
        products_by_user = {}
        for product in low_stock_products:
            user = product.user
            if user not in products_by_user:
                products_by_user[user] = []
            products_by_user[user].append(product)
        
        # Generate purchase orders for each user
        for user, products in products_by_user.items():
            try:
                po_count = generate_purchase_orders_for_user(user, products)
                results['purchase_orders_generated'] += po_count
            except Exception as e:
                error_msg = f"Error generating PO for user {user.username}: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
        
        logger.info(f"Daily inventory check completed: {results}")
        return results
        
    except Exception as e:
        logger.error(f"Daily inventory check failed: {str(e)}")
        raise


@shared_task(bind=True, queue='forecast')
def generate_sales_forecast(self):
    """
    Generate sales forecasts for all active products based on historical data.
    """
    logger.info("Starting sales forecast generation...")
    
    try:
        current_date = timezone.now().date()
        current_month = current_date.month
        current_year = current_date.year
        
        # Get all active products
        products = Product.objects.filter(active=True).select_related('user')
        
        results = {
            'total_products': products.count(),
            'forecasts_generated': 0,
            'forecasts_updated': 0,
            'errors': []
        }
        
        for product in products:
            try:
                forecast_data = calculate_internal_forecast(product, current_month, current_year)
                
                # Create or update forecast
                forecast, created = SalesForecast.objects.update_or_create(
                    product=product,
                    forecast_month=current_month,
                    forecast_year=current_year,
                    defaults=forecast_data
                )
                
                if created:
                    results['forecasts_generated'] += 1
                else:
                    results['forecasts_updated'] += 1
                    
            except Exception as e:
                error_msg = f"Error generating forecast for product {product.name}: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
        
        logger.info(f"Sales forecast generation completed: {results}")
        return results
        
    except Exception as e:
        logger.error(f"Sales forecast generation failed: {str(e)}")
        raise


@shared_task(bind=True, queue='ai')
def process_ai_forecasts(self):
    """
    Process forecasts through AI API for validation and enhancement.
    """
    if not settings.AI_FORECAST_ENABLED:
        logger.info("AI forecast processing is disabled")
        return {'status': 'disabled'}
    
    logger.info("Starting AI forecast processing...")
    
    try:
        # Get unprocessed forecasts
        unprocessed_forecasts = SalesForecast.objects.filter(
            ai_processed=False,
            forecast_date__gte=timezone.now().date() - timedelta(days=7)
        ).select_related('product', 'user')
        
        results = {
            'total_forecasts': unprocessed_forecasts.count(),
            'processed': 0,
            'errors': []
        }
        
        for forecast in unprocessed_forecasts:
            try:
                ai_result = call_deepseek_api(forecast)
                
                if ai_result:
                    forecast.ai_confirmed_quantity = ai_result.get('confirmed_sales_quantity')
                    forecast.ai_confidence_score = ai_result.get('ai_confidence_score')
                    forecast.ai_analysis_comment = ai_result.get('analysis_comment')
                    forecast.ai_processed = True
                    forecast.ai_processed_at = timezone.now()
                    
                    # Update final forecast based on AI confirmation
                    if forecast.ai_confidence_score and forecast.ai_confidence_score > 0.7:
                        forecast.final_forecast_quantity = forecast.ai_confirmed_quantity
                    else:
                        # Use internal forecast if AI confidence is low
                        forecast.final_forecast_quantity = forecast.internal_forecast_quantity
                    
                    forecast.save()
                    results['processed'] += 1
                
            except Exception as e:
                error_msg = f"Error processing AI forecast for {forecast.product.name}: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
        
        logger.info(f"AI forecast processing completed: {results}")
        return results
        
    except Exception as e:
        logger.error(f"AI forecast processing failed: {str(e)}")
        raise


@shared_task(bind=True, queue='purchase')
def generate_purchase_order(self, user_id: int, product_ids: List[int]):
    """
    Generate purchase order for specific products and user.
    """
    logger.info(f"Generating purchase order for user {user_id}, products: {product_ids}")
    
    try:
        user = User.objects.get(id=user_id)
        products = Product.objects.filter(id__in=product_ids, user=user)
        
        po_count = generate_purchase_orders_for_user(user, products)
        
        return {
            'user_id': user_id,
            'products_processed': len(products),
            'purchase_orders_generated': po_count
        }
        
    except Exception as e:
        logger.error(f"Purchase order generation failed: {str(e)}")
        raise


def calculate_internal_forecast(product: Product, month: int, year: int) -> Dict:
    """
    Calculate internal sales forecast based on historical data.
    """
    # Get last year same month sales
    last_year_sales = SalesInvoiceItem.objects.filter(
        product=product,
        sales_invoice__date__month=month,
        sales_invoice__date__year=year - 1
    ).aggregate(
        total_qty=Sum('no') + Sum('weight')
    )['total_qty'] or 0
    
    # Get last 3 months average sales
    three_months_ago = timezone.now().date() - timedelta(days=90)
    last_3_months_sales = SalesInvoiceItem.objects.filter(
        product=product,
        sales_invoice__date__gte=three_months_ago
    ).aggregate(
        avg_qty=Avg('no') + Avg('weight')
    )['avg_qty'] or 0
    
    # Calculate internal forecast
    growth_factor = getattr(settings, 'DEFAULT_GROWTH_FACTOR', 0.1)
    base_forecast = max(last_year_sales, last_3_months_sales)
    internal_forecast = base_forecast * (1 + growth_factor)
    
    return {
        'user': product.user,
        'forecast_date': timezone.now().date(),
        'last_year_same_month_sales': float(last_year_sales),
        'last_3_months_avg_sales': float(last_3_months_sales),
        'growth_factor': growth_factor,
        'internal_forecast_quantity': float(internal_forecast),
        'final_forecast_quantity': float(internal_forecast),  # Default to internal
    }


def call_deepseek_api(forecast: SalesForecast) -> Optional[Dict]:
    """
    Call DeepSeek API for forecast validation.
    """
    if not settings.DEEPSEEK_API_URL or not settings.DEEPSEEK_API_KEY:
        logger.warning("DeepSeek API configuration missing")
        return None
    
    try:
        payload = {
            'product_id': forecast.product.id,
            'product_name': forecast.product.name,
            'last_year_same_month_sales': forecast.last_year_same_month_sales,
            'last_3_months_avg_sales': forecast.last_3_months_avg_sales,
            'growth_factor': forecast.growth_factor,
            'abc_classification': forecast.product.abc_classification,
            'month': forecast.forecast_month,
            'internal_forecast': forecast.internal_forecast_quantity,
        }
        
        headers = {
            'Authorization': f'Bearer {settings.DEEPSEEK_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            settings.DEEPSEEK_API_URL,
            json=payload,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"DeepSeek API error: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"DeepSeek API call failed: {str(e)}")
        return None


def generate_purchase_orders_for_user(user: User, products: List[Product]) -> int:
    """
    Generate purchase orders for a user's low stock products.
    """
    # Group products by preferred supplier
    products_by_supplier = {}
    
    for product in products:
        # Get preferred supplier for this product
        preferred_supplier = get_preferred_supplier(product)
        
        if preferred_supplier not in products_by_supplier:
            products_by_supplier[preferred_supplier] = []
        products_by_supplier[preferred_supplier].append(product)
    
    po_count = 0
    
    # Create purchase order drafts for each supplier
    for supplier, supplier_products in products_by_supplier.items():
        try:
            with transaction.atomic():
                # Create draft PO
                draft = PurchaseOrderDraft.objects.create(
                    user=user,
                    supplier=supplier,
                    auto_generated=True,
                    forecast_based=True
                )
                
                total_estimated_amount = 0
                
                # Add items to draft
                for product in supplier_products:
                    shortage_qty = max(0, product.min_stock_threshold - product.stock_quantity)
                    
                    # Get latest forecast if available
                    latest_forecast = SalesForecast.objects.filter(
                        product=product
                    ).order_by('-forecast_date').first()
                    
                    forecast_qty = latest_forecast.final_forecast_quantity if latest_forecast else 0
                    
                    # Calculate order quantity (shortage + forecast for lead time)
                    lead_time_demand = forecast_qty * (product.lead_time_days / 30)  # Monthly forecast to daily
                    order_qty = shortage_qty + lead_time_demand + product.safety_stock
                    
                    estimated_rate = product.pr_rate or 0
                    estimated_total = order_qty * estimated_rate
                    total_estimated_amount += estimated_total
                    
                    PurchaseOrderDraftItem.objects.create(
                        draft=draft,
                        product=product,
                        quantity=order_qty,
                        estimated_rate=estimated_rate,
                        estimated_total=estimated_total,
                        forecast_quantity=forecast_qty,
                        current_stock=product.stock_quantity,
                        min_stock_threshold=product.min_stock_threshold,
                        shortage_quantity=shortage_qty,
                        reorder_reason=f"Stock below minimum threshold. Current: {product.stock_quantity}, Min: {product.min_stock_threshold}",
                        auto_generated=True
                    )
                
                # Update draft total
                draft.total_estimated_amount = total_estimated_amount
                draft.save()
                
                po_count += 1
                logger.info(f"Created purchase order draft {draft.id} for supplier {supplier.name}")
                
        except Exception as e:
            logger.error(f"Error creating PO draft for supplier {supplier.name}: {str(e)}")
    
    return po_count


def get_preferred_supplier(product: Product) -> Suplier:
    """
    Get the preferred supplier for a product based on preferences.
    """
    # Try to get preferred supplier
    preference = SupplierPreference.objects.filter(
        product=product,
        is_preferred=True
    ).order_by('priority').first()
    
    if preference:
        return preference.supplier
    
    # Fallback to any supplier with preferences for this product
    fallback_preference = SupplierPreference.objects.filter(
        product=product
    ).order_by('priority').first()
    
    if fallback_preference:
        return fallback_preference.supplier
    
    # Last resort: get any active supplier for this user
    fallback_supplier = Suplier.objects.filter(
        user=product.user,
        active=True
    ).first()
    
    if not fallback_supplier:
        # Create a default supplier if none exists
        fallback_supplier = Suplier.objects.create(
            user=product.user,
            name="Default Supplier",
            active=True
        )
    
    return fallback_supplier


@shared_task(bind=True, queue='cleanup')
def cleanup_old_shop_photos(_self):
    """
    Cleanup task to delete shop freezer photos older than 30 days.
    Runs daily to keep storage clean and remove outdated images.
    """
    logger.info("Starting cleanup of old shop photos...")

    try:
        # Calculate cutoff date (30 days ago)
        cutoff_date = timezone.now().date() - timedelta(days=30)

        # Find photos older than 30 days
        old_photos = ShopFreezerPhoto.objects.filter(
            date_taken__lt=cutoff_date
        )

        results = {
            'total_photos_found': old_photos.count(),
            'photos_deleted': 0,
            'storage_freed_mb': 0,
            'errors': []
        }

        logger.info(f"Found {results['total_photos_found']} photos older than {cutoff_date}")

        # Delete photos and calculate storage freed
        for photo in old_photos:
            try:
                # Get file size before deletion
                file_size = 0
                if photo.photo and hasattr(photo.photo, 'size'):
                    file_size = photo.photo.size

                # Delete the photo file and database record
                if photo.photo:
                    photo.photo.delete(save=False)  # Delete file from storage
                photo.delete()  # Delete database record

                results['photos_deleted'] += 1
                results['storage_freed_mb'] += file_size / (1024 * 1024)  # Convert to MB

            except Exception as e:
                error_msg = f"Error deleting photo {photo.id}: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)

        # Round storage freed to 2 decimal places
        results['storage_freed_mb'] = round(results['storage_freed_mb'], 2)

        logger.info(f"Photo cleanup completed: {results}")
        return results

    except Exception as e:
        logger.error(f"Photo cleanup task failed: {str(e)}")
        raise
