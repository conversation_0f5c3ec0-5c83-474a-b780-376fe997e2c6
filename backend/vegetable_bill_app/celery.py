"""
Celery configuration for vegetable_bill_app project.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')

app = Celery('vegetable_bill_app')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery Beat Schedule for periodic tasks
app.conf.beat_schedule = {
    'daily-inventory-check': {
        'task': 'master.tasks.daily_inventory_check',
        'schedule': 60.0 * 60.0 * 24.0,  # Run daily at 8 AM
        'options': {'queue': 'inventory'},
    },
    'generate-sales-forecast': {
        'task': 'master.tasks.generate_sales_forecast',
        'schedule': 60.0 * 60.0 * 24.0,  # Run daily
        'options': {'queue': 'forecast'},
    },
    'process-ai-forecasts': {
        'task': 'master.tasks.process_ai_forecasts',
        'schedule': 60.0 * 60.0 * 2.0,  # Run every 2 hours
        'options': {'queue': 'ai'},
    },
    'cleanup-old-shop-photos': {
        'task': 'master.tasks.cleanup_old_shop_photos',
        'schedule': 60.0 * 60.0 * 24.0,  # Run daily at 2 AM
        'options': {'queue': 'cleanup'},
    },
}

app.conf.timezone = 'UTC'

# Task routing
app.conf.task_routes = {
    'master.tasks.daily_inventory_check': {'queue': 'inventory'},
    'master.tasks.generate_sales_forecast': {'queue': 'forecast'},
    'master.tasks.process_ai_forecasts': {'queue': 'ai'},
    'master.tasks.generate_purchase_order': {'queue': 'purchase'},
    'master.tasks.cleanup_old_shop_photos': {'queue': 'cleanup'},
}

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
