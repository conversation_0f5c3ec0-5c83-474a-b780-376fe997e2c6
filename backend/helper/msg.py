
import json
import requests
from django.utils import timezone

version = 'v18.0'
def capitalize_specific_words(text):
    # Define the words to capitalize
    words_to_capitalize = ['credit', 'debit']
    
    # Loop through the words and replace them with their capitalized versions
    for word in words_to_capitalize:
        text = text.replace(word, word.capitalize())
    
    return text


def sendSalesInvoice():

    headers = {
        'content-type': 'application/json',
        'Authorization': 'Bearer EAATflZBZANgVgBOzHAmirs1VrK08thzTQBSUnwexofPTVoZCgiB8SNl4jZCQlyyLBt8H7ITW6jXrms9dOP9tgElZBg2UOAJxJLdUOwkyKJfkMkN0MM77sXKKRHLwlzPpKaalwQIDU5Hs1UP37tXCkzDuZBZAUr9U8T5xhPYFkbKeCDmy74OkUOfXy94U36HjWECZAI1Fwf00mWaxQhUCjmsmpT71gXzespFtFnpCVybhl00ZD'
    }

    url = 'https://graph.facebook.com/' + version + '/156728714188032/messages'


    template_body = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": "9629174525",
        "type": "template",
        "template": {
            "name": "invoice_bill_copy",
            "language": {
                "code": "en_GB"
            },
            "components": [
                {
                    "type": "header",
                    "parameters": [
                        {
                            "type": "document",
                            "document": {
                                "link": "https://dev-veg.kingwizard.in/api/sales_invoice_print/?invoice_id=1120",
                                "filename": "invoice_1120"
                            }
                        }
                    ]
                },
                {
                    "type": "body",
                    "parameters": [
                        {
                            "type": "text",
                            "text": "Abinesh Thiruppathi"
                        },
                        {
                            "type": "text",
                            "text": "Abinesh Thiruppathi"
                        },
                        {
                            "type": "text",
                            "text": "#2342"
                        },
                        {
                            "type": "date_time",
                            "date_time": {
                                "fallback_value": "2023-02-12"
                            }
                        },
                        {
                            "type": "currency",
                            "currency": {
                                "fallback_value": "1244",
                                "code": "INR",
                                "amount_1000": 1244000
                            }
                        },
                        {
                            "type": "currency",
                            "currency": {
                                "fallback_value": "1244",
                                "code": "INR",
                                "amount_1000": 1244000
                            }
                        },
                        {
                            "type": "text",
                            "text": "Selvi"
                        },
                        {
                            "type": "text",
                            "text": "9629174525"
                        }

                    ]
                }
            ]
        }
    }

    response = requests.post(url, data=json.dumps(
        template_body), headers=headers)
    print(response.text)


def sendTransaction(data):

    headers = {
        'content-type': 'application/json',
        'Authorization': 'Bearer '+data.buyer.user.wp_token
    }

    url = 'https://graph.facebook.com/' + version + '/'+ data.buyer.user.wp_id +'/messages'

    created_at = data.created_at
    fallback_value = created_at.strftime('%B %d, %Y')
    template_body = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": data.buyer.phone_no,
        "type": "template",
        "template": {
            "name": "credit_message",
            "language": {
                "code": "en_GB"
            },
            "components": [
                {
                    "type": "header",
                    "parameters": [
                        {
                            "type": "text",
                            "text": capitalize_specific_words(data.entry_type)
                        }
                    ]
                },
                {
                    "type": "body",
                    "parameters": [
                        {
                            "type": "text",
                            "text": data.buyer.name
                        },
                        {
                            "type": "text",
                            "text": data.entry_type
                        },
                        {
                            "type": "text",
                            "text": data.entry_type
                        },
                        {
                            "type": "currency",
                            "currency": {
                                "fallback_value": data.amount,
                                "code": "INR",
                                "amount_1000": data.amount*1000
                            }
                        },
                        {
                            "type": "date_time",
                            "date_time": {
                                "fallback_value": fallback_value,
                                "day_of_week": created_at.isoweekday(),  # WhatsApp API might expect 1=Monday, 7=Sunday
                                "year": created_at.year,
                                "month": created_at.month,
                                "day_of_month": created_at.day,
                                # The "calendar" field is likely static, depending on your needs
                                "calendar": "GREGORIAN"
                            }
                        },
                        {
                            "type": "text",
                            "text": data.id
                        },
                        {
                            "type": "currency",
                            "currency": {
                                "fallback_value": data.closing_amount,
                                "code": "INR",
                                "amount_1000": data.closing_amount*1000
                            }
                        },
                        {
                            "type": "text",
                            "text": data.buyer.user.company_name
                        }
                    ]
                }
            ]
        }
    }
    response = requests.post(url, data=json.dumps(
        template_body), headers=headers)
    print(response.text)
