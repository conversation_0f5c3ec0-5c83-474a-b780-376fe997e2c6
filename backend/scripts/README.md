# Celery Production Setup Scripts

This directory contains scripts for setting up and managing Celery in production environment on AWS Lightsail with Ubuntu OS and Bitnami image.

## Scripts Overview

### 1. `setup_celery_production.sh`
**Purpose**: One-time setup script to configure Celery with <PERSON>visor for production deployment.

**What it does**:
- Checks prerequisites (Redis, Supervisor, application structure)
- Installs Celery dependencies in virtual environment
- Creates Supervisor configuration files for Celery worker and beat
- Sets proper file permissions for bitnami user
- Starts Celery services
- Tests the setup

### 2. `manage_celery.sh`
**Purpose**: Daily management script for Celery services.

**Available commands**:
- `status` - Show status of all Celery services
- `start` - Start all Celery services
- `stop` - Stop all Celery services
- `restart` - Restart all Celery services
- `logs` - Show recent logs
- `monitor` - Monitor Celery tasks in real-time
- `test` - Test Celery functionality
- `queues` - Show queue information
- `purge` - Purge all tasks from queues (DANGEROUS)

## Prerequisites

Before running these scripts, ensure:

1. **Application deployed** at `/opt/bitnami/apps/king-bill/`
2. **Virtual environment** created at `/opt/bitnami/apps/king-bill/venv/`
3. **Redis server** installed and running
4. **Supervisor** installed (`sudo apt install supervisor`)
5. **PostgreSQL** configured and running
6. **Environment variables** configured in `.env` file

## Usage Instructions

### Initial Setup (Run Once)

```bash
# Make scripts executable
sudo chmod +x /opt/bitnami/apps/king-bill/scripts/setup_celery_production.sh
sudo chmod +x /opt/bitnami/apps/king-bill/scripts/manage_celery.sh

# Run the setup script as root
sudo bash /opt/bitnami/apps/king-bill/scripts/setup_celery_production.sh
```

### Daily Management

```bash
# Check status
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh status

# Restart services
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh restart

# Monitor tasks
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh monitor

# View logs
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh logs

# Test functionality
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh test
```

## What Gets Created

### Supervisor Configuration Files
- `/etc/supervisor/conf.d/king_bill_celery_worker.conf`
- `/etc/supervisor/conf.d/king_bill_celery_beat.conf`

### Log Files
- `/var/log/supervisor/king_bill_celery_worker.log`
- `/var/log/supervisor/king_bill_celery_beat.log`

### Services Created
- `king_bill_celery_worker` - Handles background tasks
- `king_bill_celery_beat` - Handles scheduled tasks

## Celery Queues

The setup configures the following queues:
- **inventory** - Stock level checks and inventory management
- **forecast** - Sales forecasting and prediction tasks
- **ai** - AI-powered analysis and processing
- **purchase** - Purchase order generation and management
- **cleanup** - Maintenance tasks like photo cleanup
- **celery** - Default queue for general tasks

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   ```bash
   sudo chown -R bitnami:bitnami /opt/bitnami/apps/king-bill/
   ```

2. **Redis Connection Issues**
   ```bash
   sudo systemctl status redis-server
   redis-cli ping
   ```

3. **Supervisor Not Starting Services**
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start all
   ```

4. **Virtual Environment Issues**
   ```bash
   # Recreate virtual environment
   cd /opt/bitnami/apps/king-bill/
   sudo rm -rf venv
   sudo -u bitnami python3 -m venv venv
   sudo -u bitnami bash -c "source venv/bin/activate && pip install -r backend/requirements.txt"
   ```

### Checking Logs

```bash
# Supervisor logs
sudo supervisorctl tail -f king_bill_celery_worker
sudo supervisorctl tail -f king_bill_celery_beat

# System logs
sudo journalctl -u supervisor -f
sudo journalctl -u redis-server -f
```

### Manual Testing

```bash
# Test Celery manually
cd /opt/bitnami/apps/king-bill/backend
sudo -u bitnami bash -c "source ../venv/bin/activate && celery -A vegetable_bill_app worker --loglevel=debug"

# Test Redis connection
redis-cli ping

# Test task submission
cd /opt/bitnami/apps/king-bill/backend
sudo -u bitnami bash -c "source ../venv/bin/activate && python manage.py shell"
```

## Security Notes

- Scripts must be run as root to manage system services
- All Celery processes run as `bitnami` user for security
- Log files are owned by `bitnami` user
- Redis is configured for localhost access only

## Maintenance

### Regular Tasks
- Monitor service status daily
- Check log files for errors
- Restart services if needed
- Clean up old log files monthly

### Updates
When updating the application:
1. Stop Celery services
2. Update code and dependencies
3. Restart services
4. Test functionality

```bash
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh stop
# Update application code
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh start
sudo bash /opt/bitnami/apps/king-bill/scripts/manage_celery.sh test
```

## Support

For issues with these scripts:
1. Check the logs using the management script
2. Verify prerequisites are met
3. Review the main documentation in `backend/README.md`
4. Contact the development team

---

**Script Version**: 1.0  
**Last Updated**: June 17, 2025  
**Compatible With**: Ubuntu 20.04+, Bitnami Stack
