#!/bin/bash

# =============================================================================
# King Bill - Celery Production Setup Script
# =============================================================================
# This script configures Celery with Supervisor for production deployment
# Run as root: sudo bash setup_celery_production.sh
# 
# Requirements:
# - Application deployed at /home/<USER>/bill_backend/
# - Virtual environment at /home/<USER>/venv/
# - Redis server installed and running
# - Supervisor installed
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
APP_DIR="/home/<USER>"
BACKEND_DIR="${APP_DIR}/bill_backend"
VENV_DIR="${APP_DIR}/venv"
BITNAMI_USER="bitnami"
SUPERVISOR_CONF_DIR="/etc/supervisor/conf.d"
LOG_DIR="/var/log/supervisor"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
    print_success "Running as root user"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if application directory exists
    if [[ ! -d "$APP_DIR" ]]; then
        print_error "Application directory not found: $APP_DIR"
        exit 1
    fi
    
    # Check if virtual environment exists
    if [[ ! -d "$VENV_DIR" ]]; then
        print_error "Virtual environment not found: $VENV_DIR"
        exit 1
    fi
    
    # Check if backend directory exists
    if [[ ! -d "$BACKEND_DIR" ]]; then
        print_error "Backend directory not found: $BACKEND_DIR"
        exit 1
    fi
    
    # Check if bitnami user exists
    if ! id "$BITNAMI_USER" &>/dev/null; then
        print_error "User $BITNAMI_USER does not exist"
        exit 1
    fi
    
    # Check if supervisor is installed
    if ! command -v supervisorctl &> /dev/null; then
        print_error "Supervisor is not installed. Install with: apt install supervisor"
        exit 1
    fi
    
    # Check if Redis is running
    if ! systemctl is-active --quiet redis-server; then
        print_warning "Redis server is not running. Starting Redis..."
        systemctl start redis-server
        systemctl enable redis-server
    fi
    
    print_success "All prerequisites met"
}

# Function to install Celery dependencies
install_celery_dependencies() {
    print_status "Installing Celery dependencies..."
    
    # Activate virtual environment and install dependencies
    cd "$BACKEND_DIR"
    source "$VENV_DIR/bin/activate"
    
    # Check if celery is installed
    if ! python -c "import celery" &>/dev/null; then
        print_status "Installing Celery..."
        pip install celery==5.3.6 redis==5.0.1 django-celery-beat==2.5.0
    else
        print_success "Celery already installed"
    fi
    
    deactivate
}

# Function to create Celery worker supervisor configuration
create_celery_worker_config() {
    print_status "Creating Celery worker supervisor configuration..."
    
    cat > "$SUPERVISOR_CONF_DIR/king_bill_celery_worker.conf" << EOF
[program:king_bill_celery_worker]
command=$VENV_DIR/bin/celery -A vegetable_bill_app worker --loglevel=info --concurrency=4 --queues=inventory,forecast,ai,purchase,cleanup,celery
directory=$BACKEND_DIR
user=$BITNAMI_USER
numprocs=1
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
killasgroup=true
priority=998
redirect_stderr=true
stdout_logfile=$LOG_DIR/king_bill_celery_worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PATH="$VENV_DIR/bin"
EOF
    
    print_success "Celery worker configuration created"
}

# Function to create Celery beat supervisor configuration
create_celery_beat_config() {
    print_status "Creating Celery beat supervisor configuration..."
    
    cat > "$SUPERVISOR_CONF_DIR/king_bill_celery_beat.conf" << EOF
[program:king_bill_celery_beat]
command=$VENV_DIR/bin/celery -A vegetable_bill_app beat --loglevel=info --scheduler=django_celery_beat.schedulers:DatabaseScheduler
directory=$BACKEND_DIR
user=$BITNAMI_USER
numprocs=1
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
killasgroup=true
priority=999
redirect_stderr=true
stdout_logfile=$LOG_DIR/king_bill_celery_beat.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PATH="$VENV_DIR/bin"
EOF
    
    print_success "Celery beat configuration created"
}

# Function to set proper permissions
set_permissions() {
    print_status "Setting proper permissions..."
    
    # Ensure log directory exists and has proper permissions
    mkdir -p "$LOG_DIR"
    chown -R "$BITNAMI_USER:$BITNAMI_USER" "$LOG_DIR"
    
    # Ensure application directory has proper permissions
    chown -R "$BITNAMI_USER:$BITNAMI_USER" "$APP_DIR"
    
    # Set execute permissions on virtual environment
    chmod +x "$VENV_DIR/bin/celery"
    
    print_success "Permissions set correctly"
}

# Function to update supervisor and start services
start_celery_services() {
    print_status "Starting Celery services..."
    
    # Update supervisor configuration
    supervisorctl reread
    supervisorctl update
    
    # Start Celery services
    supervisorctl start king_bill_celery_worker
    supervisorctl start king_bill_celery_beat
    
    # Check status
    sleep 5
    supervisorctl status king_bill_celery_worker king_bill_celery_beat
    
    print_success "Celery services started"
}

# Function to test Celery setup
test_celery_setup() {
    print_status "Testing Celery setup..."
    
    cd "$BACKEND_DIR"
    source "$VENV_DIR/bin/activate"
    
    # Test Celery worker connection
    if timeout 10 python -c "
from celery import Celery
app = Celery('vegetable_bill_app')
app.config_from_object('django.conf:settings', namespace='CELERY')
result = app.control.ping()
print('Celery workers responding:', bool(result))
" 2>/dev/null; then
        print_success "Celery workers are responding"
    else
        print_warning "Celery workers may not be fully ready yet (this is normal on first start)"
    fi
    
    deactivate
}

# Function to display status and next steps
display_status() {
    echo ""
    echo "=============================================="
    echo "  Celery Production Setup Complete!"
    echo "=============================================="
    echo ""
    echo "Services Status:"
    supervisorctl status king_bill_celery_worker king_bill_celery_beat
    echo ""
    echo "Useful Commands:"
    echo "  Check status:     supervisorctl status"
    echo "  Restart worker:   supervisorctl restart king_bill_celery_worker"
    echo "  Restart beat:     supervisorctl restart king_bill_celery_beat"
    echo "  View logs:        supervisorctl tail -f king_bill_celery_worker"
    echo "  Monitor tasks:    celery -A vegetable_bill_app inspect active"
    echo ""
    echo "Log Files:"
    echo "  Worker: $LOG_DIR/king_bill_celery_worker.log"
    echo "  Beat:   $LOG_DIR/king_bill_celery_beat.log"
    echo ""
    echo "Next Steps:"
    echo "1. Configure scheduled tasks in Django admin"
    echo "2. Test task execution with: python manage.py shell"
    echo "3. Monitor logs for any issues"
    echo ""
}

# Main execution
main() {
    echo "=============================================="
    echo "  King Bill - Celery Production Setup"
    echo "=============================================="
    echo ""
    
    check_root
    check_prerequisites
    install_celery_dependencies
    create_celery_worker_config
    create_celery_beat_config
    set_permissions
    start_celery_services
    test_celery_setup
    display_status
    
    print_success "Celery setup completed successfully!"
}

# Run main function
main "$@"
