#!/bin/bash

# =============================================================================
# King Bill - Celery Management Script
# =============================================================================
# This script provides easy management of Celery services
# Run as root: sudo bash manage_celery.sh [command]
# 
# Available commands:
# - status    : Show status of all Celery services
# - start     : Start all Celery services
# - stop      : Stop all Celery services
# - restart   : Restart all Celery services
# - logs      : Show recent logs
# - monitor   : Monitor Celery tasks in real-time
# - test      : Test Celery functionality
# - queues    : Show queue information
# - purge     : Purge all tasks from queues (DANGEROUS)
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/home/<USER>"
BACKEND_DIR="${APP_DIR}/bill_backend"
VENV_DIR="${APP_DIR}/venv"
BITNAMI_USER="bitnami"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to show service status
show_status() {
    print_header "=== Celery Services Status ==="
    supervisorctl status king_bill_celery_worker king_bill_celery_beat
    echo ""
    
    print_header "=== Redis Status ==="
    systemctl status redis-server --no-pager -l
    echo ""
    
    print_header "=== Quick Health Check ==="
    if redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is responding"
    else
        print_error "Redis is not responding"
    fi
}

# Function to start services
start_services() {
    print_status "Starting Celery services..."
    supervisorctl start king_bill_celery_worker king_bill_celery_beat
    sleep 3
    supervisorctl status king_bill_celery_worker king_bill_celery_beat
    print_success "Celery services started"
}

# Function to stop services
stop_services() {
    print_status "Stopping Celery services..."
    supervisorctl stop king_bill_celery_worker king_bill_celery_beat
    supervisorctl status king_bill_celery_worker king_bill_celery_beat
    print_success "Celery services stopped"
}

# Function to restart services
restart_services() {
    print_status "Restarting Celery services..."
    supervisorctl restart king_bill_celery_worker king_bill_celery_beat
    sleep 3
    supervisorctl status king_bill_celery_worker king_bill_celery_beat
    print_success "Celery services restarted"
}

# Function to show logs
show_logs() {
    print_header "=== Recent Celery Worker Logs ==="
    supervisorctl tail king_bill_celery_worker | tail -20
    echo ""
    
    print_header "=== Recent Celery Beat Logs ==="
    supervisorctl tail king_bill_celery_beat | tail -20
    echo ""
    
    print_status "To follow logs in real-time, use:"
    echo "  supervisorctl tail -f king_bill_celery_worker"
    echo "  supervisorctl tail -f king_bill_celery_beat"
}

# Function to monitor Celery tasks
monitor_tasks() {
    print_header "=== Celery Task Monitoring ==="
    
    cd "$BACKEND_DIR"
    sudo -u "$BITNAMI_USER" bash -c "
        source '$VENV_DIR/bin/activate'
        echo 'Active Tasks:'
        celery -A vegetable_bill_app inspect active
        echo ''
        echo 'Scheduled Tasks:'
        celery -A vegetable_bill_app inspect scheduled
        echo ''
        echo 'Worker Stats:'
        celery -A vegetable_bill_app inspect stats
    "
}

# Function to test Celery
test_celery() {
    print_header "=== Testing Celery Functionality ==="
    
    cd "$BACKEND_DIR"
    sudo -u "$BITNAMI_USER" bash -c "
        source '$VENV_DIR/bin/activate'
        python << 'EOF'
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from celery import current_app
from master.tasks import daily_inventory_check

print('Testing Celery connection...')
try:
    # Test basic connectivity
    result = current_app.control.ping()
    if result:
        print('✅ Celery workers are responding')
        print('Active workers:', len(result))
    else:
        print('❌ No Celery workers responding')
        
    # Test task submission
    print('\\nTesting task submission...')
    task_result = daily_inventory_check.delay()
    print(f'✅ Task submitted successfully. Task ID: {task_result.id}')
    print(f'Task state: {task_result.state}')
    
except Exception as e:
    print(f'❌ Error testing Celery: {str(e)}')
EOF
    "
}

# Function to show queue information
show_queues() {
    print_header "=== Celery Queue Information ==="
    
    cd "$BACKEND_DIR"
    sudo -u "$BITNAMI_USER" bash -c "
        source '$VENV_DIR/bin/activate'
        echo 'Active Queues:'
        celery -A vegetable_bill_app inspect active_queues
        echo ''
        echo 'Registered Tasks:'
        celery -A vegetable_bill_app inspect registered
    "
}

# Function to purge queues (DANGEROUS)
purge_queues() {
    print_warning "This will DELETE ALL pending tasks from all queues!"
    read -p "Are you sure you want to continue? (type 'yes' to confirm): " confirm
    
    if [[ "$confirm" == "yes" ]]; then
        print_status "Purging all queues..."
        cd "$BACKEND_DIR"
        sudo -u "$BITNAMI_USER" bash -c "
            source '$VENV_DIR/bin/activate'
            celery -A vegetable_bill_app purge -f
        "
        print_success "All queues purged"
    else
        print_status "Operation cancelled"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Available commands:"
    echo "  status    - Show status of all Celery services"
    echo "  start     - Start all Celery services"
    echo "  stop      - Stop all Celery services"
    echo "  restart   - Restart all Celery services"
    echo "  logs      - Show recent logs"
    echo "  monitor   - Monitor Celery tasks in real-time"
    echo "  test      - Test Celery functionality"
    echo "  queues    - Show queue information"
    echo "  purge     - Purge all tasks from queues (DANGEROUS)"
    echo ""
    echo "Examples:"
    echo "  sudo $0 status"
    echo "  sudo $0 restart"
    echo "  sudo $0 monitor"
}

# Main function
main() {
    check_root
    
    case "${1:-}" in
        "status")
            show_status
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs
            ;;
        "monitor")
            monitor_tasks
            ;;
        "test")
            test_celery
            ;;
        "queues")
            show_queues
            ;;
        "purge")
            purge_queues
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        "")
            print_error "No command specified"
            echo ""
            show_usage
            exit 1
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
