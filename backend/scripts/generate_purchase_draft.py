#!/usr/bin/env python
"""
Script to generate purchase order drafts using Celery tasks.
"""
import os
import django
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from master.tasks import daily_inventory_check, generate_purchase_order
from master.models import Product, User, PurchaseOrderDraft, PurchaseOrderDraftItem

def check_low_stock_products():
    """Check for products with low stock."""
    print("🔍 Checking for low stock products...")
    
    from django.db.models import F
    
    low_stock_products = Product.objects.filter(
        active=True,
        stock_quantity__lt=F('min_stock_threshold'),
        user__isnull=False  # Only products with valid users
    ).select_related('user')

    print(f"📊 Found {low_stock_products.count()} products with low stock:")

    for product in low_stock_products[:10]:  # Show first 10
        user_name = product.user.username if product.user else "No User"
        print(f"   • {product.name} (User: {user_name})")
        print(f"     Current: {product.stock_quantity}, Min: {product.min_stock_threshold}")
    
    if low_stock_products.count() > 10:
        print(f"   ... and {low_stock_products.count() - 10} more")
    
    return low_stock_products

def generate_draft_via_inventory_check():
    """Generate purchase drafts via daily inventory check task."""
    print("\n🚀 Generating purchase drafts via inventory check task...")
    
    try:
        # Submit the daily inventory check task
        result = daily_inventory_check.delay()
        print(f"✅ Inventory check task submitted: {result.id}")
        print(f"📊 Task state: {result.state}")
        
        # Try to get result with a short timeout
        try:
            task_result = result.get(timeout=10)
            print(f"📋 Task completed with result: {task_result}")
        except Exception as e:
            print(f"⏳ Task is running in background (timeout: {str(e)})")
            print("   Check task status later or monitor worker logs")
        
        return result.id
        
    except Exception as e:
        print(f"❌ Error submitting inventory check task: {str(e)}")
        return None

def generate_draft_for_specific_user():
    """Generate purchase draft for a specific user's products."""
    print("\n🎯 Generating purchase draft for specific user...")
    
    try:
        # Get a user with products
        users_with_products = User.objects.filter(
            product__isnull=False,
            product__active=True
        ).distinct()[:1]
        
        if not users_with_products:
            print("❌ No users with active products found")
            return None
            
        user = users_with_products[0]
        print(f"👤 Selected user: {user.username}")
        
        # Get some products for this user
        products = Product.objects.filter(
            user=user,
            active=True
        )[:5]  # Take first 5 products
        
        if not products:
            print(f"❌ No active products found for user {user.username}")
            return None
            
        product_ids = [p.id for p in products]
        print(f"📦 Selected products: {[p.name for p in products]}")
        
        # Submit the purchase order generation task
        result = generate_purchase_order.delay(user.id, product_ids)
        print(f"✅ Purchase order task submitted: {result.id}")
        print(f"📊 Task state: {result.state}")
        
        # Try to get result with a short timeout
        try:
            task_result = result.get(timeout=15)
            print(f"📋 Task completed with result: {task_result}")
        except Exception as e:
            print(f"⏳ Task is running in background (timeout: {str(e)})")
            print("   Check task status later or monitor worker logs")
        
        return result.id
        
    except Exception as e:
        print(f"❌ Error generating purchase order: {str(e)}")
        return None

def check_existing_drafts():
    """Check existing purchase order drafts."""
    print("\n📋 Checking existing purchase order drafts...")
    
    drafts = PurchaseOrderDraft.objects.all().order_by('-created_on')[:10]
    
    if drafts:
        print(f"📊 Found {PurchaseOrderDraft.objects.count()} total drafts. Recent ones:")
        for draft in drafts:
            items_count = draft.items.count()
            print(f"   • Draft #{draft.id} - {draft.supplier.name}")
            print(f"     Created: {draft.created_on.strftime('%Y-%m-%d %H:%M')}")
            print(f"     Items: {items_count}, Amount: ₹{draft.total_estimated_amount:.2f}")
            print(f"     Auto-generated: {draft.auto_generated}, Status: {draft.status}")
    else:
        print("📭 No purchase order drafts found")
    
    return drafts

def show_draft_details(draft_id=None):
    """Show details of a specific draft or the latest one."""
    if draft_id:
        try:
            draft = PurchaseOrderDraft.objects.get(id=draft_id)
        except PurchaseOrderDraft.DoesNotExist:
            print(f"❌ Draft with ID {draft_id} not found")
            return
    else:
        draft = PurchaseOrderDraft.objects.order_by('-created_on').first()
        if not draft:
            print("❌ No drafts found")
            return
    
    print(f"\n📄 Purchase Order Draft #{draft.id} Details:")
    print(f"   Supplier: {draft.supplier.name}")
    print(f"   User: {draft.user.username}")
    print(f"   Created: {draft.created_on.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   Status: {draft.status}")
    print(f"   Auto-generated: {draft.auto_generated}")
    print(f"   Forecast-based: {draft.forecast_based}")
    print(f"   Total Amount: ₹{draft.total_estimated_amount:.2f}")
    
    items = draft.items.all()
    print(f"\n📦 Items ({items.count()}):")
    
    for item in items:
        print(f"   • {item.product.name}")
        print(f"     Quantity: {item.quantity}")
        print(f"     Rate: ₹{item.estimated_rate:.2f}")
        print(f"     Total: ₹{item.estimated_total:.2f}")
        print(f"     Current Stock: {item.current_stock}")
        print(f"     Min Threshold: {item.min_stock_threshold}")
        print(f"     Shortage: {item.shortage_quantity}")
        if item.reorder_reason:
            print(f"     Reason: {item.reorder_reason}")
        print()

def main():
    """Main function to generate purchase drafts."""
    print("🚀 Purchase Order Draft Generation")
    print("=" * 50)
    
    # Check current state
    check_low_stock_products()
    check_existing_drafts()
    
    # Generate drafts using different methods
    print("\n" + "=" * 50)
    print("🔧 Generating New Purchase Drafts")
    
    # Method 1: Via inventory check (recommended)
    task_id_1 = generate_draft_via_inventory_check()
    
    # Method 2: For specific user
    task_id_2 = generate_draft_for_specific_user()
    
    # Wait a moment and check results
    print("\n" + "=" * 50)
    print("⏳ Waiting for tasks to complete...")
    
    import time
    time.sleep(3)
    
    # Check drafts again
    print("\n📋 Checking drafts after generation...")
    new_drafts = check_existing_drafts()
    
    # Show details of latest draft
    if new_drafts:
        show_draft_details()
    
    print("\n" + "=" * 50)
    print("🎯 Purchase Draft Generation Complete!")
    
    if task_id_1 or task_id_2:
        print("📝 Task IDs for monitoring:")
        if task_id_1:
            print(f"   Inventory Check: {task_id_1}")
        if task_id_2:
            print(f"   Specific User: {task_id_2}")
        
        print("\n💡 Monitor tasks with:")
        print("   celery -A vegetable_bill_app events")
        print("   or check Redis: redis-cli keys 'celery-task-meta-*'")

if __name__ == "__main__":
    main()
