#!/usr/bin/env python
"""
Manual purchase draft generation script.
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from django.db import transaction
from django.db.models import F
from master.models import (
    Product, User, Suplier, PurchaseOrderDraft, PurchaseOrderDraftItem,
    SupplierPreference, SalesForecast
)

def get_preferred_supplier(product):
    """Get the preferred supplier for a product."""
    # Try to get preferred supplier
    preference = SupplierPreference.objects.filter(
        product=product,
        is_preferred=True
    ).order_by('priority').first()
    
    if preference:
        return preference.supplier
    
    # Fallback to any supplier with preferences for this product
    fallback_preference = SupplierPreference.objects.filter(
        product=product
    ).order_by('priority').first()
    
    if fallback_preference:
        return fallback_preference.supplier
    
    # Last resort: get any active supplier for this user
    fallback_supplier = Suplier.objects.filter(
        user=product.user,
        active=True
    ).first()
    
    return fallback_supplier

def generate_purchase_draft_manually():
    """Manually generate purchase drafts for low stock products."""
    print("🚀 Manual Purchase Draft Generation")
    print("=" * 50)
    
    # Get low stock products
    low_stock_products = Product.objects.filter(
        active=True,
        stock_quantity__lt=F('min_stock_threshold'),
        user__isnull=False
    ).select_related('user')[:10]  # Limit to 10 for testing
    
    print(f"📊 Found {low_stock_products.count()} low stock products to process")
    
    if not low_stock_products:
        print("📭 No low stock products found")
        return
    
    # Group products by user and supplier
    products_by_user_supplier = {}
    
    for product in low_stock_products:
        user = product.user
        supplier = get_preferred_supplier(product)
        
        if not supplier:
            print(f"⚠️  No supplier found for product {product.name}")
            continue
            
        key = (user.id, supplier.id)
        if key not in products_by_user_supplier:
            products_by_user_supplier[key] = {
                'user': user,
                'supplier': supplier,
                'products': []
            }
        products_by_user_supplier[key]['products'].append(product)
    
    print(f"📦 Grouped into {len(products_by_user_supplier)} user-supplier combinations")
    
    # Generate drafts
    drafts_created = 0
    
    for (user_id, supplier_id), group in products_by_user_supplier.items():
        user = group['user']
        supplier = group['supplier']
        products = group['products']
        
        print(f"\n🏭 Creating draft for {user.username} → {supplier.name}")
        print(f"   Products: {[p.name for p in products]}")
        
        try:
            with transaction.atomic():
                # Create draft PO
                draft = PurchaseOrderDraft.objects.create(
                    user=user,
                    supplier=supplier,
                    auto_generated=True,
                    forecast_based=True,
                    status='draft'
                )
                
                total_estimated_amount = 0
                items_created = 0
                
                # Add items to draft
                for product in products:
                    shortage_qty = max(0, product.min_stock_threshold - product.stock_quantity)
                    
                    # Get latest forecast if available
                    latest_forecast = SalesForecast.objects.filter(
                        product=product
                    ).order_by('-forecast_date').first()
                    
                    forecast_qty = latest_forecast.final_forecast_quantity if latest_forecast else 0
                    
                    # Calculate order quantity
                    lead_time_days = getattr(product, 'lead_time_days', 7)
                    safety_stock = getattr(product, 'safety_stock', 0)
                    
                    lead_time_demand = forecast_qty * (lead_time_days / 30) if forecast_qty else 0
                    order_qty = shortage_qty + lead_time_demand + safety_stock
                    
                    # Ensure minimum order quantity
                    if order_qty < shortage_qty:
                        order_qty = shortage_qty
                    
                    estimated_rate = product.pr_rate or 0
                    estimated_total = order_qty * estimated_rate
                    total_estimated_amount += estimated_total
                    
                    # Create draft item
                    draft_item = PurchaseOrderDraftItem.objects.create(
                        draft=draft,
                        product=product,
                        quantity=order_qty,
                        estimated_rate=estimated_rate,
                        estimated_total=estimated_total,
                        forecast_quantity=forecast_qty,
                        current_stock=product.stock_quantity,
                        min_stock_threshold=product.min_stock_threshold,
                        shortage_quantity=shortage_qty,
                        reorder_reason=f"Stock below minimum threshold. Current: {product.stock_quantity}, Min: {product.min_stock_threshold}",
                        auto_generated=True
                    )
                    
                    items_created += 1
                    print(f"     ✅ {product.name}: Qty {order_qty}, Rate ₹{estimated_rate}")
                
                # Update draft total
                draft.total_estimated_amount = total_estimated_amount
                draft.save()
                
                print(f"   📄 Draft #{draft.id} created with {items_created} items")
                print(f"   💰 Total estimated amount: ₹{total_estimated_amount:.2f}")
                
                drafts_created += 1
                
        except Exception as e:
            print(f"   ❌ Error creating draft: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎯 Summary: {drafts_created} purchase order drafts created")
    
    # Show created drafts
    if drafts_created > 0:
        print("\n📋 Created Drafts:")
        recent_drafts = PurchaseOrderDraft.objects.filter(
            auto_generated=True
        ).order_by('-created_on')[:drafts_created]
        
        for draft in recent_drafts:
            print(f"   📄 Draft #{draft.id}")
            print(f"      Supplier: {draft.supplier.name}")
            print(f"      User: {draft.user.username}")
            print(f"      Items: {draft.items.count()}")
            print(f"      Amount: ₹{draft.total_estimated_amount:.2f}")
            print(f"      Created: {draft.created_on.strftime('%Y-%m-%d %H:%M:%S')}")

def check_draft_details():
    """Check details of created drafts."""
    print("\n🔍 Checking Draft Details")
    print("=" * 30)
    
    latest_draft = PurchaseOrderDraft.objects.order_by('-created_on').first()
    
    if not latest_draft:
        print("📭 No drafts found")
        return
    
    print(f"📄 Latest Draft #{latest_draft.id}:")
    print(f"   Supplier: {latest_draft.supplier.name}")
    print(f"   User: {latest_draft.user.username}")
    print(f"   Created: {latest_draft.created_on.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   Status: {latest_draft.status}")
    print(f"   Total: ₹{latest_draft.total_estimated_amount:.2f}")
    
    items = latest_draft.items.all()
    print(f"\n📦 Items ({items.count()}):")
    
    for item in items[:5]:  # Show first 5 items
        print(f"   • {item.product.name}")
        print(f"     Qty: {item.quantity}, Rate: ₹{item.estimated_rate}")
        print(f"     Stock: {item.current_stock}/{item.min_stock_threshold}")
        print(f"     Shortage: {item.shortage_quantity}")
    
    if items.count() > 5:
        print(f"   ... and {items.count() - 5} more items")

if __name__ == "__main__":
    generate_purchase_draft_manually()
    check_draft_details()
