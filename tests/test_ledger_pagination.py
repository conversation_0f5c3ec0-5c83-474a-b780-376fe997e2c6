#!/usr/bin/env python3
"""
Test script to verify ledger pagination fix
"""

import requests
import json

# Test configuration
BASE_URL = "https://billing-api.kingwizard.in/api"
# You'll need to replace these with actual test credentials
USERNAME = "test_user"
PASSWORD = "test_password"

def test_ledger_pagination():
    """Test ledger pagination with different page numbers"""
    
    # First, get authentication token
    auth_response = requests.post(f"{BASE_URL}/auth/login/", {
        "username": USERNAME,
        "password": PASSWORD
    })
    
    if auth_response.status_code != 200:
        print("❌ Authentication failed")
        print(f"Status: {auth_response.status_code}")
        print(f"Response: {auth_response.text}")
        return False
    
    auth_data = auth_response.json()
    if not auth_data.get('success'):
        print("❌ Authentication failed")
        print(f"Response: {auth_data}")
        return False
    
    token = auth_data.get('token')
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    print("✅ Authentication successful")
    
    # Test cases
    test_cases = [
        {"page_number": 1, "page_size": 30, "buyer": "true", "description": "First page"},
        {"page_number": 2, "page_size": 30, "buyer": "true", "description": "Second page"},
        {"page_number": 10, "page_size": 30, "buyer": "true", "description": "High page number (should fail gracefully)"},
        {"page_number": 1, "page_size": 10, "buyer": "true", "description": "Smaller page size"},
        {"page_number": 1, "page_size": 30, "buyer": "false", "description": "Supplier view"},
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['description']}")
        
        params = {
            'page_number': test_case['page_number'],
            'page_size': test_case['page_size'],
            'buyer': test_case['buyer']
        }
        
        try:
            response = requests.get(f"{BASE_URL}/ledger/", headers=headers, params=params)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("   ✅ Success response")
                    if 'data' in data.get('data', {}):
                        paginated_data = data['data']['data']
                        print(f"   📊 Items returned: {len(paginated_data.get('data', []))}")
                        print(f"   📊 Total count: {paginated_data.get('count', 0)}")
                else:
                    print("   ❌ API returned error")
                    print(f"   📝 Message: {data.get('message', 'No message')}")
            else:
                print("   ❌ HTTP error")
                print(f"   📝 Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
    
    print("\n🎯 Pagination test completed!")

if __name__ == "__main__":
    print("🚀 Starting Ledger Pagination Test")
    print("=" * 50)
    test_ledger_pagination() 