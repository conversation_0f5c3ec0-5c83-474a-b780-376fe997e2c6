#!/usr/bin/env python
"""
Simple test to create a purchase draft directly.
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from master.models import Product, User, Suplier, PurchaseOrderDraft, PurchaseOrderDraftItem

def create_simple_draft():
    """Create a simple purchase draft for testing."""
    print("🧪 Creating Simple Purchase Draft")
    print("=" * 40)
    
    try:
        # Get a user
        user = User.objects.first()
        if not user:
            print("❌ No users found")
            return
        
        print(f"👤 User: {user.username}")
        
        # Get a supplier
        supplier = Suplier.objects.filter(active=True).first()
        if not supplier:
            print("❌ No active suppliers found")
            return
        
        print(f"🏭 Supplier: {supplier.name}")
        
        # Get a product
        product = Product.objects.filter(active=True, user=user).first()
        if not product:
            print("❌ No active products found for user")
            return
        
        print(f"📦 Product: {product.name}")
        
        # Create draft
        draft = PurchaseOrderDraft.objects.create(
            user=user,
            supplier=supplier,
            auto_generated=True,
            forecast_based=False,
            total_estimated_amount=1000.0,
            status='draft'
        )
        
        print(f"📄 Created draft #{draft.id}")
        
        # Create draft item
        draft_item = PurchaseOrderDraftItem.objects.create(
            draft=draft,
            product=product,
            quantity=10.0,
            estimated_rate=100.0,
            estimated_total=1000.0,
            current_stock=5.0,
            min_stock_threshold=15.0,
            shortage_quantity=5.0,
            reorder_reason="Test draft creation",
            auto_generated=True
        )
        
        print(f"📦 Created draft item #{draft_item.id}")
        
        # Verify creation
        print("\n✅ Draft created successfully!")
        print(f"   Draft ID: {draft.id}")
        print(f"   Supplier: {draft.supplier.name}")
        print(f"   User: {draft.user.username}")
        print(f"   Items: {draft.items.count()}")
        print(f"   Total: ₹{draft.total_estimated_amount}")
        
        return draft
        
    except Exception as e:
        print(f"❌ Error creating draft: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def check_all_drafts():
    """Check all existing drafts."""
    print("\n📋 All Purchase Order Drafts")
    print("=" * 30)
    
    drafts = PurchaseOrderDraft.objects.all().order_by('-created_on')
    
    print(f"📊 Total drafts: {drafts.count()}")
    
    for draft in drafts:
        print(f"\n📄 Draft #{draft.id}")
        print(f"   Supplier: {draft.supplier.name}")
        print(f"   User: {draft.user.username}")
        print(f"   Created: {draft.created_on.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Status: {draft.status}")
        print(f"   Auto-generated: {draft.auto_generated}")
        print(f"   Items: {draft.items.count()}")
        print(f"   Total: ₹{draft.total_estimated_amount}")

if __name__ == "__main__":
    # Check existing drafts first
    check_all_drafts()
    
    # Create a new draft
    new_draft = create_simple_draft()
    
    # Check drafts again
    if new_draft:
        check_all_drafts()
