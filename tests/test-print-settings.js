// Test script to verify print settings functionality
// Run this in browser console after navigating to the app

// Mock invoice data for testing
const mockInvoiceData = {
    id: 123,
    name: "Test Customer",
    date: "2025-06-18",
    phone_no: "9876543210",
    place: "Test City",
    gst_no: "29ABCDE1234F1Z5",
    previous_balance: 100.00,
    bill_amount: 500.00,
    received_amount: 300.00,
    current_balance: 300.00,
    gross_total: 500.00,
    rounding_enabled: true,
    rounding_adjustment: 0.50,
    delivery_challan: false,
    sales_invoice_items: [
        {
            product_name: "Test Product 1",
            hsn_code: "1234",
            no: 2,
            weight: 10,
            line_total: 200.00,
            rate: 100.00,
            mrp: 110.00,
            tax_rate: 18
        },
        {
            product_name: "Test Product 2",
            hsn_code: "5678",
            no: 1,
            weight: 5,
            line_total: 300.00,
            rate: 300.00,
            mrp: 330.00,
            tax_rate: 12
        }
    ]
};

// Function to test print settings
function testPrintSettings() {
    console.log('=== Testing Print Settings ===');
    
    // Get the print service instance
    const printService = document.querySelector('app-root')?._ngElementStrategy?.componentRef?.injector?.get('PrintServiceService');
    
    if (!printService) {
        console.error('Print service not found. Make sure you are on the app page.');
        return;
    }
    
    console.log('Print service found:', printService);
    
    // Test different paper size settings
    const testCases = [
        { paperType: 'Regular', paperSize: 'A4', description: 'A4 Regular Printing' },
        { paperType: 'Regular', paperSize: 'A5', description: 'A5 Regular Printing' },
        { paperType: 'Thermal', paperSize: '58mm', description: 'Thermal 58mm Printing' },
        { paperType: 'Thermal', paperSize: '80mm', description: 'Thermal 80mm Printing' }
    ];
    
    testCases.forEach((testCase, index) => {
        console.log(`\n--- Test Case ${index + 1}: ${testCase.description} ---`);
        
        // Set the paper settings
        localStorage.setItem('paper_type', testCase.paperType);
        localStorage.setItem('paper_size', testCase.paperSize);
        
        console.log(`Set paper_type: ${testCase.paperType}`);
        console.log(`Set paper_size: ${testCase.paperSize}`);
        
        // Test the getHtmlContent method
        printService.getHtmlContent(mockInvoiceData).then(htmlContent => {
            console.log(`HTML content length: ${htmlContent.length} characters`);
            
            // Check which format was used based on content
            if (htmlContent.includes('width:210mm')) {
                console.log('✅ A4 format detected');
            } else if (htmlContent.includes('width:148mm')) {
                console.log('✅ A5 format detected');
            } else if (htmlContent.includes('width:100%') && htmlContent.includes('font-size:12px')) {
                console.log('✅ Mobile/Thermal format detected');
            } else {
                console.log('❓ Unknown format detected');
            }
        }).catch(error => {
            console.error('Error getting HTML content:', error);
        });
    });
    
    // Reset to default settings
    localStorage.setItem('paper_type', 'Regular');
    localStorage.setItem('paper_size', 'A4');
    console.log('\n--- Reset to default settings (Regular A4) ---');
}

// Function to test print method directly
function testPrintMethod() {
    console.log('\n=== Testing Print Method ===');
    
    const printService = document.querySelector('app-root')?._ngElementStrategy?.componentRef?.injector?.get('PrintServiceService');
    
    if (!printService) {
        console.error('Print service not found.');
        return;
    }
    
    // Test with A4 settings
    localStorage.setItem('paper_type', 'Regular');
    localStorage.setItem('paper_size', 'A4');
    
    console.log('Testing print method with A4 settings...');
    console.log('Check browser console for print method logs');
    
    // This will trigger the print method and show console logs
    printService.print(mockInvoiceData, false, 'Test-Invoice-A4');
}

// Function to show current settings
function showCurrentSettings() {
    console.log('\n=== Current Print Settings ===');
    console.log('paper_type:', localStorage.getItem('paper_type'));
    console.log('paper_size:', localStorage.getItem('paper_size'));
}

// Export functions to global scope for easy testing
window.testPrintSettings = testPrintSettings;
window.testPrintMethod = testPrintMethod;
window.showCurrentSettings = showCurrentSettings;
window.mockInvoiceData = mockInvoiceData;

console.log('Print testing functions loaded!');
console.log('Available functions:');
console.log('- testPrintSettings() - Test different paper size settings');
console.log('- testPrintMethod() - Test the print method directly');
console.log('- showCurrentSettings() - Show current print settings');
console.log('- mockInvoiceData - Sample invoice data for testing');
