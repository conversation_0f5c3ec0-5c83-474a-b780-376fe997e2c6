  // win.document.body.innerHTML = `<!DOCTYPE html>
        // <html>
        
        // <head>
        //     <title>Invoice Print</title>
        //     <meta charset="UTF-8" />
        //     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
        //     <style>
        //         * {
        //             font-family: "arial";
        //             width: 100%;
        //             box-sizing: border-box;
        //         }
        
        //         .sheet {
        
        //             page-break-after: auto !important;
        //         }
        
        //         html,
        //         body {
        //             height: 100%;
        //             margin: 0 !important;
        //             padding: 0 !important;
        //             overflow: hidden;
        //         }
        
        //         .page {
        //             page-break-after: avoid;
        //             page-break-inside: avoid;
        //         }
        
        //         .container {
        //             display: table;
        //         }
        
        //         .container:nth-child(21) {
        //             page-break-after: always;
        //             page-break-inside: avoid;
        //         }
        
        //         .container {
        //             font-size: 0;
        //         }
        
        //         .header-item {
        //             display: inline-block;
        //             font-size: 22px !important;
        //         }
        
        //         div {
        //             box-sizing: border-box;
        //         }
        
        //         .page {
        //             min-height: 600px;
        //         }
        
        //         @page {
        //             size: A5;
        
        //         }
        
        
        //         @media print {
        
        //             body {
        //                 margin: 0;
        //                 zoom: 65%;
        //             }
        
        
        
        //             thead {
        //                 display: table-header-group;
        //             }
        
        //             tfoot {
        //                 display: table-footer-group;
        //             }
        //         }
        //     </style>
        // </head>
        
        // <body class="A5">
        //     <table class="sheet padding-10mm" cellspacing="0" cellpadding="0" width="100%" style="width: 100%;">
        //         <thead>
        //             <tr>
        //                 <td width="100%">
        //                     <div style="display: flex;width: 100%;height:70%;">
        //                         <div style="width:auto;"> <img
        //                                 src="${environment.apiUrlClean + localStorage.getItem("company_logo")}"
        //                                 style="width: 125px;height: 140px;">
        //                         </div>
        //                         <div style="widht:100%;height:100%;margin-top: -25px;text-align: center;">
        //                             <div style="display:flex;"><h4 style="padding:0;text-align: start;">Phone No:-${localStorage.getItem('contact_no_left')}</h4><h4 style="padding:0;text-align: right;">Phone No:-${localStorage.getItem('contact_no_right')}</h4></div>
        //                             <h1 style="padding: 0px;margin: 0px;">${localStorage.getItem("company_name")}</h1>
        //                             <h4>${localStorage.getItem("address")}</h4>
        //                         </div>
        //                     </div>
        //                     <div style="display: flex;">
        //                         <div
        //                             style="width: 70%;margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid;border-bottom:none; border-right:none;">
        //                             <p style="margin: 0; font-size: 25px;font-weight: 300;">To</p>
        //                             <h1 style="margin: 0; font-size: 30px;">${data.name}</h1>
        //                             <div style="display:flex;">
        //                                 <!--  <h4 style="margin: 0; font-size: 25px;font-weight: 300;">
        //                                         <strong> Phone : </strong> ${data.phone_no}
        //                                     </h4> -->
        //                                 <h4 style="margin: 0; font-size: 25px;font-weight: 300;">
        //                                     ${data.place}
        //                                 </h4>
        //                             </div>
        //                         </div>
        //                         <div style="width: 30%;margin-top: 3px; padding: 10px; border: 0.5px solid; border-bottom:none; display: flex;"
        //                             align="center">
        //                             <div class="content" style="text-align: left; width: 100%;">
        //                                 <p style="font-size: 25px;font-weight: 300;margin: 0;">
        //                                     No &emsp;: ${data.id}
        //                                 </p>
        //                                 <p style="font-size: 25px;font-weight: 300;margin: 0;">
        //                                     Date &nbsp;: ${data.date}
        //                                 </p>
        //                             </div>
        //                         </div>
        //                     </div>
        //                     </div>
        //                 </td>
        //             </tr>
        //         </thead>
        //         <tbody>
        //             <tr>
        //                 <td>
        //                     <div class="page" style="border: 0.5px solid;position:relative;padding-bottom:30px">
        //                         <div class="container" style="width: 100%;">
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 5%; font-size: 16px; padding: 3px;">
        //                                 <strong> Sr. </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 10%; font-size: 16px; padding: 3px;">
        //                                 <strong> Rate </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 45%; font-size: 16px; padding: 3px;">
        //                                 <strong> Particulars </strong>
        //                             </div>
        
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 14%; font-size: 16px; padding: 3px;">
        //                                 <strong> Box </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-bottom:0.5px solid; width: 8%; font-size: 16px; padding: 3px;">
        //                                 <strong> Pcs </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-bottom:0.5px solid; width: 18%; font-size: 16px; padding: 3px;">
        //                                 <strong> Amount </strong>
        //                             </div>
        //                         </div>
        //                         <div class="container" style="width: 100%;border-bottom:0.5px solid;">
        //                             ${items}
        //                         </div>
        //                         <div class="container" style="width: 100%;position:absolute;bottom:0;left:0;">
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-top:0.5px solid; width: 5%; font-size: 16px; padding: 3px;">
        //                                 <strong> &nbsp; </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-top:0.5px solid; width: 10%; font-size: 16px; padding: 3px;">
        //                                 <strong> &nbsp; </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-top:0.5px solid; width: 45%; font-size: 16px; padding: 3px;">
        //                                 <strong> Total </strong>
        //                             </div>
        
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-top:0.5px solid; width: 14%; font-size: 16px; padding: 3px;">
        //                                 <strong> ${totalWeight} </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-right: 0.5px solid; border-top:0.5px solid; width: 8%; font-size: 16px; padding: 3px;">
        //                                 <strong> ${totalNo} </strong>
        //                             </div>
        //                             <div class="header-item"
        //                                 style="border-top:0.5px solid; width: 18%; font-size: 16px; padding: 3px;">
        //                                 <strong> ${totalAmount} </strong>
        //                             </div>
        //                         </div>
        //                     </div>
        //                 </td>
        //             </tr>
        //         </tbody>
        //         <tfoot>
        //             <tr>
        //                 <td>
        //                     <div id="page-footer">
        //                         <div style="display: flex;">
        //                             <div
        //                                 style="display: flex; flex-direction: column; width: 20%; border: 0.5px solid; padding-top: 5px;border-top:none;border-right:none;">
        //                                 <!--  <div style="display: flex;">
        //                                         <p
        //                                             style="margin: 5px; font-size: 25px; width: 50%; text-align: left; padding-left: 10px;">
        //                                             Notes:</p>
        //                                         <p
        //                                             style="margin: 5px; font-size: 14px; width: 50%; text-align: left; padding-right: 10px;">
        //                                         </p>
        //                                     </div>-->
        //                             </div>
        //                             <div
        //                                 style="display: flex; flex-direction: column; width: 80%; border: 0.5px solid; padding-top: 5px;border-top:none;border-left:none;">
        //                                 <div style="display: flex;">
        //                                     <p
        //                                         style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
        //                                         Previous Balance(முன் பாக்கி)</p>
        //                                     <p
        //                                         style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
        //                                         : ${data.previous_balance} </p>
        //                                 </div>
        //                                 <div style="display: flex;">
        //                                     <p
        //                                         style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
        //                                         Bill Amount</p>
        //                                     <p
        //                                         style="margin: 5px; font-size: 20px; font-weight:900;font-weight:1200em;width: 50%; text-align: left; padding-right: 10px;">
        //                                         : ${data.bill_amount}</p>
        //                                 </div>
        //                                 <div style="display: flex;">
        //                                     <p
        //                                         style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
        //                                         Received Amount(வரவு)</p>
        //                                     <p
        //                                         style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
        //                                         : ${data.received_amount}</p>
        //                                 </div>
        //                                 <div style="display: flex;">
        //                                     <p
        //                                         style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
        //                                         Closing Balance(பாக்கி)</p>
        //                                     <p
        //                                         style="margin: 5px; font-size: 30px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
        //                                         : <b>${data.current_balance}</b> </p>
        //                                 </div>
        //                             </div>
        //                         </div>
        //                     </div>
        //                 </td>
        //             </tr>
        //         </tfoot>
        //     </table>
        // </body>
        
        // </html>`;