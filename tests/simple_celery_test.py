#!/usr/bin/env python
"""
Simple Celery test script that submits tasks without waiting for results.
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

def test_celery_tasks():
    """Submit Celery tasks and check their status."""
    print("🚀 Starting Simple Celery Test")
    print("=" * 40)
    
    try:
        # Test 1: Debug task
        print("🧪 Testing debug task...")
        from vegetable_bill_app.celery import debug_task
        result1 = debug_task.delay()
        print(f"✅ Debug task submitted: {result1.id}")
        print(f"📊 Task state: {result1.state}")
        
        # Test 2: Sales forecast task
        print("\n🧪 Testing sales forecast task...")
        from master.tasks import generate_sales_forecast
        result2 = generate_sales_forecast.delay()
        print(f"✅ Sales forecast submitted: {result2.id}")
        print(f"📊 Task state: {result2.state}")
        
        # Test 3: AI forecast task
        print("\n🧪 Testing AI forecast task...")
        from master.tasks import process_ai_forecasts
        result3 = process_ai_forecasts.delay()
        print(f"✅ AI forecast submitted: {result3.id}")
        print(f"📊 Task state: {result3.state}")
        
        # Test 4: Inventory check task
        print("\n🧪 Testing inventory check task...")
        from master.tasks import daily_inventory_check
        result4 = daily_inventory_check.delay()
        print(f"✅ Inventory check submitted: {result4.id}")
        print(f"📊 Task state: {result4.state}")
        
        print("\n" + "=" * 40)
        print("🎯 All tasks submitted successfully!")
        print("📝 Task IDs for monitoring:")
        print(f"   Debug: {result1.id}")
        print(f"   Sales Forecast: {result2.id}")
        print(f"   AI Forecast: {result3.id}")
        print(f"   Inventory Check: {result4.id}")
        
        print("\n💡 You can monitor these tasks using:")
        print("   celery -A vegetable_bill_app events")
        print("   or check Redis directly")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_celery_tasks()
