#!/usr/bin/env python3
"""
Test script for Brand-Based Product Filtering Implementation
This script tests the backend API endpoints to ensure they work correctly.
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

# Test data
TEST_SUPPLIER_ID = 1
TEST_BRAND_ID = 1

def test_supplier_brand_endpoints():
    """Test the supplier-brand relationship endpoints"""
    print("🧪 Testing Supplier-Brand Endpoints...")
    
    # Test 1: Get supplier brands
    print("\n1. Testing GET /supplier_brand/")
    try:
        response = requests.get(f"{API_BASE}/supplier_brand/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
            print(f"   Data count: {len(data.get('data', []))}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 2: Create supplier-brand relationship
    print("\n2. Testing POST /supplier_brand/")
    try:
        payload = {
            "supplier_id": TEST_SUPPLIER_ID,
            "brand_id": TEST_BRAND_ID
        }
        response = requests.post(f"{API_BASE}/supplier_brand/", json=payload)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")

def test_supplier_products_endpoint():
    """Test the supplier products filtering endpoint"""
    print("\n🧪 Testing Supplier Products Endpoint...")
    
    print(f"\n3. Testing GET /supplier_products/?supplier_id={TEST_SUPPLIER_ID}")
    try:
        response = requests.get(f"{API_BASE}/supplier_products/?supplier_id={TEST_SUPPLIER_ID}")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
            products = data.get('data', {}).get('products', [])
            brands = data.get('data', {}).get('brands', [])
            print(f"   Products count: {len(products)}")
            print(f"   Brands count: {len(brands)}")
            if products:
                print(f"   Sample product: {products[0].get('name', 'N/A')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")

def test_purchase_order_endpoint():
    """Test the purchase order endpoint to ensure it still works"""
    print("\n🧪 Testing Purchase Order Endpoint...")
    
    print("\n4. Testing GET /purchase_order/")
    try:
        response = requests.get(f"{API_BASE}/purchase_order/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
            # Check if brands are included in the response
            if 'brands' in data.get('data', {}):
                print("   ✅ Brands data included in PO endpoint")
            else:
                print("   ⚠️  Brands data not found in PO endpoint")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")

def check_server_status():
    """Check if the Django server is running"""
    print("🔍 Checking server status...")
    try:
        response = requests.get(BASE_URL, timeout=5)
        print(f"   Server is running (Status: {response.status_code})")
        return True
    except requests.exceptions.ConnectionError:
        print("   ❌ Server is not running")
        print("   Please start the Django server with: python manage.py runserver 8000")
        return False
    except Exception as e:
        print(f"   Error checking server: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Brand-Based Product Filtering - Backend API Tests")
    print("=" * 60)
    
    # Check if server is running
    if not check_server_status():
        sys.exit(1)
    
    # Run tests
    test_supplier_brand_endpoints()
    test_supplier_products_endpoint()
    test_purchase_order_endpoint()
    
    print("\n" + "=" * 60)
    print("✅ Test execution completed!")
    print("\nNote: Some tests may fail if:")
    print("- Authentication is required")
    print("- Test data doesn't exist")
    print("- Database is empty")
    print("\nFor full testing, use Django's test framework or authenticate properly.")

if __name__ == "__main__":
    main()
