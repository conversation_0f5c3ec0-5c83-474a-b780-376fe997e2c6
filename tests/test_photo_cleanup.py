#!/usr/bin/env python
"""
Test script for the photo cleanup celery task.
"""
import os
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from master.tasks import cleanup_old_shop_photos
from master.models import ShopFreezerPhoto, Buyer, Route, User
from django.utils import timezone

def create_test_photos():
    """Create some test photos with different dates for testing."""
    print("🧪 Creating test photos...")
    
    try:
        # Get or create a test user, buyer, and route
        user, _ = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        route, _ = Route.objects.get_or_create(
            name='Test Route',
            defaults={'user': user}
        )
        
        buyer, _ = Buyer.objects.get_or_create(
            name='Test Buyer',
            defaults={'user': user, 'route': route}
        )
        
        # Create photos with different dates
        today = timezone.now().date()
        
        # Recent photo (should NOT be deleted)
        recent_photo = ShopFreezerPhoto.objects.create(
            buyer=buyer,
            route=route,
            photo='test_photos/recent_photo.jpg',
            notes='Recent photo - should not be deleted',
            uploaded_by=user
        )
        recent_photo.date_taken = today - timedelta(days=5)
        recent_photo.save()
        
        # Old photo (should be deleted)
        old_photo = ShopFreezerPhoto.objects.create(
            buyer=buyer,
            route=route,
            photo='test_photos/old_photo.jpg',
            notes='Old photo - should be deleted',
            uploaded_by=user
        )
        old_photo.date_taken = today - timedelta(days=35)
        old_photo.save()
        
        # Very old photo (should be deleted)
        very_old_photo = ShopFreezerPhoto.objects.create(
            buyer=buyer,
            route=route,
            photo='test_photos/very_old_photo.jpg',
            notes='Very old photo - should be deleted',
            uploaded_by=user
        )
        very_old_photo.date_taken = today - timedelta(days=60)
        very_old_photo.save()
        
        print(f"✅ Created 3 test photos:")
        print(f"   Recent photo (5 days old): {recent_photo.id}")
        print(f"   Old photo (35 days old): {old_photo.id}")
        print(f"   Very old photo (60 days old): {very_old_photo.id}")
        
        return [recent_photo.id, old_photo.id, very_old_photo.id]
        
    except Exception as e:
        print(f"❌ Error creating test photos: {str(e)}")
        return []

def check_photos_before_cleanup():
    """Check photo count before cleanup."""
    total_photos = ShopFreezerPhoto.objects.count()
    cutoff_date = timezone.now().date() - timedelta(days=30)
    old_photos = ShopFreezerPhoto.objects.filter(date_taken__lt=cutoff_date).count()
    
    print(f"📊 Photos before cleanup:")
    print(f"   Total photos: {total_photos}")
    print(f"   Photos older than 30 days: {old_photos}")
    
    return total_photos, old_photos

def check_photos_after_cleanup():
    """Check photo count after cleanup."""
    total_photos = ShopFreezerPhoto.objects.count()
    cutoff_date = timezone.now().date() - timedelta(days=30)
    old_photos = ShopFreezerPhoto.objects.filter(date_taken__lt=cutoff_date).count()
    
    print(f"📊 Photos after cleanup:")
    print(f"   Total photos: {total_photos}")
    print(f"   Photos older than 30 days: {old_photos}")
    
    return total_photos, old_photos

def test_cleanup_task():
    """Test the cleanup task."""
    print("🧪 Testing photo cleanup task...")
    
    try:
        # Run the cleanup task
        result = cleanup_old_shop_photos.delay()
        print(f"✅ Cleanup task submitted successfully. Task ID: {result.id}")
        
        # Wait for result with timeout
        task_result = result.get(timeout=30)
        print(f"📊 Task completed with result: {task_result}")
        
        return task_result
        
    except Exception as e:
        print(f"❌ Cleanup task failed: {str(e)}")
        return None

def cleanup_test_data():
    """Clean up test data."""
    print("🧹 Cleaning up test data...")
    
    try:
        # Delete test photos
        ShopFreezerPhoto.objects.filter(
            notes__icontains='test'
        ).delete()
        
        # Delete test buyer, route, and user
        Buyer.objects.filter(name='Test Buyer').delete()
        Route.objects.filter(name='Test Route').delete()
        User.objects.filter(username='test_user').delete()
        
        print("✅ Test data cleaned up")
        
    except Exception as e:
        print(f"❌ Error cleaning up test data: {str(e)}")

def main():
    """Run the photo cleanup test."""
    print("🚀 Starting Photo Cleanup Task Test")
    print("=" * 50)
    
    # Create test photos
    test_photo_ids = create_test_photos()
    
    if not test_photo_ids:
        print("❌ Failed to create test photos. Exiting.")
        return
    
    # Check photos before cleanup
    print("\n" + "=" * 30)
    total_before, old_before = check_photos_before_cleanup()
    
    # Run cleanup task
    print("\n" + "=" * 30)
    result = test_cleanup_task()
    
    # Check photos after cleanup
    print("\n" + "=" * 30)
    total_after, old_after = check_photos_after_cleanup()
    
    # Verify results
    print("\n" + "=" * 30)
    print("🔍 Verification:")
    
    if result:
        expected_deleted = old_before
        actual_deleted = result.get('photos_deleted', 0)
        
        print(f"   Expected photos to be deleted: {expected_deleted}")
        print(f"   Actually deleted: {actual_deleted}")
        print(f"   Storage freed: {result.get('storage_freed_mb', 0)} MB")
        
        if actual_deleted == expected_deleted and old_after == 0:
            print("✅ Cleanup task worked correctly!")
        else:
            print("❌ Cleanup task did not work as expected")
    else:
        print("❌ No result from cleanup task")
    
    # Clean up test data
    print("\n" + "=" * 30)
    cleanup_test_data()
    
    print("\n" + "=" * 50)
    print("🏁 Photo cleanup testing completed!")

if __name__ == "__main__":
    main()
