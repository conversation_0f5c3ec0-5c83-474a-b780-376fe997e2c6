#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append('/Volumes/Abinesh/Documents/Abinesh/king-bill/backend')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from master.models import BuyerBrand, Buyer, Brand, User
from master.serializer import BuyerBrandSerializer
from rest_framework.test import APIRequestFactory
from master.views import BuyerBrandView

def test_buyer_brand_api():
    # Get a user
    user = User.objects.first()
    if not user:
        print("No users found in database")
        return
    
    print(f"Using user: {user.username} (ID: {user.id})")
    print(f"User company: {user.company}")
    
    # Get a buyer that has brands
    buyer_brand = BuyerBrand.objects.first()
    if not buyer_brand:
        print("No buyer-brand relationships found")
        return
    
    buyer_id = buyer_brand.buyer.id
    print(f"Testing with buyer ID: {buyer_id} ({buyer_brand.buyer.name})")
    print(f"Buyer user: {buyer_brand.buyer.user}")
    
    # Check if the buyer belongs to the user's company
    if buyer_brand.user != user.company and buyer_brand.user != user:
        print(f"Warning: Buyer brand belongs to user {buyer_brand.user}, but we're testing with user {user}")
    
    # Create a request
    factory = APIRequestFactory()
    request = factory.get(f'/api/buyer_brand/?buyer_id={buyer_id}')
    request.user = user
    
    # Call the view
    view = BuyerBrandView.as_view()
    response = view(request)
    
    print(f"Response status: {response.status_code}")
    print(f"Response data: {response.data}")
    
    if response.status_code == 200:
        print("✅ API is working correctly!")
        data = response.data
        if data.get('success'):
            buyer_brands = data.get('data', [])
            print(f"Found {len(buyer_brands)} buyer-brand relationships")
            for bb in buyer_brands:
                print(f"  - Buyer: {bb.get('buyer_name')}, Brand: {bb.get('brand_name')}")
        else:
            print("❌ API returned success=False")
    else:
        print("❌ API returned error status")

if __name__ == "__main__":
    test_buyer_brand_api() 