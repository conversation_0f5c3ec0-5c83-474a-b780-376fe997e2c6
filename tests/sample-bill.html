<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${localStorage.getItem("company_name")}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
    <style>
        @media print {
            body {
                width: 70mm;
                /* height: 3276mm;*/
                margin: 0.2mm 0.2mm 0.2mm 0.2mm;
                /* change the margins as you want them to be. */
            }
        }

        #options {
            align-content: center;
            align-items: center;
            text-align: center;
        }
    </style>
</head>

<body>
    <div style="text-align: center;word-wrap: break-word;">
        <h4 style="padding:2px;margin: 3px;">${localStorage.getItem("company_name")}</h4>
        <p style="margin: 3px;">
            ${localStorage.getItem("address")}
        </p>
        <p style="margin: 3px;">${localStorage.getItem('contact_no_left')},${localStorage.getItem('contact_no_right')}
        </p>
        <p style="margin: 3px;">*******************************</p>
    </div>
    <div>
        <p style="margin: 3px;"><b style="float:left;">Bill No:-${data.id}</b> <b
                style="float:right;">Date:-${data.date}</b></p>
        <p style="margin: 3px;text-align: center;font-weight:bold;">Customer Details</p>
        <p style="margin: 3px;">Name:${data.name}</p>
        <p style="margin: 3px;">Number:${data.phone_no}</p>
        <p style="margin: 3px;">Place:${data.place}</p>
        <p style="margin: 3px;text-align: center;">*******************************</p>
    </div>
    <table>
        <tr>
            <th style="width:60%">Item</th>
            <th style="width:10%">Qty</th>
            <th style="width:10%">Pcs</th>
            <th style="width:20%">Amount</th>
        </tr>
        ${items}
        <tr>
            <th style="width:60%">Total</th>
            <th style="width:10%">${totalNo}</th>
            <th style="width:10%">${totalWeight} </th>
            <th style="width:20%">${totalAmount}</th>
        </tr>
    </table>

    <table>
        <tr>
            <th style="width:60%">Previous Balance</th>
            <td style="width:10%">${data.previous_balance} </td>
        </tr>
        <tr>
            <th style="width:60%">Bill Amount </th>
            <td style="width:10%">${data.bill_amount}</td>
        </tr>
        <tr>
            <th style="width:60%">Recieved Amount</th>
            <td style="width:10%">${data.received_amount}</td>
        </tr>
        <tr>
            <th style="width:60%">Closing Balance </th>
            <td style="width:10%">${data.current_balance}</td>
        </tr>
    </table>
    <p style="margin: 3px;text-align: center;">*******************************</p>
    <p style="margin: 3px;text-align: center;">Thank You</p>

</body>

</html>