#!/usr/bin/env python
"""
Test script for Celery tasks in the vegetable bill application.
"""
import os
import sys
import django
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vegetable_bill_app.settings')
django.setup()

from celery import current_app
from master.tasks import (
    daily_inventory_check,
    generate_sales_forecast,
    process_ai_forecasts,
    generate_purchase_order
)
from master.models import Product, User

def test_debug_task():
    """Test the simple debug task."""
    print("🧪 Testing debug task...")
    try:
        # Get the debug task from celery app
        debug_task = current_app.tasks.get('vegetable_bill_app.celery.debug_task')
        if debug_task:
            result = debug_task.delay()
            print(f"✅ Debug task submitted successfully. Task ID: {result.id}")
            print(f"📊 Task result: {result.get(timeout=10)}")
        else:
            print("❌ Debug task not found")
    except Exception as e:
        print(f"❌ Debug task failed: {str(e)}")

def test_sales_forecast():
    """Test the sales forecast generation task."""
    print("\n🧪 Testing sales forecast generation...")
    try:
        result = generate_sales_forecast.delay()
        print(f"✅ Sales forecast task submitted successfully. Task ID: {result.id}")
        
        # Wait for result with timeout
        task_result = result.get(timeout=30)
        print(f"📊 Task completed with result: {task_result}")
        
    except Exception as e:
        print(f"❌ Sales forecast task failed: {str(e)}")

def test_inventory_check():
    """Test the daily inventory check task."""
    print("\n🧪 Testing daily inventory check...")
    try:
        result = daily_inventory_check.delay()
        print(f"✅ Inventory check task submitted successfully. Task ID: {result.id}")
        
        # Wait for result with timeout
        task_result = result.get(timeout=30)
        print(f"📊 Task completed with result: {task_result}")
        
    except Exception as e:
        print(f"❌ Inventory check task failed: {str(e)}")

def test_ai_forecasts():
    """Test the AI forecast processing task."""
    print("\n🧪 Testing AI forecast processing...")
    try:
        result = process_ai_forecasts.delay()
        print(f"✅ AI forecast task submitted successfully. Task ID: {result.id}")
        
        # Wait for result with timeout
        task_result = result.get(timeout=30)
        print(f"📊 Task completed with result: {task_result}")
        
    except Exception as e:
        print(f"❌ AI forecast task failed: {str(e)}")

def test_purchase_order_generation():
    """Test purchase order generation for specific products."""
    print("\n🧪 Testing purchase order generation...")
    try:
        # Get a test user and some products
        users = User.objects.all()[:1]
        if not users:
            print("❌ No users found in database. Cannot test purchase order generation.")
            return
            
        user = users[0]
        products = Product.objects.filter(user=user, active=True)[:3]
        
        if not products:
            print(f"❌ No active products found for user {user.username}")
            return
            
        product_ids = [p.id for p in products]
        print(f"📦 Testing with user: {user.username}, products: {[p.name for p in products]}")
        
        result = generate_purchase_order.delay(user.id, product_ids)
        print(f"✅ Purchase order task submitted successfully. Task ID: {result.id}")
        
        # Wait for result with timeout
        task_result = result.get(timeout=30)
        print(f"📊 Task completed with result: {task_result}")
        
    except Exception as e:
        print(f"❌ Purchase order generation failed: {str(e)}")

def check_celery_status():
    """Check Celery worker status."""
    print("🔍 Checking Celery status...")
    try:
        # Check if workers are active
        inspect = current_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print(f"✅ Active workers found: {list(active_workers.keys())}")
            for worker, tasks in active_workers.items():
                print(f"   Worker {worker}: {len(tasks)} active tasks")
        else:
            print("❌ No active workers found")
            
        # Check registered tasks
        registered = inspect.registered()
        if registered:
            print(f"📋 Registered tasks on workers:")
            for worker, tasks in registered.items():
                print(f"   Worker {worker}: {len(tasks)} registered tasks")
                for task in sorted(tasks)[:5]:  # Show first 5 tasks
                    print(f"      - {task}")
                if len(tasks) > 5:
                    print(f"      ... and {len(tasks) - 5} more")
        
    except Exception as e:
        print(f"❌ Failed to check Celery status: {str(e)}")

def main():
    """Run all Celery tests."""
    print("🚀 Starting Celery Task Testing")
    print("=" * 50)
    
    # Check Celery status first
    check_celery_status()
    
    # Test debug task
    test_debug_task()
    
    # Test business logic tasks
    test_sales_forecast()
    test_inventory_check()
    test_ai_forecasts()
    test_purchase_order_generation()
    
    print("\n" + "=" * 50)
    print("🏁 Celery testing completed!")

if __name__ == "__main__":
    main()
