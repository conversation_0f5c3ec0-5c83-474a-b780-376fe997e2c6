[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
    - [ ] NAME: Checklist Module DESCRIPTION:Add ability to create checklist with the common industry details,some mandatory fields i would like to have is image,task start and submit time show summary of completions in the checklist and ask to complete it but poping up as an alert or toast
    - [ ] NAME: Sales Bill Page Improvements DESCRIPTION:first show summary of shops that are not billed on the day expected output if a route mapped with monday  then it should show summary of monday  list of shops to be bill on monday also need to upload freezer photo for every shop in the route need an interface for that as well
    - [ ] NAME: Collection reciept DESCRIPTION:collection receipt generating should be in thermal print always