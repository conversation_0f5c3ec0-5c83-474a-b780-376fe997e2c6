# Backend Documentation

This directory contains all backend-related documentation for the King Bill application.

## 📋 Available Documentation

- **[Main Backend README](README.md)** - Complete backend setup, API documentation, and development guide

## 🏗️ Backend Overview

The King Bill backend is built with Django and Django REST Framework, providing:

- **RESTful API** - Complete API for all application features
- **Database Models** - Comprehensive data models for business logic
- **Authentication** - JWT-based authentication system
- **Translation** - Multi-language support with Django's translation system
- **File Handling** - Media file management and processing
- **Admin Interface** - Django admin for data management

## 🔗 Related Documentation

- **[API Schema](../api/schema.yaml)** - Complete API documentation
- **[Translation Documentation](../translation/)** - Language and localization features
- **[Development Setup](../development/)** - Development environment configuration

## 🚀 Quick Links

- [Backend Setup Guide](README.md#setup)
- [API Endpoints](README.md#api-endpoints)
- [Database Models](README.md#models)
- [Authentication](README.md#authentication)
- [Translation System](README.md#translation)