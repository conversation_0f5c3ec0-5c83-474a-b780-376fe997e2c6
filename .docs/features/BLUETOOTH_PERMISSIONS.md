# Bluetooth Permissions Setup

## Problem
The Bluetooth permissions in `AndroidManifest.xml` get removed every time we run `npx cap sync` because Capacitor regenerates the manifest file.

## Solution
We've implemented an automated solution that adds the required Bluetooth permissions after each sync operation.

## Files Created

### 1. `scripts/add-bluetooth-permissions.js`
This script automatically adds the required Bluetooth permissions to the AndroidManifest.xml file.

### 2. Updated `package.json` scripts
Added new npm scripts that automatically run the permission script after sync:

```json
{
  "scripts": {
    "cap:sync": "npx cap sync && node scripts/add-bluetooth-permissions.js",
    "cap:sync:android": "npx cap sync android && node scripts/add-bluetooth-permissions.js",
    "add-bluetooth-permissions": "node scripts/add-bluetooth-permissions.js"
  }
}
```

## Usage

### Instead of running:
```bash
npx cap sync android
```

### Run:
```bash
npm run cap:sync:android
```

### Or for all platforms:
```bash
npm run cap:sync
```

### To manually add permissions:
```bash
npm run add-bluetooth-permissions
```

## Permissions Added
The script adds these Bluetooth permissions to AndroidManifest.xml:

```xml
<!-- Bluetooth permissions for Cordova Bluetooth Serial plugin -->
<uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
```

## How It Works
1. The script checks if the permissions already exist
2. If they don't exist, it adds them before the closing `</manifest>` tag
3. The script is safe to run multiple times (won't duplicate permissions)

## Benefits
- ✅ Permissions persist through Capacitor sync operations
- ✅ Automated process - no manual intervention needed
- ✅ Safe to run multiple times
- ✅ Clear logging of what's happening

## AndroidX Compatibility Fix

### Problem
The original `cordova-plugin-printer` was incompatible with AndroidX, causing build errors like:
```
error: package android.support.annotation does not exist
```

### Solution
1. **Removed Cordova Printer Plugin**: Replaced with browser-based printing for non-thermal printing
2. **Added Java Version Configuration**: Set Java 17 compatibility in build.gradle files
3. **Updated Print Service**: Modified to use `printHtmlDocument()` for all non-thermal printing

### Files Modified for AndroidX Compatibility

#### `android/app/build.gradle`
```gradle
compileOptions {
    sourceCompatibility JavaVersion.VERSION_17
    targetCompatibility JavaVersion.VERSION_17
}
```

#### `android/build.gradle`
```gradle
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
            }
        }
    }
}
```

## Important Notes
- Always use `npm run cap:sync:android` instead of `npx cap sync android`
- The script will automatically detect if permissions are already present
- If you forget to use the npm script, you can manually run `npm run add-bluetooth-permissions`
- Thermal printing works via Bluetooth (unchanged)
- Regular printing (A4/A5) now uses browser print dialog instead of Cordova plugin
