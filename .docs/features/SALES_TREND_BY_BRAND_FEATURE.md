# Sales Trend Analytics by Brand Feature

## Overview

This feature adds comprehensive sales trend analytics grouped by brand to the dashboard, providing insights into brand performance over time with interactive visualizations and detailed metrics.

## Features Implemented

### 1. Backend Implementation

#### New API Endpoint
- **URL**: `/sales-trend-by-brand/` or `/dashboard/?sales_trend_by_brand=true`
- **Method**: GET
- **Authentication**: Token-based authentication required

#### Query Parameters
- `sales_trend_by_brand`: Set to 'true' to get brand analytics
- `from_date`: Start date (YYYY-MM-DD format, optional - defaults to 6 months ago)
- `to_date`: End date (YYYY-MM-DD format, optional - defaults to today)
- `time_period`: Grouping period ('monthly', 'quarterly', 'yearly' - defaults to 'monthly')
- `brand_ids`: Array of brand IDs to filter (optional - includes all brands if not specified)

#### Response Structure
```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": {
    "time_series_data": [
      {
        "period": "2024-01-01",
        "product__brand__id": 1,
        "product__brand__name": "Brand A",
        "total_sales_amount": 15000.0,
        "total_quantity": 150,
        "total_pieces": 1500,
        "transaction_count": 25
      }
    ],
    "brand_summary": [
      {
        "product__brand__id": 1,
        "product__brand__name": "Brand A",
        "total_sales_amount": 75000.0,
        "total_quantity": 750,
        "total_pieces": 7500,
        "transaction_count": 125
      }
    ],
    "brand_growth": {
      "1": 15.5,
      "2": -5.2
    },
    "date_range": {
      "from_date": "2024-01-01",
      "to_date": "2024-06-30",
      "time_period": "monthly"
    },
    "total_brands": 5,
    "generated_at": "2024-06-30T10:30:00Z"
  }
}
```

#### Database Queries
- Joins `SalesInvoiceItem` with `Product` and `Brand` models
- Filters by user, date range, and invoice status ('billed')
- Groups data by time period and brand
- Calculates aggregated metrics (sum, count)
- Computes growth percentages between periods

### 2. Frontend Implementation

#### Dashboard Integration
- Added new analytics section "Sales Trend by Brand"
- Interactive line chart showing sales trends over time
- Brand performance summary with rankings
- Time period selector (Monthly/Quarterly/Yearly)

#### Chart Features
- **Interactive Line Chart**: Shows sales trends for each brand over time
- **Multiple Brand Lines**: Different colors for each brand
- **Hover Tooltips**: Display exact values and brand information
- **Responsive Design**: Adapts to different screen sizes
- **Indian Currency Formatting**: Displays amounts in INR format

#### Brand Performance Section
- **Top 5 Brands**: Ranked by total sales amount
- **Growth Indicators**: Shows positive/negative growth with icons
- **Transaction Metrics**: Displays transaction count and units sold
- **Real-time Data**: Updates when time period changes

### 3. User Interface Components

#### Chart Controls
```html
<ion-segment [(ngModel)]="selectedTimePeriod" (ionChange)="onTimePeriodChange()">
  <ion-segment-button value="monthly">Monthly</ion-segment-button>
  <ion-segment-button value="quarterly">Quarterly</ion-segment-button>
  <ion-segment-button value="yearly">Yearly</ion-segment-button>
</ion-segment>
```

#### Brand Summary Display
- Ranking numbers (1-5)
- Brand names and performance metrics
- Growth percentage with color coding
- Transaction count and unit sales

## Technical Implementation Details

### Backend Changes

#### 1. Views (`backend/master/views.py`)
- Extended `DashboardView` class with `get_sales_trend_by_brand()` method
- Added query parameter handling for filtering and time period selection
- Implemented growth percentage calculations
- Added proper error handling and response formatting

#### 2. URL Configuration (`backend/master/urls.py`)
- Added new URL pattern for sales trend endpoint
- Maintains backward compatibility with existing dashboard endpoint

#### 3. Serializers (`backend/master/serializer.py`)
- Added `SalesTrendByBrandSerializer` for time series data
- Added `BrandSummarySerializer` for brand performance data
- Proper field mapping and data validation

### Frontend Changes

#### 1. Service Layer (`frontend/src/app/shared/services/dashboard.service.ts`)
- Added TypeScript interfaces for type safety
- Implemented `getSalesTrendByBrand()` method
- Proper error handling and data transformation

#### 2. Component Logic (`frontend/src/app/menu/menu.page.ts`)
- Added Chart.js integration for line charts
- Implemented time period change handling
- Added brand growth calculation methods
- Integrated with existing dashboard loading flow

#### 3. Template Updates (`frontend/src/app/menu/menu.page.html`)
- Added sales trend chart section
- Added brand performance summary section
- Implemented responsive grid layout
- Added interactive controls

#### 4. Styling (`frontend/src/app/menu/menu.page.scss`)
- Added chart control styling
- Added brand performance section styles
- Implemented growth indicator styling
- Maintained consistent design language

## Usage Instructions

### For Developers

1. **Backend Setup**: The feature is automatically available after the code changes
2. **Frontend Setup**: Ensure Chart.js is loaded (already integrated)
3. **Testing**: Use the provided test script to verify functionality

### For Users

1. **Access**: Navigate to the Dashboard page
2. **View Charts**: Scroll to the "Analytics & Insights" section
3. **Change Time Period**: Use the segment buttons (Monthly/Quarterly/Yearly)
4. **View Brand Performance**: Check the "Brand Performance" section below
5. **Interpret Data**: 
   - Green growth indicators show positive growth
   - Red indicators show negative growth
   - Rankings are based on total sales amount

## Data Requirements

### Minimum Data Needed
- At least one Brand with associated Products
- Sales invoices with 'billed' status
- Sales invoice items linked to products with brands
- Date range covering at least 2 time periods for growth calculation

### Performance Considerations
- Queries are optimized with proper joins and indexing
- Data is filtered by user to ensure security
- Pagination can be added for large datasets if needed

## Future Enhancements

### Potential Improvements
1. **Brand Filtering**: Allow users to select specific brands to display
2. **Export Functionality**: Add CSV/PDF export for reports
3. **Drill-down Analysis**: Click on brands to see product-level details
4. **Comparison Tools**: Compare multiple time periods side by side
5. **Forecasting**: Add predictive analytics for future trends
6. **Mobile Optimization**: Enhanced mobile chart interactions

### Additional Metrics
1. **Profit Margins**: Include profit analysis by brand
2. **Customer Segmentation**: Show which customers buy which brands
3. **Seasonal Analysis**: Identify seasonal patterns in brand performance
4. **Market Share**: Calculate brand market share percentages

## Testing

### Manual Testing Steps
1. Create test brands and products
2. Generate sales invoices with items from different brands
3. Access the dashboard and verify chart displays
4. Test time period changes
5. Verify growth calculations
6. Check responsive design on different screen sizes

### Automated Testing
- Use the provided `test_sales_trend_api.py` script
- Verify API responses and data structure
- Test edge cases (no data, single brand, etc.)

## Troubleshooting

### Common Issues
1. **No Data Displayed**: Ensure products have brands assigned
2. **Chart Not Loading**: Check Chart.js library loading
3. **Growth Calculation Errors**: Verify sufficient historical data
4. **Performance Issues**: Consider date range limitations

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify API responses in network tab
3. Ensure proper authentication tokens
4. Check database for required data relationships
