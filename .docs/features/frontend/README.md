# Frontend Documentation

This directory contains all frontend-related documentation for the King Bill application.

## 📋 Available Documentation

- **[Main Frontend README](README.md)** - Frontend setup, development, and deployment guide

## 🎨 Frontend Overview

The King Bill frontend is built with Ionic Angular, providing:

- **Cross-Platform App** - Works on web, iOS, and Android
- **Modern UI/UX** - Material Design with custom theming
- **Responsive Design** - Mobile-first approach
- **Translation Support** - Multi-language interface
- **Printing Integration** - Cross-platform printing capabilities
- **Bluetooth Support** - Mobile device integration

## 🔗 Related Documentation

- **[Printing Documentation](../printing/)** - Cross-platform printing guides
- **[Development Setup](../development/)** - Development environment configuration
- **[Translation Documentation](../translation/)** - Language and localization features

## 🚀 Quick Links

- [Frontend Setup Guide](README.md#setup)
- [Ionic Configuration](README.md#ionic-config)
- [Angular Components](README.md#components)
- [Mobile Deployment](README.md#mobile)
- [Printing Setup](README.md#printing)

Run The following command
ionic build
npx cap sync
npx jetify
npx cap sync 
npx cap open android 
:)))

