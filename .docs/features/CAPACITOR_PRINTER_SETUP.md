# Capacitor Printer Plugin Setup

## Overview

The KingBill app has been successfully migrated from the Cordova printer plugin to the Capacitor printer plugin `@bcyesil/capacitor-plugin-printer`. This provides better compatibility with Capacitor 6.x and improved printing functionality.

## Plugin Information

- **Package**: `@bcyesil/capacitor-plugin-printer`
- **Version**: 0.0.5
- **Compatibility**: Capacitor 6.x
- **Platforms**: Android 5.1+, iOS 13+

## Features

- ✅ HTML content printing
- ✅ Plain text printing
- ✅ Image printing (as HTML)
- ✅ Base64 content printing (PDF & images)
- ✅ Automatic fallback to browser printing
- ✅ Portrait/landscape orientation support

## Installation

The plugin has already been installed and configured:

```bash
npm install @bcyesil/capacitor-plugin-printer --legacy-peer-deps
npx cap sync
```

## Usage

### Basic Printing

The print service automatically uses the Capacitor printer plugin when available:

```typescript
// Print an invoice
await this.printService.print(invoiceData, false, 'Invoice-123');

// Print collection receipt
await this.printService.printCollectionReceipt(invoiceData);
```

### Direct Plugin Usage

You can also use the plugin directly:

```typescript
import { Printer } from '@bcyesil/capacitor-plugin-printer';

await Printer.print({
  content: '<h1>Hello World</h1>',
  name: 'Test Document',
  orientation: 'portrait'
});
```

### Testing

A test method has been added to the print service:

```typescript
// Test the printer plugin (can be called from browser console)
await this.printService.testCapacitorPrinter();
```

## Print Flow

1. **Thermal Printing**: Uses Bluetooth service for thermal printers (unchanged)
2. **Regular Printing on Cordova**: Uses Capacitor printer plugin
3. **Web/Browser**: Falls back to browser printing

## Error Handling

The implementation includes automatic fallback:
- If Capacitor printer fails → Falls back to browser printing
- Errors are logged to console for debugging

## Supported Content Types

### HTML Content
```typescript
const htmlContent = `
  <html>
    <body>
      <h1>Invoice</h1>
      <p>Content here...</p>
    </body>
  </html>
`;
await Printer.print({ content: htmlContent });
```

### Base64 Content
```typescript
// PDF or image as base64
await Printer.print({ content: 'base64:JVBERi0xLjQK...' });
```

### Images
```typescript
// Image as HTML
await Printer.print({ 
  content: '<img src="data:image/png;base64,...">' 
});
```

## Configuration Options

```typescript
interface PrintOptions {
  content: string;           // HTML content, text, or base64
  name?: string;            // Document name (default: 'Document')
  orientation?: string;     // 'portrait' or 'landscape' (default: 'portrait')
}
```

## Migration Notes

### Changes Made

1. **Removed**: `@awesome-cordova-plugins/printer`
2. **Added**: `@bcyesil/capacitor-plugin-printer`
3. **Updated**: Print service methods to use async/await
4. **Enhanced**: Error handling with fallback support

### Backward Compatibility

- All existing print methods continue to work
- No changes required in components using the print service
- Thermal printing via Bluetooth remains unchanged

## Troubleshooting

### Common Issues

1. **Plugin not found**: Ensure `npx cap sync` was run after installation
2. **Print dialog not showing**: Check device permissions and printer availability
3. **Fallback to browser**: Normal behavior when native printing fails

### Debug Commands

```bash
# Check plugin installation
npx cap ls

# Rebuild and sync
ionic build
npx cap sync

# Test on device
ionic cap run android -l --external
```

### Console Testing

Open browser console and test:

```javascript
// Get print service instance
const printService = document.querySelector('app-root')?._ngElementStrategy?.componentRef?.injector?.get('PrintServiceService');

// Test printer
await printService.testCapacitorPrinter();
```

## Platform-Specific Notes

### Android
- Requires Android 5.1+
- Print dialog will show available printers
- Supports PDF generation and sharing

### iOS
- Requires iOS 13+
- Uses AirPrint when available
- Supports wireless printer discovery

### Web
- Falls back to browser printing
- Uses window.print() API
- Limited to browser capabilities

## Future Enhancements

Potential improvements:
- Custom print templates
- Printer discovery and selection
- Print queue management
- Advanced formatting options

## Support

For issues related to the plugin itself, refer to:
- [Plugin Repository](https://github.com/BarisCanYesil/capacitor-plugin-printer)
- [NPM Package](https://www.npmjs.com/package/@bcyesil/capacitor-plugin-printer)
