# Printing & Output Documentation

This directory contains all printing and output-related documentation for the King Bill application.

## 📋 Available Documentation

- **[Cross-Platform Printing Guide](cross-platform-printing-guide.md)** - How to set up printing across different platforms
- **[Print Settings Fix Summary](print-settings-fix-summary.md)** - Solutions for common printing issues
- **[Capacitor Printer Setup](capacitor-printer-setup.md)** - Setting up printers with Capacitor

## 🖨️ Printing Overview

The King Bill application supports comprehensive printing functionality:

- **Cross-Platform Support** - Web, iOS, and Android printing
- **Bluetooth Printers** - Mobile device printer integration
- **Multiple Formats** - HTML, PDF, and direct printer output
- **Custom Templates** - Branded invoice and receipt templates
- **Print Settings** - Configurable print options and preferences

## 🔧 Implementation Details

### Web Platform
- Browser-based printing
- HTML to PDF conversion
- Print preview functionality
- Custom CSS for print styling

### Mobile Platforms (iOS/Android)
- Capacitor printer plugin
- Bluetooth printer support
- Native printing capabilities
- Print queue management

### Common Features
- Invoice and receipt printing
- Purchase order printing
- Report generation
- Custom template support

## 🚀 Quick Links

- [Web Setup](cross-platform-printing-guide.md#web-setup)
- [Mobile Setup](capacitor-printer-setup.md#mobile-setup)
- [Troubleshooting](print-settings-fix-summary.md#common-issues)
- [Bluetooth Setup](capacitor-printer-setup.md#bluetooth)

## 🔗 Related Documentation

- **[Frontend Documentation](../frontend/)** - Frontend printing implementation
- **[Development Setup](../development/)** - Development environment configuration
- **[Bluetooth Permissions](../development/bluetooth-permissions.md)** - Mobile permissions setup

## 🛠️ Development

- **Testing**: Use test scripts for print functionality
- **Templates**: Customizable print templates
- **Configuration**: Print settings management
- **Debugging**: Print debugging tools and logs 