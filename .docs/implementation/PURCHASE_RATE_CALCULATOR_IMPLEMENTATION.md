# Purchase Rate Calculator Implementation

## Overview
A comprehensive purchase rate calculator modal has been implemented for the product master forms that calculates the final purchase rate (Pr_rate) from total purchase amount using the required formula sequence.

## Formula Implementation

### Step 1: Tax Amount Calculation
**When GST is NOT included:**
```
Tax Amount = Total Purchase Amount × (Tax % / 100)
```

**When GST IS included:**
```
Tax Amount = Total Purchase Amount × (Tax % / (100 + Tax %))
```

### Step 2: Final Total Amount Calculation
**When GST is NOT included:**
```
Final Total Amount = Total Purchase Amount + Tax Amount
```

**When GST IS included:**
```
Final Total Amount = Total Purchase Amount (GST already included)
```

### Step 3: Purchase Rate (PR Rate) Calculation
```
PR Rate = Final Total Amount / (Order Quantity × Unit Contains × Crate Contains)
```

## Features Implemented

### 🎯 Core Functionality
- **Real-time calculations** with immediate updates as user types
- **Visual formula display** showing step-by-step calculation breakdown
- **Clear distinction** between input fields and calculated fields
- **Integration** with both Add Product and Edit Product forms
- **Field reuse** leveraging existing crate_size and tax_rate fields

### 🎨 User Interface
- **Modern modal design** following existing application patterns
- **Responsive layout** that works on mobile and desktop
- **Step-by-step visualization** of calculation process
- **Color-coded results** with clear visual hierarchy
- **Intuitive button placement** with calculator icon

### 📱 Form Integration
- **Calculator buttons** added to both Add and Edit product forms
- **Seamless data flow** between calculator and main forms
- **Automatic field population** from existing product data in edit mode
- **Validation** ensuring required fields are filled

## Technical Implementation

### Files Modified
1. **frontend/src/app/product/product.page.ts**
   - Added calculator modal state management
   - Implemented calculation logic methods
   - Added form integration methods

2. **frontend/src/app/product/product.page.html**
   - Added calculator buttons to PR Rate fields
   - Implemented comprehensive calculator modal
   - Added step-by-step calculation display

3. **frontend/src/app/product/product.page.scss**
   - Added calculator modal styling
   - Implemented responsive design
   - Added visual distinction for calculated fields

### Key Methods Added
- `openPurchaseCalculator(mode)` - Opens calculator in add/edit mode
- `closePurchaseCalculator()` - Closes calculator modal
- `calculatePurchaseRate()` - Performs all calculations
- `onCalculatorFieldChange()` - Handles real-time updates
- `applyCalculatedRate()` - Applies result to main form

### Input Fields
- **Purchase Rate** (required) - Base purchase rate
- **Adjustment** (optional) - Amount to subtract from purchase rate
- **Quantity** (required) - Quantity being purchased
- **Units per Crate** (required) - Reuses existing crate_size field
- **Discount** (optional) - Discount amount to subtract
- **Tax %** (optional) - Tax percentage to apply

### Calculated Fields (Read-only)
- **Basic Purchase** - Result of step 1 calculation
- **Tax Amount** - Result of step 2 calculation
- **Net Purchase Cost** - Final PR Rate result

## Backend Integration

### Existing Fields Utilized
- `pr_rate` - Stores the final calculated purchase rate
- `crate_size` - Used as "Units per Crate" in calculator
- `tax_rate` - Used as "Tax %" in calculator
- `is_crate_based` - Boolean flag for crate-based products

### No New Backend Fields Required
The calculator uses temporary calculation fields that don't need database storage. Only the final `pr_rate` result is saved to the database.

## Usage Instructions

### For Add Product Form
1. Fill in basic product information
2. Click "Calculate PR Rate" button next to PR Rate field
3. Enter calculation inputs in the modal
4. Review step-by-step calculation breakdown
5. Click "Apply Rate" to populate the PR Rate field
6. Continue with product creation

### For Edit Product Form
1. Open existing product for editing
2. Click "Calculate PR Rate" button next to PR Rate field
3. Calculator pre-populates with existing crate_size and tax_rate
4. Modify calculation inputs as needed
5. Review updated calculations
6. Click "Apply Rate" to update the PR Rate field
7. Save product changes

## Testing Recommendations

### Manual Testing Scenarios
1. **Basic Calculation Test**
   - Purchase Rate: 100, Quantity: 2, Units per Crate: 5
   - Expected Basic Purchase: 1000
   - Expected with 10% tax: 1100

2. **With Adjustments Test**
   - Purchase Rate: 100, Adjustment: 5, Quantity: 2, Units per Crate: 5
   - Expected Basic Purchase: 950
   - Expected with 10% tax: 1045

3. **With Discount Test**
   - Purchase Rate: 100, Quantity: 2, Units per Crate: 5, Discount: 50
   - Expected Basic Purchase: 950
   - Expected with 10% tax: 1045

4. **Complex Calculation Test**
   - Purchase Rate: 150, Adjustment: 10, Quantity: 3, Units per Crate: 4, Discount: 25, Tax: 12%
   - Expected Basic Purchase: (150-10) × (3×4) - 25 = 1655
   - Expected Tax Amount: 1655 × 0.12 = 198.6
   - Expected Final Rate: 1853.6

### Form Integration Testing
1. Test calculator button visibility in both forms
2. Verify data pre-population in edit mode
3. Confirm calculated rate applies correctly to main form
4. Test form validation with calculated values
5. Verify backend save functionality

## Browser Compatibility
- ✅ Chrome/Chromium browsers
- ✅ Safari (iOS/macOS)
- ✅ Firefox
- ✅ Edge
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Considerations
- Real-time calculations are lightweight and performant
- No external API calls required for calculations
- Minimal memory footprint with efficient state management
- Responsive design optimized for mobile devices

## Future Enhancements
- Add calculation history/presets
- Implement bulk calculation for multiple products
- Add export functionality for calculation results
- Integration with purchase order workflows
