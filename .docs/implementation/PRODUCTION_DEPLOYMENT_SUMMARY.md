# Production Deployment Summary - Supervisor + Gunicorn + Celery

## Overview

This document provides a quick reference for the complete production deployment setup of King Bill application on AWS Lightsail with Ubuntu OS and Bitnami image, using <PERSON><PERSON> for process management, <PERSON><PERSON> as WSGI server, and Celery for background tasks.

## Architecture Stack

```
┌─────────────────────────────────────────────────────────────┐
│                    Production Architecture                   │
├─────────────────────────────────────────────────────────────┤
│  Internet → Nginx → Gunicorn → Django Application          │
│                                                             │
│  Supervisor manages:                                        │
│  ├── Django App (Gunicorn)                                │
│  ├── Celery Worker                                        │
│  └── Celery Beat                                          │
│                                                             │
│  Supporting Services:                                       │
│  ├── PostgreSQL (Database)                                │
│  ├── Redis (Message Broker + Cache)                       │
│  └── Nginx (Reverse Proxy + Static Files)                 │
└─────────────────────────────────────────────────────────────┘
```

## Key Components

### 1. **Supervisor** - Process Management
- **Purpose**: Manages all application processes
- **Benefits**: Auto-restart, centralized logging, easy monitoring
- **Manages**: Django app, Celery worker, Celery beat

### 2. **Gunicorn** - WSGI HTTP Server
- **Purpose**: Serves Django application
- **Benefits**: Production-ready, multi-worker, configurable
- **Configuration**: `/opt/bitnami/apps/king-bill/backend/gunicorn.conf.py`

### 3. **Nginx** - Reverse Proxy
- **Purpose**: Handle HTTP requests, serve static files
- **Benefits**: High performance, SSL termination, load balancing
- **Configuration**: `/opt/bitnami/nginx/conf/server_blocks/king_bill.conf`

### 4. **Celery** - Background Tasks
- **Purpose**: Handle asynchronous tasks
- **Components**: Worker (task execution) + Beat (scheduling)
- **Queues**: inventory, forecast, ai, purchase, cleanup

## Quick Deployment Steps

### 1. System Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y redis-server supervisor python3-pip python3-venv postgresql-client

# Create application directory
sudo mkdir -p /opt/bitnami/apps/king-bill
sudo chown -R bitnami:bitnami /opt/bitnami/apps/king-bill
```

### 2. Application Setup
```bash
# Deploy code and setup virtual environment
cd /opt/bitnami/apps/king-bill
python3 -m venv venv
source venv/bin/activate
pip install -r backend/requirements.txt

# Configure environment and database
cp backend/.env.example backend/.env  # Edit with production values
cd backend
python manage.py migrate
python manage.py collectstatic --noinput
```

### 3. Service Configuration
```bash
# Create Gunicorn config
# See backend/README.md for full gunicorn.conf.py content

# Create Supervisor configs
sudo nano /etc/supervisor/conf.d/king_bill_django.conf
sudo nano /etc/supervisor/conf.d/king_bill_celery_worker.conf
sudo nano /etc/supervisor/conf.d/king_bill_celery_beat.conf

# Update and start services
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start all
```

### 4. Nginx Configuration
```bash
# Create site config
sudo nano /opt/bitnami/nginx/conf/server_blocks/king_bill.conf

# Test and restart
sudo nginx -t
sudo systemctl restart nginx
```

## Service Management Commands

### Supervisor Commands
```bash
# Check all services
sudo supervisorctl status

# Restart specific service
sudo supervisorctl restart king_bill_django
sudo supervisorctl restart king_bill_celery_worker

# View logs
sudo supervisorctl tail -f king_bill_django
sudo supervisorctl tail -f king_bill_celery_worker

# Start/stop all
sudo supervisorctl start all
sudo supervisorctl stop all
```

### System Services
```bash
# Check system services
sudo systemctl status supervisor nginx redis-server postgresql

# Restart system services
sudo systemctl restart supervisor
sudo systemctl restart nginx
```

## Monitoring and Logs

### Log Locations
```bash
# Supervisor logs
/var/log/supervisor/king_bill_django.log
/var/log/supervisor/king_bill_celery_worker.log
/var/log/supervisor/king_bill_celery_beat.log

# Gunicorn logs
/var/log/gunicorn/access.log
/var/log/gunicorn/error.log

# Nginx logs
/opt/bitnami/nginx/logs/access.log
/opt/bitnami/nginx/logs/error.log
```

### Health Checks
```bash
# Application health
curl http://localhost:8000/health/

# Celery health
celery -A vegetable_bill_app inspect ping

# Redis health
redis-cli ping

# Database health
python manage.py dbshell
```

## Scheduled Tasks

| Task | Schedule | Queue | Description |
|------|----------|-------|-------------|
| Daily Inventory Check | 8:00 AM daily | inventory | Check stock levels, generate POs |
| Sales Forecast | Daily | forecast | Generate sales predictions |
| AI Processing | Every 2 hours | ai | Process forecasts through AI |
| Photo Cleanup | 2:00 AM daily | cleanup | Remove old shop photos |

## Performance Tuning

### Gunicorn Workers
```python
# In gunicorn.conf.py
workers = multiprocessing.cpu_count() * 2 + 1  # Adjust based on resources
worker_class = "sync"
timeout = 30
max_requests = 1000
```

### Celery Concurrency
```bash
# In supervisor config
--concurrency=4  # Adjust based on CPU cores
```

### Database Optimization
```sql
-- PostgreSQL settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
```

## Security Checklist

- [ ] Firewall configured (ports 80, 443, 22 only)
- [ ] Database access restricted to localhost
- [ ] Redis access restricted to localhost
- [ ] Debug mode disabled (`DEBUG=False`)
- [ ] Secret key properly set
- [ ] SSL certificate installed
- [ ] File permissions properly set
- [ ] Regular security updates applied

## Backup Strategy

### Database Backup
```bash
# Daily backup script
pg_dump -h localhost -U king_bill_user king_bill_db > backup_$(date +%Y%m%d).sql
```

### Application Backup
```bash
# Backup application files and media
tar -czf king_bill_backup_$(date +%Y%m%d).tar.gz /opt/bitnami/apps/king-bill/
```

## Troubleshooting Quick Reference

### Common Issues
1. **502 Bad Gateway**: Check if Gunicorn is running (`supervisorctl status`)
2. **Celery tasks not running**: Check Redis connection and worker status
3. **Static files not loading**: Check Nginx configuration and file permissions
4. **Database errors**: Verify connection settings and PostgreSQL status

### Emergency Commands
```bash
# Restart everything
sudo systemctl restart supervisor nginx redis-server

# Check all logs for errors
sudo supervisorctl tail king_bill_django stderr
tail -f /var/log/gunicorn/error.log
tail -f /opt/bitnami/nginx/logs/error.log
```

## Maintenance Schedule

### Daily
- Monitor service status
- Check log files for errors
- Verify scheduled tasks execution

### Weekly
- Review system resources (CPU, memory, disk)
- Check database performance
- Update application if needed

### Monthly
- Clean up old log files
- Database maintenance (vacuum, analyze)
- Security updates
- Backup verification

---

**Document Version**: 1.0  
**Last Updated**: June 17, 2025  
**Compatible With**: Ubuntu 20.04+, Bitnami Stack  
**For detailed instructions**: See [backend/README.md](../../backend/README.md)
