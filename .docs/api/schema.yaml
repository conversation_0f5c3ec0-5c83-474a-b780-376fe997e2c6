openapi: 3.0.3
info:
  title: ''
  version: 0.0.0
paths:
  /api/brand/:
    get:
      operationId: brand_retrieve
      tags:
      - brand
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: brand_create
      tags:
      - brand
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/buyer/:
    get:
      operationId: buyer_retrieve
      tags:
      - buyer
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: buyer_create
      tags:
      - buyer
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: buyer_update
      tags:
      - buyer
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: buyer_destroy
      tags:
      - buyer
      security:
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/buyer_class/:
    get:
      operationId: buyer_class_retrieve
      tags:
      - buyer_class
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: buyer_class_create
      tags:
      - buyer_class
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/buyer_class_margin/:
    get:
      operationId: buyer_class_margin_retrieve
      tags:
      - buyer_class_margin
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: buyer_class_margin_create
      tags:
      - buyer_class_margin
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/expense/:
    get:
      operationId: expense_retrieve
      tags:
      - expense
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: expense_create
      tags:
      - expense
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: expense_update
      tags:
      - expense
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: expense_destroy
      tags:
      - expense
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/expense_category/:
    get:
      operationId: expense_category_retrieve
      tags:
      - expense_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: expense_category_create
      tags:
      - expense_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: expense_category_update
      tags:
      - expense_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: expense_category_destroy
      tags:
      - expense_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/expense_sub_category/:
    get:
      operationId: expense_sub_category_retrieve
      tags:
      - expense_sub_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: expense_sub_category_create
      tags:
      - expense_sub_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: expense_sub_category_update
      tags:
      - expense_sub_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: expense_sub_category_destroy
      tags:
      - expense_sub_category
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/invoice/:
    get:
      operationId: invoice_retrieve
      tags:
      - invoice
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: invoice_create
      tags:
      - invoice
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: invoice_update
      tags:
      - invoice
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: invoice_destroy
      tags:
      - invoice
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/item/:
    get:
      operationId: item_retrieve
      tags:
      - item
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: item_create
      tags:
      - item
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: item_update
      tags:
      - item
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: item_destroy
      tags:
      - item
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/ledger/:
    get:
      operationId: ledger_retrieve
      tags:
      - ledger
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: ledger_create
      tags:
      - ledger
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: ledger_update
      tags:
      - ledger
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/line_account_data/:
    get:
      operationId: line_account_data_retrieve
      tags:
      - line_account_data
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: line_account_data_create
      tags:
      - line_account_data
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: line_account_data_update
      tags:
      - line_account_data
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: line_account_data_destroy
      tags:
      - line_account_data
      security:
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/login/:
    post:
      operationId: login_create
      tags:
      - login
      security:
      - basicAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/metadata/:
    get:
      operationId: metadata_retrieve
      tags:
      - metadata
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: metadata_create
      tags:
      - metadata
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/order/:
    get:
      operationId: order_retrieve
      tags:
      - order
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: order_create
      tags:
      - order
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: order_update
      tags:
      - order
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: order_destroy
      tags:
      - order
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/party/:
    get:
      operationId: party_retrieve
      tags:
      - party
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: party_create
      tags:
      - party
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: party_update
      tags:
      - party
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: party_destroy
      tags:
      - party
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/product/:
    get:
      operationId: product_retrieve
      tags:
      - product
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: product_create
      tags:
      - product
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: product_update
      tags:
      - product
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: product_destroy
      tags:
      - product
      security:
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/purchase_invoice/:
    get:
      operationId: purchase_invoice_retrieve
      tags:
      - purchase_invoice
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: purchase_invoice_create
      tags:
      - purchase_invoice
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: purchase_invoice_update
      tags:
      - purchase_invoice
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: purchase_invoice_destroy
      tags:
      - purchase_invoice
      security:
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/purchase_payment/:
    post:
      operationId: purchase_payment_create
      tags:
      - purchase_payment
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/rate/:
    get:
      operationId: rate_retrieve
      tags:
      - rate
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: rate_create
      tags:
      - rate
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    put:
      operationId: rate_update
      tags:
      - rate
      security:
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
    delete:
      operationId: rate_destroy
      tags:
      - rate
      security:
      - tokenAuth: []
      - {}
      responses:
        '204':
          description: No response body
  /api/report/:
    get:
      operationId: report_retrieve
      tags:
      - report
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/sales_invoice/:
    get:
      operationId: sales_invoice_retrieve
      tags:
      - sales_invoice
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: sales_invoice_create
      tags:
      - sales_invoice
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: sales_invoice_update
      tags:
      - sales_invoice
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: sales_invoice_partial_update
      tags:
      - sales_invoice
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: sales_invoice_destroy
      tags:
      - sales_invoice
      security:
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/sales_invoice_print/:
    get:
      operationId: sales_invoice_print_retrieve
      tags:
      - sales_invoice_print
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/sales_invoice_report/:
    get:
      operationId: sales_invoice_report_retrieve
      tags:
      - sales_invoice_report
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/sales_order/:
    get:
      operationId: sales_order_retrieve
      tags:
      - sales_order
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: sales_order_create
      tags:
      - sales_order
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: sales_order_update
      tags:
      - sales_order
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: sales_order_destroy
      tags:
      - sales_order
      security:
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/sales_payment/:
    post:
      operationId: sales_payment_create
      tags:
      - sales_payment
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/schema/:
    get:
      operationId: schema_retrieve
      description: |-
        OpenApi3 schema for this API. Format can be selected via content negotiation.

        - YAML: application/vnd.oai.openapi
        - JSON: application/vnd.oai.openapi+json
      parameters:
      - in: query
        name: format
        schema:
          type: string
          enum:
          - json
          - yaml
      - in: query
        name: lang
        schema:
          type: string
          enum:
          - af
          - ar
          - ar-dz
          - ast
          - az
          - be
          - bg
          - bn
          - br
          - bs
          - ca
          - ckb
          - cs
          - cy
          - da
          - de
          - dsb
          - el
          - en
          - en-au
          - en-gb
          - eo
          - es
          - es-ar
          - es-co
          - es-mx
          - es-ni
          - es-ve
          - et
          - eu
          - fa
          - fi
          - fr
          - fy
          - ga
          - gd
          - gl
          - he
          - hi
          - hr
          - hsb
          - hu
          - hy
          - ia
          - id
          - ig
          - io
          - is
          - it
          - ja
          - ka
          - kab
          - kk
          - km
          - kn
          - ko
          - ky
          - lb
          - lt
          - lv
          - mk
          - ml
          - mn
          - mr
          - ms
          - my
          - nb
          - ne
          - nl
          - nn
          - os
          - pa
          - pl
          - pt
          - pt-br
          - ro
          - ru
          - sk
          - sl
          - sq
          - sr
          - sr-latn
          - sv
          - sw
          - ta
          - te
          - tg
          - th
          - tk
          - tr
          - tt
          - udm
          - uk
          - ur
          - uz
          - vi
          - zh-hans
          - zh-hant
      tags:
      - schema
      security:
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/vnd.oai.openapi:
              schema:
                type: object
                additionalProperties: {}
            application/yaml:
              schema:
                type: object
                additionalProperties: {}
            application/vnd.oai.openapi+json:
              schema:
                type: object
                additionalProperties: {}
            application/json:
              schema:
                type: object
                additionalProperties: {}
          description: ''
  /api/suplier/:
    get:
      operationId: suplier_retrieve
      tags:
      - suplier
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: suplier_create
      tags:
      - suplier
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: suplier_update
      tags:
      - suplier
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: suplier_destroy
      tags:
      - suplier
      security:
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/tally/:
    get:
      operationId: tally_retrieve
      tags:
      - tally
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    tokenAuth:
      type: apiKey
      in: header
      name: Authorization
      description: Token-based authentication with required prefix "Token"
