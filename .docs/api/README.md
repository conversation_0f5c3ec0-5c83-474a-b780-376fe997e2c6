# API Documentation

This directory contains all API-related documentation for the King Bill application.

## 📋 Available Documentation

- **[API Schema](schema.yaml)** - Complete OpenAPI/Swagger schema for all API endpoints

## 🔌 API Overview

The King Bill API is built with Django REST Framework, providing:

- **RESTful Endpoints** - Standard REST API design
- **JWT Authentication** - Secure token-based authentication
- **Comprehensive Models** - Full CRUD operations for all entities
- **File Upload Support** - Media file handling
- **Translation Support** - Multi-language API responses
- **Filtering & Search** - Advanced query capabilities

## 🚀 Quick Start

1. **Authentication**: All endpoints require JWT authentication
2. **Base URL**: `https://your-domain.com/api/`
3. **Content-Type**: `application/json`
4. **File Uploads**: Use `multipart/form-data` for file uploads

## 📚 API Categories

### Authentication
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `POST /api/auth/refresh/` - Refresh token

### Core Entities
- **Products** - Product management and inventory
- **Suppliers** - Supplier information and relationships
- **Buyers** - Customer management
- **Invoices** - Sales invoice creation and management
- **Purchase Orders** - Purchase order workflow
- **Payments** - Payment processing and tracking

### Business Logic
- **Brands** - Brand management and filtering
- **Categories** - Product categorization
- **Reports** - Business analytics and reporting
- **Settings** - Application configuration

## 🔗 Related Documentation

- **[Backend Documentation](../backend/)** - Backend setup and development
- **[Schema Reference](schema.yaml)** - Complete API specification

## 🛠️ Development

- **Testing**: Use the schema for API testing tools
- **Documentation**: Auto-generated from Django REST Framework
- **Versioning**: API versioning through URL prefixes
- **Rate Limiting**: Implemented for production stability 