# Development Documentation

This directory contains all development-related documentation for the King Bill application.

## 📋 Available Documentation

- **[Bluetooth Permissions](bluetooth-permissions.md)** - Setting up Bluetooth permissions for mobile apps

## 🛠️ Development Overview

The King Bill development environment supports:

- **Cross-Platform Development** - Web, iOS, and Android
- **Modern Toolchain** - Latest development tools and frameworks
- **Mobile-First Design** - Responsive and mobile-optimized
- **Permission Management** - Platform-specific permissions
- **Development Workflow** - Efficient development processes

## 🔧 Development Stack

### Frontend
- **Ionic Angular** - Cross-platform mobile framework
- **TypeScript** - Type-safe JavaScript development
- **SCSS** - Advanced CSS preprocessing
- **Capacitor** - Native mobile capabilities

### Backend
- **Django** - Python web framework
- **Django REST Framework** - API development
- **PostgreSQL** - Database management
- **Redis** - Caching and task queue
- **Celery** - Background task processing

### Development Tools
- **Git** - Version control
- **Docker** - Containerization (optional)
- **VS Code** - Recommended IDE
- **Postman** - API testing

## 🚀 Quick Links

- [Bluetooth Setup](bluetooth-permissions.md#setup)
- [Mobile Permissions](bluetooth-permissions.md#permissions)
- [Development Environment](bluetooth-permissions.md#environment)

## 🔗 Related Documentation

- **[Backend Documentation](../backend/)** - Backend development setup
- **[Frontend Documentation](../frontend/)** - Frontend development setup
- **[API Documentation](../api/)** - API development and testing
- **[Printing Documentation](../printing/)** - Printing development

## 🛠️ Development Workflow

### Setup
1. Clone the repository
2. Install dependencies (backend and frontend)
3. Configure environment variables
4. Set up database and Redis
5. Run migrations

### Development
1. Start backend server
2. Start frontend development server
3. Configure mobile permissions
4. Test on multiple platforms

### Testing
- Unit tests for both frontend and backend
- Integration tests for API endpoints
- E2E tests for critical user flows
- Mobile device testing

### Deployment
- Backend deployment to production server
- Frontend build and deployment
- Mobile app builds for app stores
- Environment-specific configurations 